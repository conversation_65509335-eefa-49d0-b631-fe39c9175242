<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advertisements', function (Blueprint $table) {
            $table->id();
            $table->foreignId(column: 'user_id')->constrained()->onDelete('cascade');
            $table->text('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->foreignId('main_category_id')->constrained('categories')->onDelete('cascade');
            $table->foreignId('sub_category_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->enum('gender_target', ['male', 'female', 'both']);
            $table->foreignId('region_id')->constrained('regions')->onDelete('cascade');
            $table->foreignId('city_id')->constrained('cities')->onDelete('cascade');
            $table->enum('status', [
                'under_review',           
                'active',                 
                'rejected',               
                'hidden',                 
                'cancelled',              
                'awaiting_sale_confirm',  
                'awaiting_purchase_confirm', 
                'sold',                  
            ])->default('under_review');
            $table->string('whatsapp_contact')->nullable();
            $table->boolean('is_main')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advertisements');
    }
};
