<div class="position-relative">
    {{-- table content --}}
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>{{ __('admin.id_num') }}</th>
                <th>{{ __('admin.personal_image') }}</th>
                <th>{{ __('admin.name') }}</th>
                {{-- <th>{{ __('admin.is_active') }}</th> --}}
                {{-- <th>{{ __('admin.control') }}</th> --}}
            </tr>
        </thead>
        <tbody>
            @foreach($rows as $trainer)
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="{{ $trainer->id }}">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $trainer->id }}</td>
                    <td>
                        <img src="{{ $trainer->getFirstMediaUrl('personal_image') }}" width="50px" height="50px" alt="">
                    </td>
                    <td>{{ $trainer->name }}</td>
                    {{-- <td>
                        {!! toggleBooleanView($ad , route('admin.model.active' , ['model' =>'ad' , 'id' => $ad->id , 'action' => 'is_active'])) !!}
                    </td> --}}
                    <td class="product-action">
                        <span class="text-primary">
                            <a href="{{ route('admin.trainers.show', ['id' => $trainer->id]) }}" class="btn btn-warning btn-sm">
                                <i class="feather icon-eye"></i> {{ __('admin.show') }}
                            </a>
                        </span>
                        <span class="action-edit text-primary">
                            <a href="{{ route('admin.trainers.edit', ['id' => $trainer->id]) }}" class="btn btn-primary btn-sm">
                                <i class="feather icon-edit"></i> {{ __('admin.edit') }}
                            </a>
                        </span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="{{ url('admin/trainers/' . $trainer->id) }}">
                            <i class="feather icon-trash"></i> {{ __('admin.delete') }}
                        </span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}

    {{-- no data found div --}}
    @if ($rows->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{ asset('admin/app-assets/images/pages/404.png') }}" alt="">
            <span class="mt-2" style="font-family: cairo">{{ __('admin.there_are_no_matches_matching') }}</span>
        </div>
    @endif
    {{-- no data found div --}}
</div>

{{-- pagination  links div --}}
@if ($rows->count() > 0 && $rows instanceof \Illuminate\Pagination\AbstractPaginator)
    <div class="d-flex justify-content-center mt-3">
        {{ $rows->links() }}
    </div>
@endif
{{-- pagination  links div --}}
