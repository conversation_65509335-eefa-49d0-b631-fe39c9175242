<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Api\Client\ProductResource;
use App\Http\Resources\Api\Client\ServiceResource;

class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'total' => $this->total,
            'provider_name' => $this->provider ? $this->provider->commercial_name : null,
            'is_product' => $this->isProduct(),
            'is_service' => $this->isService(),

            // Product details (if product)
            'product' => $this->when($this->isProduct() && $this->relationLoaded('item'), function() {
                $productResource = new ProductResource($this->item);
                return $productResource;
            }),

            // Service details (if service)
            'service' => $this->when($this->isService() && $this->relationLoaded('item'), function() {
                $serviceResource = new ServiceResource($this->item);
                return $serviceResource;
            }),
        ];
    }
}
