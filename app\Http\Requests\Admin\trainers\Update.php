<?php

namespace App\Http\Requests\Admin\trainers;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class Update extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $trainerId = $this->route('id');

        $rules = [
            'user_id'           => ['nullable', 'exists:users,id'],
            'region_id'         => ['nullable', 'exists:regions,id'],
            'city_id'           => ['nullable', 'exists:cities,id'],
            'training_price'    => ['nullable', 'numeric', 'min:0'],


            'status' => ['nullable', Rule::in(['pending', 'approved', 'rejected'])],


            'contact_phone'     => [
                'nullable', 'string', 'max:20',
                Rule::unique('trainer_applications', 'contact_phone')->ignore($trainerId)
            ],

            'contact_email'     => [
                'nullable', 'email',
                Rule::unique('trainer_applications', 'contact_email')->ignore($trainerId)
            ],

            'contact_whatsapp'  => [
                'nullable', 'string', 'max:20', 'regex:/^[0-9]+$/',
                Rule::unique('trainer_applications', 'contact_whatsapp')->ignore($trainerId)
            ],

            'personal_image'    => ['nullable', 'image', 'mimes:jpg,jpeg,png', 'max:5120'],
            'certificate_image' => ['nullable', 'image', 'mimes:jpg,jpeg,png', 'max:5120'],
            'works.*'           => ['nullable', 'image', 'mimes:jpg,jpeg,png', 'max:5120'],
        ];

        foreach (languages() as $lang) {
            $rules["name.$lang"] = ['nullable', 'string', 'min:6', 'max:255'];
            $rules["bio.$lang"] = ['nullable', 'string', 'min:10'];
            $rules["experience.$lang"] = ['nullable', 'string', 'min:10'];
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'user_id.required' => __('admin.this_field_is_required'),
            'user_id.exists'   => __('admin.user_not_found'),

            'region_id.required' => __('admin.this_field_is_required'),
            'region_id.exists'   => __('admin.region_not_found'),

            'city_id.required' => __('admin.this_field_is_required'),
            'city_id.exists'   => __('admin.city_not_found'),

            'training_price.required' => __('admin.this_field_is_required'),
            'training_price.numeric'  => __('admin.must_be_number'),

            'contact_phone.required' => __('admin.this_field_is_required'),
            'contact_phone.unique'   => __('admin.already_used'),

            'contact_email.email'    => __('admin.invalid_email'),
            'contact_email.unique'   => __('admin.already_used'),

            'contact_whatsapp.unique' => __('admin.already_used'),

            'name.ar.required'       => __('admin.this_field_is_required'),
            'name.ar.min'            => __('admin.must_be_at_least_5_characters'),

            'bio.ar.required'        => __('admin.this_field_is_required'),
            'bio.ar.min'             => __('admin.must_be_at_least_10_characters'),

            'experience.ar.required' => __('admin.this_field_is_required'),
            'experience.ar.min'      => __('admin.must_be_at_least_10_characters'),

            'personal_image.max'     => __('admin.image_must_not_exceed_5mb'),
            'certificate_image.max'  => __('admin.image_must_not_exceed_5mb'),
            'works.*.max'            => __('admin.image_must_not_exceed_5mb'),
        ];
    }
}
