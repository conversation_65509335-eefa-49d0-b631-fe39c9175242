# Migration Dependency Fixes

## Overview
Fixed foreign key dependency issues in course-related migrations to ensure proper table creation order and avoid "Failed to open the referenced table" errors.

## Issues Identified

### **1. Course Stage Completions Foreign Key Issue**

#### **Problem**
```php
// In 2024_01_15_000001_create_course_stage_completions_table.php
$table->foreignId('stage_id')->constrained('course_stages')->onDelete('cascade');
```
- ❌ **Issue**: `course_stage_completions` table created on `2024_01_15`
- ❌ **Issue**: `course_stages` table created on `2025_01_20` 
- ❌ **Result**: Foreign key references table that doesn't exist yet

#### **Solution**
```php
// Step 1: Create table without foreign key constraint
// In 2024_01_15_000001_create_course_stage_completions_table.php
$table->unsignedBigInteger('stage_id');

// Step 2: Add foreign key constraint after referenced table exists
// In 2025_01_20_120002_add_foreign_keys_to_course_stage_completions.php
$table->foreign('stage_id')->references('id')->on('course_stages')->onDelete('cascade');
```

### **2. Course Enrollments Bank Account Foreign Key Issue**

#### **Problem**
```php
// In 2024_01_14_130000_create_course_enrollments_table.php
$table->foreignId('bank_account_id')->nullable()->constrained('provider_bank_accounts')->onDelete('set null');
```
- ❌ **Issue**: `course_enrollments` table created on `2024_01_14`
- ❌ **Issue**: `provider_bank_accounts` table created on `2025_05_25`
- ❌ **Result**: Foreign key references table that doesn't exist yet

#### **Solution**
```php
// Step 1: Create table without foreign key constraint
// In 2024_01_14_130000_create_course_enrollments_table.php
$table->unsignedBigInteger('bank_account_id')->nullable();

// Step 2: Add foreign key constraint after referenced table exists
// In 2025_05_25_212260_add_foreign_keys_to_course_enrollments.php
$table->foreign('bank_account_id')->references('id')->on('provider_bank_accounts')->onDelete('set null');
```

## Migration Files Modified

### **1. Course Stage Completions Table**

#### **Modified File**: `2024_01_15_000001_create_course_stage_completions_table.php`
```php
Schema::create('course_stage_completions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('enrollment_id')->constrained('course_enrollments')->onDelete('cascade');
    $table->unsignedBigInteger('stage_id'); // Changed: No foreign key constraint yet
    $table->timestamp('completed_at')->nullable()->comment('When the stage was completed');
    $table->integer('time_spent')->nullable()->comment('Time spent on this stage in seconds');
    $table->integer('last_watch_time')->default(0)->comment('Last minute watched in the video (in seconds)');
    $table->text('notes')->nullable()->comment('Optional notes about completion');
    $table->timestamps();
    
    // Ensure each stage can only be completed once per enrollment
    $table->unique(['enrollment_id', 'stage_id'], 'enrollment_stage_unique');
    
    // Indexes for better performance
    $table->index(['enrollment_id', 'completed_at']);
    $table->index(['enrollment_id', 'last_watch_time']);
});
```

#### **New File**: `2025_01_20_120002_add_foreign_keys_to_course_stage_completions.php`
```php
Schema::table('course_stage_completions', function (Blueprint $table) {
    // Add foreign key constraint to stage_id now that course_stages table exists
    $table->foreign('stage_id')->references('id')->on('course_stages')->onDelete('cascade');
    
    // Add index for better performance
    $table->index(['stage_id', 'completed_at']);
});
```

### **2. Course Enrollments Table**

#### **Modified File**: `2024_01_14_130000_create_course_enrollments_table.php`
```php
Schema::create('course_enrollments', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->foreignId('course_id')->constrained()->onDelete('cascade');
    $table->timestamp('enrolled_at');
    $table->enum('status', ['pending_payment', 'active', 'suspended', 'completed', 'cancelled'])->default('pending_payment');
    $table->decimal('progress_percentage', 5, 2)->default(0);
    $table->timestamp('completed_at')->nullable();

    // Payment information
    $table->enum('payment_method', ['wallet', 'bank_transfer', 'credit_card', 'mada', 'apple_pay']);
    $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
    $table->decimal('amount_paid', 10, 2);
    $table->string('payment_reference')->nullable();
    $table->unsignedBigInteger('bank_account_id')->nullable(); // Changed: No foreign key constraint yet
    $table->timestamp('payment_completed_at')->nullable();

    $table->timestamps();

    // Ensure user can only enroll once per course
    $table->unique(['user_id', 'course_id']);

    // Add indexes for better performance
    $table->index(['user_id', 'status']);
    $table->index(['course_id', 'status']);
    $table->index(['payment_status']);
});
```

#### **New File**: `2025_05_25_212260_add_foreign_keys_to_course_enrollments.php`
```php
Schema::table('course_enrollments', function (Blueprint $table) {
    // Add foreign key constraint to bank_account_id now that provider_bank_accounts table exists
    $table->foreign('bank_account_id')->references('id')->on('provider_bank_accounts')->onDelete('set null');
    
    // Add index for better performance
    $table->index(['bank_account_id']);
});
```

## Migration Execution Order

### **Correct Order After Fixes**
1. ✅ `2024_01_13_120000_create_courses_table.php`
2. ✅ `2024_01_14_130000_create_course_enrollments_table.php` (without bank_account_id FK)
3. ✅ `2024_01_15_000001_create_course_stage_completions_table.php` (without stage_id FK)
4. ✅ `2024_01_15_000002_add_progress_fields_to_course_enrollments_table.php`
5. ✅ `2025_01_20_120001_create_course_stages_table.php`
6. ✅ `2025_01_20_120002_add_foreign_keys_to_course_stage_completions.php` (adds stage_id FK)
7. ✅ `2025_05_25_212259_create_provider_bank_accounts_table.php`
8. ✅ `2025_05_25_212260_add_foreign_keys_to_course_enrollments.php` (adds bank_account_id FK)

## Benefits of This Approach

### **1. Dependency Resolution**
- ✅ **No Circular Dependencies**: Tables created before their foreign keys
- ✅ **Clean Migration**: Each migration can run independently
- ✅ **Error Prevention**: Eliminates "Failed to open the referenced table" errors

### **2. Maintainability**
- ✅ **Clear Structure**: Separate migrations for foreign key additions
- ✅ **Rollback Safety**: Foreign keys can be dropped independently
- ✅ **Documentation**: Clear history of when constraints were added

### **3. Development Workflow**
- ✅ **Fresh Migrations**: `php artisan migrate:fresh` works without errors
- ✅ **Incremental Updates**: New environments can migrate cleanly
- ✅ **Testing**: Database tests can run without dependency issues

## Running Migrations

### **Fresh Migration (Recommended)**
```bash
# Drop all tables and re-run all migrations
php artisan migrate:fresh

# With seeding
php artisan migrate:fresh --seed
```

### **Regular Migration**
```bash
# Run pending migrations
php artisan migrate

# Check migration status
php artisan migrate:status
```

### **Rollback if Needed**
```bash
# Rollback last batch
php artisan migrate:rollback

# Rollback specific migration
php artisan migrate:rollback --step=1
```

## Verification

### **Check Foreign Key Constraints**
```sql
-- Check course_stage_completions foreign keys
SHOW CREATE TABLE course_stage_completions;

-- Check course_enrollments foreign keys  
SHOW CREATE TABLE course_enrollments;

-- Verify all tables exist
SHOW TABLES LIKE 'course%';
```

### **Test Data Integrity**
```php
// Test that foreign key constraints work
$enrollment = CourseEnrollment::first();
$stage = CourseStage::first();

// This should work
CourseStageCompletion::create([
    'enrollment_id' => $enrollment->id,
    'stage_id' => $stage->id,
    'last_watch_time' => 0
]);

// This should fail (foreign key constraint)
CourseStageCompletion::create([
    'enrollment_id' => 999999, // Non-existent enrollment
    'stage_id' => $stage->id,
    'last_watch_time' => 0
]);
```

The migration dependency fixes ensure that all course-related tables can be created in the correct order without foreign key constraint errors! 🗄️✅🔧
