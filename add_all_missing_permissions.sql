-- Add All Missing Permissions for Admin Role (role_id = 1)
-- This script adds permissions for the reorganized route structure

-- Products Management Group
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.products.management', NOW(), NOW()),
(1, 'admin.products.index', NOW(), NOW()),
(1, 'admin.products.create', NOW(), NOW()),
(1, 'admin.products.store', NOW(), NOW()),
(1, 'admin.products.edit', NOW(), NOW()),
(1, 'admin.products.update', NOW(), NOW()),
(1, 'admin.products.show', NOW(), NOW()),
(1, 'admin.products.delete', NOW(), NOW()),
(1, 'admin.products.deleteAll', NOW(), NOW()),
(1, 'admin.products.toggleStatus', NOW(), NOW()),
(1, 'admin.product-categories.index', NOW(), NOW()),
(1, 'admin.product-categories.create', NOW(), NOW()),
(1, 'admin.product-categories.store', NOW(), NOW()),
(1, 'admin.product-categories.edit', NOW(), NOW()),
(1, 'admin.product-categories.update', NOW(), NOW()),
(1, 'admin.product-categories.show', NOW(), NOW()),
(1, 'admin.product-categories.delete', NOW(), NOW()),
(1, 'admin.product-categories.deleteAll', NOW(), NOW());

-- Services Management Group
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.services.management', NOW(), NOW()),
(1, 'admin.services.index', NOW(), NOW()),
(1, 'admin.services.create', NOW(), NOW()),
(1, 'admin.services.store', NOW(), NOW()),
(1, 'admin.services.edit', NOW(), NOW()),
(1, 'admin.services.update', NOW(), NOW()),
(1, 'admin.services.show', NOW(), NOW()),
(1, 'admin.services.delete', NOW(), NOW()),
(1, 'admin.services.deleteAll', NOW(), NOW()),
(1, 'admin.services.toggleStatus', NOW(), NOW()),
(1, 'admin.categories.index', NOW(), NOW()),
(1, 'admin.categories.export', NOW(), NOW()),
(1, 'admin.categories.create', NOW(), NOW()),
(1, 'admin.categories.store', NOW(), NOW()),
(1, 'admin.categories.edit', NOW(), NOW()),
(1, 'admin.categories.update', NOW(), NOW()),
(1, 'admin.categories.show', NOW(), NOW()),
(1, 'admin.categories.delete', NOW(), NOW()),
(1, 'admin.categories.deleteAll', NOW(), NOW());

-- Courses Management Group
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.courses.management', NOW(), NOW()),
(1, 'admin.courses.index', NOW(), NOW()),
(1, 'admin.courses.create', NOW(), NOW()),
(1, 'admin.courses.store', NOW(), NOW()),
(1, 'admin.courses.edit', NOW(), NOW()),
(1, 'admin.courses.update', NOW(), NOW()),
(1, 'admin.courses.show', NOW(), NOW()),
(1, 'admin.courses.delete', NOW(), NOW()),
(1, 'admin.courses.deleteAll', NOW(), NOW()),
(1, 'admin.courses.toggleStatus', NOW(), NOW()),
(1, 'admin.course_enrollments.index', NOW(), NOW()),
(1, 'admin.course_enrollments.show', NOW(), NOW()),
(1, 'admin.course_enrollments.destroy', NOW(), NOW()),
(1, 'admin.course_enrollments.deleteAll', NOW(), NOW());

-- Blogs Management Group
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.blogs.management', NOW(), NOW()),
(1, 'admin.blogs.index', NOW(), NOW()),
(1, 'admin.blogs.create', NOW(), NOW()),
(1, 'admin.blogs.store', NOW(), NOW()),
(1, 'admin.blogs.edit', NOW(), NOW()),
(1, 'admin.blogs.update', NOW(), NOW()),
(1, 'admin.blogs.show', NOW(), NOW()),
(1, 'admin.blogs.delete', NOW(), NOW()),
(1, 'admin.blogs.deleteAll', NOW(), NOW()),
(1, 'admin.blogs.comments.load-more', NOW(), NOW()),
(1, 'admin.blogs.comments.toggle-approval', NOW(), NOW()),
(1, 'admin.blogcategories.index', NOW(), NOW()),
(1, 'admin.blogcategories.create', NOW(), NOW()),
(1, 'admin.blogcategories.store', NOW(), NOW()),
(1, 'admin.blogcategories.edit', NOW(), NOW()),
(1, 'admin.blogcategories.update', NOW(), NOW()),
(1, 'admin.blogcategories.show', NOW(), NOW()),
(1, 'admin.blogcategories.delete', NOW(), NOW()),
(1, 'admin.blogcategories.deleteAll', NOW(), NOW());

-- Additional missing permissions for existing routes
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.paymentmethods.index', NOW(), NOW()),
(1, 'admin.paymentmethods.show', NOW(), NOW()),
(1, 'admin.paymentmethods.delete', NOW(), NOW()),
(1, 'admin.paymentmethods.deleteAll', NOW(), NOW());

-- Marketing permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.marketing', NOW(), NOW()),
(1, 'admin.notifications.index', NOW(), NOW()),
(1, 'admin.notifications.send', NOW(), NOW()),
(1, 'admin.coupons.index', NOW(), NOW()),
(1, 'admin.coupons.show', NOW(), NOW()),
(1, 'admin.coupons.create', NOW(), NOW()),
(1, 'admin.coupons.store', NOW(), NOW()),
(1, 'admin.coupons.edit', NOW(), NOW()),
(1, 'admin.coupons.update', NOW(), NOW()),
(1, 'admin.coupons.delete', NOW(), NOW()),
(1, 'admin.coupons.deleteAll', NOW(), NOW()),
(1, 'admin.coupons.renew', NOW(), NOW()),
(1, 'admin.images.index', NOW(), NOW()),
(1, 'admin.images.show', NOW(), NOW()),
(1, 'admin.images.create', NOW(), NOW()),
(1, 'admin.images.store', NOW(), NOW()),
(1, 'admin.images.edit', NOW(), NOW()),
(1, 'admin.images.update', NOW(), NOW()),
(1, 'admin.images.delete', NOW(), NOW()),
(1, 'admin.images.deleteAll', NOW(), NOW()),
(1, 'admin.socials.index', NOW(), NOW()),
(1, 'admin.socials.show', NOW(), NOW()),
(1, 'admin.socials.create', NOW(), NOW()),
(1, 'admin.socials.store', NOW(), NOW()),
(1, 'admin.socials.update', NOW(), NOW()),
(1, 'admin.socials.edit', NOW(), NOW()),
(1, 'admin.socials.delete', NOW(), NOW()),
(1, 'admin.socials.deleteAll', NOW(), NOW()),
(1, 'admin.intros.index', NOW(), NOW()),
(1, 'admin.intros.show', NOW(), NOW()),
(1, 'admin.intros.create', NOW(), NOW()),
(1, 'admin.intros.store', NOW(), NOW()),
(1, 'admin.intros.edit', NOW(), NOW()),
(1, 'admin.intros.update', NOW(), NOW()),
(1, 'admin.intros.delete', NOW(), NOW()),
(1, 'admin.intros.deleteAll', NOW(), NOW()),
(1, 'admin.statistics.index', NOW(), NOW());

-- Countries & Cities permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.countries_cities', NOW(), NOW()),
(1, 'admin.countries.index', NOW(), NOW()),
(1, 'admin.countries.show', NOW(), NOW()),
(1, 'admin.countries.create', NOW(), NOW()),
(1, 'admin.countries.store', NOW(), NOW()),
(1, 'admin.countries.edit', NOW(), NOW()),
(1, 'admin.countries.update', NOW(), NOW()),
(1, 'admin.countries.delete', NOW(), NOW()),
(1, 'admin.countries.deleteAll', NOW(), NOW()),
(1, 'admin.regions.index', NOW(), NOW()),
(1, 'admin.regions.create', NOW(), NOW()),
(1, 'admin.regions.store', NOW(), NOW()),
(1, 'admin.regions.edit', NOW(), NOW()),
(1, 'admin.regions.update', NOW(), NOW()),
(1, 'admin.regions.show', NOW(), NOW()),
(1, 'admin.regions.delete', NOW(), NOW()),
(1, 'admin.regions.deleteAll', NOW(), NOW()),
(1, 'admin.cities.index', NOW(), NOW()),
(1, 'admin.cities.create', NOW(), NOW()),
(1, 'admin.cities.store', NOW(), NOW()),
(1, 'admin.cities.edit', NOW(), NOW()),
(1, 'admin.cities.show', NOW(), NOW()),
(1, 'admin.cities.update', NOW(), NOW()),
(1, 'admin.cities.delete', NOW(), NOW()),
(1, 'admin.cities.deleteAll', NOW(), NOW());

-- Orders Management permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.orders.index', NOW(), NOW()),
(1, 'admin.orders.show', NOW(), NOW()),
(1, 'admin.orders.updateStatus', NOW(), NOW()),
(1, 'admin.orders.markPaymentAsPaid', NOW(), NOW()),
(1, 'admin.orders.updateProviderStatus', NOW(), NOW()),
(1, 'admin.bank_transfer_orders.index', NOW(), NOW()),
(1, 'admin.bank_transfer_orders.show', NOW(), NOW()),
(1, 'admin.bank_transfer_orders.verifyTransfer', NOW(), NOW()),
(1, 'admin.bank_transfer_orders.rejectTransfer', NOW(), NOW()),
(1, 'admin.cancel_request_orders.index', NOW(), NOW()),
(1, 'admin.cancel_request_orders.show', NOW(), NOW()),
(1, 'admin.cancel_request_orders.accept', NOW(), NOW()),
(1, 'admin.cancel_request_orders.reject', NOW(), NOW());

-- Settings permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.settings.index', NOW(), NOW()),
(1, 'admin.settings.update', NOW(), NOW()),
(1, 'admin.settings.message.all', NOW(), NOW()),
(1, 'admin.settings.message.one', NOW(), NOW()),
(1, 'admin.settings.send_email', NOW(), NOW());

-- SEO permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.seos.index', NOW(), NOW()),
(1, 'admin.seos.show', NOW(), NOW()),
(1, 'admin.seos.create', NOW(), NOW()),
(1, 'admin.seos.edit', NOW(), NOW()),
(1, 'admin.seos.store', NOW(), NOW()),
(1, 'admin.seos.update', NOW(), NOW()),
(1, 'admin.seos.delete', NOW(), NOW()),
(1, 'admin.seos.deleteAll', NOW(), NOW());

-- Reports permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.reports.index', NOW(), NOW()),
(1, 'admin.reports.show', NOW(), NOW()),
(1, 'admin.reports.delete', NOW(), NOW()),
(1, 'admin.reports.deleteAll', NOW(), NOW());

-- Common Questions permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.questions.index', NOW(), NOW()),
(1, 'admin.questions.create', NOW(), NOW()),
(1, 'admin.questions.store', NOW(), NOW()),
(1, 'admin.questions.edit', NOW(), NOW()),
(1, 'admin.questions.update', NOW(), NOW()),
(1, 'admin.questions.show', NOW(), NOW()),
(1, 'admin.questions.delete', NOW(), NOW()),
(1, 'admin.questions.deleteAll', NOW(), NOW());

-- Complaints permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.complaints.index', NOW(), NOW()),
(1, 'admin.complaints.show', NOW(), NOW()),
(1, 'admin.complaints.replay', NOW(), NOW()),
(1, 'admin.complaints.delete', NOW(), NOW()),
(1, 'admin.complaints.deleteAll', NOW(), NOW());

-- Messages permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.messages.index', NOW(), NOW()),
(1, 'admin.messages.show', NOW(), NOW()),
(1, 'admin.messages.delete', NOW(), NOW()),
(1, 'admin.messages.deleteAll', NOW(), NOW());

-- Excel/Import permissions
INSERT INTO permissions (role_id, permission, created_at, updated_at) VALUES
(1, 'admin.master-export', NOW(), NOW()),
(1, 'admin.import-items', NOW(), NOW()),
(1, 'admin.model.active', NOW(), NOW());
