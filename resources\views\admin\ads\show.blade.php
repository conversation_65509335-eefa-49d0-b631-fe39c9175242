@extends('admin.layout.master')

@section('content')
<form class="store form-horizontal">
    <section id="multiple-column-form">
        <div class="row">

            <div class="col-md-3">
                <div class="col-12 card card-body">
                    <div class="imgMontg col-12 text-center">
                        <div class="dropBox">
                            <div class="textCenter">
                                <div class="imagesUploadBlock">
                                    <label class="uploadImg">
                                        <span><i class="feather icon-image"></i></span>
                                        <input type="file" accept="image/*" name="image" class="imageUploader" disabled>
                                    </label>
                                    <div class="uploadedBlock">
                                        <img src="{{ $ad->getFirstMediaUrl('main_image') }}" alt="{{ $ad->name }}" style="max-height: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- الصور الإضافية --}}
            {{-- @if($ad->getMedia('ads')->count())
                @foreach($ad->getMedia('ads') as $media)
                    <div class="col-md-3">
                        <div class="col-12 card card-body">
                            <div class="imgMontg col-12 text-center">
                                <div class="dropBox">
                                    <div class="textCenter">
                                        <div class="imagesUploadBlock">
                                            <label class="uploadImg">
                                                <span><i class="feather icon-image"></i></span>
                                                <input type="file" accept="image/*" class="imageUploader" disabled>
                                            </label>
                                            <div class="uploadedBlock">
                                                <img src="{{ $media->getUrl() }}" alt="additional_image" style="max-height: 200px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif --}}

            <div class="col-md-9">
                <div class="card">
                    <div class="card-content">
                        <div class="card-body">
                            <div class="form-body">
                                <div class="row">

                                    {{-- Tabs --}}
                                    <div class="col-12">
                                        <ul class="nav nav-tabs mb-3">
                                            @foreach (languages() as $lang)
                                            <li class="nav-item">
                                                <a class="nav-link @if($loop->first) active @endif" data-toggle="pill" href="#tab_{{$lang}}">
                                                    {{ __('admin.data') }} {{ $lang }}
                                                </a>
                                            </li>
                                            @endforeach
                                        </ul>
                                    </div>

                                    {{-- Tab content --}}
                                    <div class="col-12">
                                        <div class="tab-content">
                                            @foreach (languages() as $lang)
                                            <div class="tab-pane fade @if($loop->first) show active @endif" id="tab_{{$lang}}">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>{{ __('admin.name') }} ({{ $lang }})</label>
                                                            <input type="text" value="{{ $ad->getTranslation('name', $lang) }}" class="form-control" disabled>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>{{ __('admin.description') }} ({{ $lang }})</label>
                                                            <textarea class="form-control" disabled>{{ $ad->getTranslation('description', $lang) }}</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.user') }}</label>
                                            <input type="text" class="form-control" value="{{ optional($ad->user)->name }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.price') }}</label>
                                            <input type="text" class="form-control" value="{{ $ad->price }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.main_category') }}</label>
                                            <input type="text" class="form-control" value="{{ optional($ad->main_category)->name }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.sub_category') }}</label>
                                            <input type="text" class="form-control" value="{{ optional($ad->sub_category)->name }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.whatsapp_contact') }}</label>
                                            <input type="text" class="form-control" value="{{ $ad->whatsapp_contact }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.city') }}</label>
                                            <input type="text" class="form-control" value="{{ optional($ad->city)->name }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.region') }}</label>
                                            <input type="text" class="form-control" value="{{ optional($ad->region)->name }}" disabled>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('admin.gender') }}</label>
                                            <input type="text" class="form-control" value="@if($ad->gender_target == 'male') {{ __('admin.male') }} @elseif($ad->gender_target == 'female') {{ __('admin.female') }} @elseif($ad->gender_target == 'both') {{ __('admin.both') }} @else - @endif" disabled>
                                        </div>
                                    </div>

<div class="col-md-6">
    <div class="form-group">
        <label>{{ __('admin.status') }}</label>

        @php
            $statusLabels = [
                'under_review' => __('admin.under_review'),
                'active' => __('admin.active'),
                'rejected' => __('admin.rejected'),
                'hidden' => __('admin.hidden'),
                'cancelled' => __('admin.cancelled'),
                'awaiting_sale_confirm' => __('admin.awaiting_sale_confirm'),
                'awaiting_purchase_confirm' => __('admin.awaiting_purchase_confirm'),
                'sold' => __('admin.sold'),
            ];

            $statusLabel = $statusLabels[$ad->status] ?? '-';
        @endphp

        <input type="text" class="form-control" value="{{ $statusLabel }}" disabled>
    </div>
</div>
   

@if($ad->getMedia('ads')->count())
    <div class="col-12">
        <label class="mb-1">{{ __('admin.additional_images') }}</label>
        <div class="row">
            @foreach($ad->getMedia('ads') as $media)
                <div class="col-md-3 mb-2">
                    <div class="card card-body">
                        <div class="text-center">
                            <img src="{{ $media->getUrl() }}" alt="additional_image" style="max-height: 200px;" class="img-fluid">
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endif


                                    <div class="col-12 text-center mt-3">
                                        <a href="{{ url()->previous() }}" class="btn btn-outline-warning">{{ __('admin.back') }}</a>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</form>
@endsection

@section('js')
<script>
    $('.store input, .store textarea, .store select').attr('disabled', true);
</script>
@endsection
@section('css')
<style>
    .imgMontg {
        position: relative;
        width: 100%;
        height: 200px;
        overflow: hidden;
    }

    .dropBox {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .textCenter {
        text-align: center;
    }

    .imagesUploadBlock {
        position: relative;
    }

    .uploadedBlock img {
        max-height: 200px;
        width: auto;
    }
    .uploadImg {
        cursor: pointer;
        display: inline-block;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
    }
    .uploadImg span {
        font-size: 24px;
        color: #555;
    }
    .imageUploader {
        display: none;
    }
    .uploadedBlock {
        margin-top: 10px;
    }
    .uploadedBlock img {
        max-height: 200px;
        width: auto;
    }
    .nav-tabs .nav-link {
        cursor: pointer;
    }
    .nav-tabs .nav-link.active {
        background-color: #007bff;
        color: white;
    }
    .nav-tabs .nav-link:hover {
        background-color: #0056b3;
        color: white;
    }
    .form-control {
        background-color: #f8f9fa;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }
    .form-control:disabled {
        background-color: #e9ecef;
        opacity: 1;
    }
    .form-group label {
        font-weight: bold;
    }
    .btn-outline-warning {
        color: #ffc107;
        border-color: #ffc107;
    }
    .btn-outline-warning:hover {
        color: white;
        background-color: #ffc107;
        border-color: #ffc107;
    }
    .btn-outline-warning:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
    }
    .btn-outline-warning:disabled {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }

    .btn-outline-warning:disabled:hover {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }
    .btn-outline-warning:disabled:focus {
        box-shadow: none;
    }
    .btn-outline-warning:disabled:active {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }
    .btn-outline-warning:disabled:active:hover {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }
    .btn-outline-warning:disabled:active:focus {
        box-shadow: none;
    }
    .btn-outline-warning:disabled:active:focus:hover {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }
    .btn-outline-warning:disabled:active:focus:active {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }
    .btn-outline-warning:disabled:active:focus:active:hover {
        color: #ffc107;
        background-color: transparent;
        border-color: #ffc107;
    }

    .btn-outline-warning:disabled:active:focus:active:focus {
        box-shadow: none;
    }
</style>
@endsection