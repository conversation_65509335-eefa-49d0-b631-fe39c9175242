<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rates', function (Blueprint $table) {
                $table->id();
                $table->foreignId(column: 'rater_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('client_id')->nullable()->constrained('users')->onDelete('cascade');
                $table->foreignId('trainer_id')->nullable()->constrained('trainer_applications')->nullOnDelete();
                $table->foreignId('purchase_request_id')->nullable()->constrained()->nullOnDelete();
                $table->tinyInteger('stars');
                $table->text('comment')->nullable();
                $table->enum('status', allowed: ['pending', 'approved', 'rejected'])->default('pending');
                $table->timestamps();
    });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rates');
    }
};
