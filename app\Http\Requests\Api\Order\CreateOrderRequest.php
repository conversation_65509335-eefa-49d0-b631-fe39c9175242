<?php

namespace App\Http\Requests\Api\Order;

use App\Http\Requests\Api\BaseApiRequest;
use App\Enums\BookingType;
use App\Enums\DeliveryType;
use Illuminate\Validation\Rule;

class CreateOrderRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // Required fields
            'address_id' => 'required|integer|exists:addresses,id',
            'payment_method_id' => 'required|integer|exists:payment_methods,id',

            // Optional service/product specific fields
            'booking_type' => ['sometimes', Rule::enum(BookingType::class)],
            'delivery_type' => ['sometimes', Rule::enum(DeliveryType::class)],
            'scheduled_at' => 'sometimes|date|after:now',

            // Coupon and loyalty points
            'coupon_id' => 'sometimes|integer|exists:coupons,id',
            'coupon_code' => 'sometimes|string|exists:coupons,coupon_num',
            'loyalty_points_amount' => 'sometimes|integer|min:1',

            // Bank transfer specific fields (will be validated in withValida<PERSON> based on payment_method_id)
            'sender_bank_name' => 'sometimes|string|max:255',
            'sender_account_holder_name' => 'sometimes|string|max:255',
            'sender_account_number' => 'sometimes|string|max:50',
            'sender_iban' => 'sometimes|string|regex:/^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/|max:34',
            'transfer_amount' => 'sometimes|numeric|min:0.01',
            'transfer_reference' => 'nullable|string|max:100',
            'transfer_date' => 'sometimes|date|before_or_equal:today',

            // Electronic payment fields (will be validated in withValidator based on payment_method_id)
            'return_url' => 'sometimes|url',
            'cancel_url' => 'sometimes|url',

            // Additional order fields
            'notes' => 'sometimes|string|max:1000',
            'city_id' => 'sometimes|integer|exists:cities,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // Basic validation messages
            'address_id.required' => 'Address is required',
            'address_id.exists' => 'Selected address is invalid',
            'payment_method_id.required' => 'Payment method is required',
            'payment_method_id.exists' => 'Selected payment method is invalid',

            // Enum validation messages
            'booking_type.enum' => 'Booking type must be either home or salon',
            'delivery_type.enum' => 'Delivery type must be either normal or express',

            // Date validation
            'scheduled_at.after' => 'Scheduled time must be in the future',
            'transfer_date.before_or_equal' => 'Transfer date must be today or in the past',

            // Coupon and loyalty points
            'coupon_id.exists' => 'Selected coupon is invalid',
            'coupon_code.exists' => 'Invalid coupon code',
            'loyalty_points_amount.min' => 'Loyalty points amount must be at least 1',

            // Electronic payment
            // Additional fields
            'notes.max' => 'Notes cannot exceed 1000 characters',
            'city_id.exists' => 'Selected city is invalid',

            // Bank transfer field messages (Arabic)
            'sender_bank_name.required_if' => 'اسم البنك المحول منه مطلوب عند اختيار التحويل البنكي',
            'sender_bank_name.max' => 'اسم البنك المحول منه يجب ألا يتجاوز 255 حرف',
            'sender_account_holder_name.required_if' => 'اسم صاحب الحساب المحول منه مطلوب عند اختيار التحويل البنكي',
            'sender_account_holder_name.max' => 'اسم صاحب الحساب المحول منه يجب ألا يتجاوز 255 حرف',
            'sender_account_number.required_if' => 'رقم الحساب المحول منه مطلوب عند اختيار التحويل البنكي',
            'sender_account_number.max' => 'رقم الحساب المحول منه يجب ألا يتجاوز 50 حرف',
            'sender_iban.required_if' => 'رقم الآيبان المحول منه مطلوب عند اختيار التحويل البنكي',
            'sender_iban.regex' => 'رقم الآيبان المحول منه غير صحيح',
            'sender_iban.max' => 'رقم الآيبان المحول منه يجب ألا يتجاوز 34 حرف',
            'transfer_amount.required_if' => 'قيمة المبلغ المحول مطلوبة عند اختيار التحويل البنكي',
            'transfer_amount.numeric' => 'قيمة المبلغ المحول يجب أن تكون رقم',
            'transfer_amount.min' => 'قيمة المبلغ المحول يجب أن تكون أكبر من صفر',
            'transfer_reference.max' => 'مرجع التحويل يجب ألا يتجاوز 100 حرف',
            'transfer_date.required_if' => 'تاريخ التحويل مطلوب عند اختيار التحويل البنكي',
            'transfer_date.date' => 'تاريخ التحويل غير صحيح',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $user = auth('api')->user();

            if (!$user) {
                $validator->errors()->add('auth', 'User not authenticated');
                return;
            }

            // Validate that address belongs to the authenticated user
            if ($this->address_id) {
                $address = $user->addresses()->find($this->address_id);
                if (!$address) {
                    $validator->errors()->add('address_id', 'Address does not belong to you');
                }
            }

            // Validate loyalty points amount doesn't exceed user's balance
            if ($this->loyalty_points_amount) {
                if ($this->loyalty_points_amount > $user->loyalty_points) {
                    $validator->errors()->add('loyalty_points_amount', 'Insufficient loyalty points balance');
                }
            }

            // Validate cart exists and has items
            $cart = $user->cart;
            if (!$cart || $cart->items()->count() === 0) {
                $validator->errors()->add('cart', 'Cart is empty or does not exist');
                return;
            }

            // Get payment method details for validation
            $paymentMethodId = $this->payment_method_id;
            if (!$paymentMethodId) {
                $validator->errors()->add('payment_method_id', 'Payment method is required');
                return;
            }

            // Validate coupon if provided
            if ($this->coupon_code || $this->coupon_id) {
                $coupon = null;
                if ($this->coupon_code) {
                    $coupon = \App\Models\Coupon::where('coupon_num', $this->coupon_code)->first();
                } elseif ($this->coupon_id) {
                    $coupon = \App\Models\Coupon::find($this->coupon_id);
                }

                if ($coupon) {
                    // Validate coupon is active and not expired
                    if (!$coupon->is_active || ($coupon->end_date && $coupon->end_date < now())) {
                        $validator->errors()->add('coupon_code', 'Coupon is expired or inactive');
                    }

                    // Validate coupon usage limit
                    if ($coupon->max_use > 0 && $coupon->used_count >= $coupon->max_use) {
                        $validator->errors()->add('coupon_code', 'Coupon usage limit exceeded');
                    }
                }
            }

            // Validate bank transfer specific fields (ID 5 = Bank Transfer)
            if ($paymentMethodId === 5) {
                // Required fields for bank transfer
                if (!$this->sender_bank_name) {
                    $validator->errors()->add('sender_bank_name', 'اسم البنك المحول منه مطلوب عند اختيار التحويل البنكي');
                }
                if (!$this->sender_account_holder_name) {
                    $validator->errors()->add('sender_account_holder_name', 'اسم صاحب الحساب المحول منه مطلوب عند اختيار التحويل البنكي');
                }
                if (!$this->sender_account_number) {
                    $validator->errors()->add('sender_account_number', 'رقم الحساب المحول منه مطلوب عند اختيار التحويل البنكي');
                }
                if (!$this->sender_iban) {
                    $validator->errors()->add('sender_iban', 'رقم الآيبان المحول منه مطلوب عند اختيار التحويل البنكي');
                }
                if (!$this->transfer_amount) {
                    $validator->errors()->add('transfer_amount', 'قيمة المبلغ المحول مطلوبة عند اختيار التحويل البنكي');
                }
                if (!$this->transfer_date) {
                    $validator->errors()->add('transfer_date', 'تاريخ التحويل مطلوب عند اختيار التحويل البنكي');
                }

                // Validate transfer amount matches cart total
                if ($this->transfer_amount) {
                    $expectedTotal = $cart->total;
                    $tolerance = 0.01;
                    if (abs($this->transfer_amount - $expectedTotal) > $tolerance) {
                        $validator->errors()->add('transfer_amount',
                            "قيمة المبلغ المحول يجب أن تساوي إجمالي الطلب ({$expectedTotal} ريال سعودي)");
                    }
                }
            }

           

            // Validate booking type is required for services
            $hasServices = $cart->items()->whereHas('item', function($query) {
                $query->where('item_type', 'App\Models\Service');
            })->exists();

            if ($hasServices && !$this->booking_type) {
                $validator->errors()->add('booking_type', 'Booking type is required when ordering services');
            }

            // Validate delivery type is required for products
            $hasProducts = $cart->items()->whereHas('item', function($query) {
                $query->where('item_type', 'App\Models\Product');
            })->exists();

            if ($hasProducts && !$this->delivery_type) {
                $validator->errors()->add('delivery_type', 'Delivery type is required when ordering products');
            }
        });
    }
}
