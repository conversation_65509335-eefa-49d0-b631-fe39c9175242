<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Rating;
use Illuminate\Http\Request;

class RatingController extends Controller
{
    /**
     */
    public function updateStatus(Request $request, Rating $rating)
    {
        $request->validate([
            'status' => 'required|in:pending,approved,rejected',
        ]);

        $rating->update([
            'status' => $request->status,
        ]);

        return redirect()->back()->with('success', 'تم تحديث حالة التقييم بنجاح.');
    }
}