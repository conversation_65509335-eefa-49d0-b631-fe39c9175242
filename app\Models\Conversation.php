<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Conversation extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $fillable = [
        'advertisement_id',
    ];

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    
    public function advertisement()
    {
        return $this->belongsTo(Advertisement::class);
    }

     public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function lastMessage()
    {
        return $this->hasOne(Message::class)->latestOfMany();
    }
    // Conversation.php
public function client()
{
    return $this->hasOneThrough(User::class, Message::class, 'conversation_id', 'id', 'id', 'sender_id')
                ->where('messages.sender_type', 'client');
}

}
