<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderSubOrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Provider sub-orders for multi-provider order management

        DB::table('provider_sub_orders')->insert([
            [
                'order_id' => 1,
                'provider_id' => 1,
                'sub_order_number' => 'PSO-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'status' => 'completed',
                'subtotal' => 45.00,
                'services_total' => 25.00,
                'products_total' => 20.00,
                'booking_fee' => 2.00,
                'home_service_fee' => 5.00,
                'delivery_fee' => 3.00,
                'discount_amount' => 0.00,
                'total' => 50.00,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(4),
            ],
            [
                'order_id' => 2,
                'provider_id' => 2,
                'sub_order_number' => 'PSO-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'status' => 'processing',
                'subtotal' => 35.00,
                'services_total' => 35.00,
                'products_total' => 0.00,
                'booking_fee' => 1.50,
                'home_service_fee' => 0.00,
                'delivery_fee' => 0.00,
                'discount_amount' => 5.00,
                'total' => 31.50,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subHours(6),
            ],
            [
                'order_id' => 3,
                'provider_id' => 3,
                'sub_order_number' => 'PSO-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'status' => 'confirmed',
                'subtotal' => 85.00,
                'services_total' => 85.00,
                'products_total' => 0.00,
                'booking_fee' => 3.00,
                'home_service_fee' => 0.00,
                'delivery_fee' => 0.00,
                'discount_amount' => 0.00,
                'total' => 88.00,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subHours(8),
            ],
            [
                'order_id' => 4,
                'provider_id' => 1,
                'sub_order_number' => 'PSO-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'status' => 'pending_payment',
                'subtotal' => 25.00,
                'services_total' => 25.00,
                'products_total' => 0.00,
                'booking_fee' => 1.00,
                'home_service_fee' => 0.00,
                'delivery_fee' => 2.00,
                'discount_amount' => 2.50,
                'total' => 25.50,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'order_id' => 5,
                'provider_id' => 4,
                'sub_order_number' => 'PSO-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'status' => 'cancelled',
                'subtotal' => 60.00,
                'services_total' => 60.00,
                'products_total' => 0.00,
                'booking_fee' => 2.50,
                'home_service_fee' => 0.00,
                'delivery_fee' => 0.00,
                'discount_amount' => 0.00,
                'total' => 62.50,
                'created_at' => now()->subHours(8),
                'updated_at' => now()->subHours(2),
            ],
        ]);

        $this->command->info('Provider sub-orders seeded successfully!');
    }
}
