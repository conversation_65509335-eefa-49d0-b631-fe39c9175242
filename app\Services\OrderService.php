<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderBankAccount;
use App\Models\ProviderSubOrder;
use App\Models\OrderStatus;
use App\Models\Cart;
use App\Models\User;
use App\Models\Address;
use App\Models\Product;
use App\Models\Provider;
use App\Models\Service;
use App\Models\Coupon;
use App\Models\ProviderBankAccount;
use App\Services\FeeCalculationService;
use App\Enums\OrderStatus as OrderStatusEnum;
use App\Enums\PaymentStatus;
use App\Enums\PaymentMethod;
use App\Enums\BookingType;
use App\Enums\DeliveryType;
use App\Notifications\OrderStatusNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;
use App\Services\TransactionService;
    
class OrderService
{
    protected $loyaltyPointsService;
    protected $feeCalculationService;
    protected $walletService;
    protected $transactionService;


    public function __construct(LoyaltyPointsService $loyaltyPointsService, FeeCalculationService $feeCalculationService , WalletService $walletService , TransactionService $transactionService)
    {
        $this->loyaltyPointsService = $loyaltyPointsService;
        $this->feeCalculationService = $feeCalculationService;
        $this->walletService = $walletService;
        $this->transactionService = $transactionService;
    }

    /**
     * Create order from cart
     *
     * @param User $user
     * @param array $data
     * @return Order|array
     * @throws \Exception
     */
    public function createOrderFromCart(User $user, array $data)
    {
        return DB::transaction(function () use ($user, $data) {
            // Step 1: Validate cart and basic requirements
            $cart = $this->validateAndPrepareCart($user);

            // Step 2: Apply and validate coupon if provided
            $coupon = $this->applyCoupon($cart, $data);

            // Step 3: Apply fees based on booking_type and delivery_type
            $this->applyFeesToCart($cart, $data);

            // Step 4: Validate payment method and balance
            $paymentMethod = PaymentMethod::from($data['payment_method_id']);
            $this->validatePaymentMethod($user, $cart, $paymentMethod);

            // Step 5: Check and reserve product quantities
            $this->checkAndReserveProductQuantities($cart);

            // Step 6: Create the main order
            $order = $this->createMainOrder($user, $cart, $data, $coupon);

            // Step 7: Create order items
            $this->createOrderItems($order, $cart);

            // Step 8: Create provider sub-orders (always, even for single provider)
            $providerGroups = $this->groupCartItemsByProvider($cart);
            $this->createProviderSubOrders($order, $providerGroups);

            // Step 9: Process payment based on method
            $paymentResult = $this->processOrderPayment($order, $data, $paymentMethod);

            if ($paymentResult['success']) {
                // Step 10: Clear cart for immediate payment methods
                $this->clearCartIfNeeded($cart, $paymentMethod);

                // Step 11: Update coupon usage if applied
                if ($coupon) {
                    $coupon = $this->updateCouponUsage($coupon);
                }

                // Step 12: Return appropriate response
                return $this->formatOrderResponse($order, $paymentResult, $paymentMethod);
            } else {
                // Rollback on payment failure
                $this->rollbackOrderCreation($order, $cart);
                throw new \Exception($paymentResult['message']);
            }
        });
    }

    /**
     * Step 1: Validate cart and prepare for order creation
     */
    private function validateAndPrepareCart(User $user): Cart
    {
        $cart = $user->cart;

        if (!$cart || $cart->items()->count() === 0) {
            throw new \Exception('Cart is empty');
        }

        return $cart;
    }

    /**
     * Step 2: Apply and validate coupon
     */
    private function applyCoupon(Cart $cart, array $data): ?Coupon
    {
        $coupon = null;

        // Check if coupon is provided in request data
        if (isset($data['coupon_code']) || isset($data['coupon_id'])) {
            if (isset($data['coupon_code'])) {
                $coupon = Coupon::where('coupon_num', $data['coupon_code'])->first();
            } elseif (isset($data['coupon_id'])) {
                $coupon = Coupon::find($data['coupon_id']);
            }
        }
        // Check if cart already has a coupon applied
        elseif ($cart->coupon_id) {
            $coupon = Coupon::find($cart->coupon_id);
        }
        // Check if cart has coupon_code but no coupon_id
        elseif ($cart->coupon_code) {
            $coupon = Coupon::where('coupon_num', $cart->coupon_code)->first();

            // Update cart with coupon_id if found
            if ($coupon) {
                $cart->update(['coupon_id' => $coupon->id]);
            }
        }

        if ($coupon) {
            // Validate coupon
            if (!$coupon->is_active || ($coupon->end_date && $coupon->end_date < now())) {
                throw new \Exception('Coupon is expired or inactive');
            }



            // Apply coupon discount to cart if not already applied
            if (!$cart->coupon_id || $cart->coupon_id !== $coupon->id) {
                $discountAmount = $this->calculateCouponDiscount($cart, $coupon);
                $cart->update([
                    'coupon_id' => $coupon->id,
                    'coupon_code' => $coupon->coupon_num,
                    'discount_amount' => $discountAmount,
                ]);
            }
        }

        return $coupon;
    }

    /**
     * Step 4: Validate payment method and balance
     */
    private function validatePaymentMethod(User $user, Cart $cart, PaymentMethod $paymentMethod): void
    {
        switch ($paymentMethod) {
            case PaymentMethod::WALLET:
                if ($user->wallet_balance < $cart->total) {
                    throw new \Exception('Insufficient wallet balance');
                }
                break;

            case PaymentMethod::BANK_TRANSFER:
                // Bank transfer validation will be done in payment processing
                break;

            default:
                // Electronic payment methods - validation will be done by gateway
                break;
        }
    }

    /**
     * Step 6: Create main order
     */
    private function createMainOrder(User $user, Cart $cart, array $data, ?Coupon $coupon): Order
    {
        // Calculate services and products totals
        $servicesTotal = $cart->items()
            ->whereHasMorph('item', ['App\\Models\\Service'])
            ->sum(DB::raw('quantity * price'));

        $productsTotal = $cart->items()
            ->whereHasMorph('item', ['App\\Models\\Product'])
            ->sum(DB::raw('quantity * price'));

        // Fetch commission rates from settings
        $settings = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        $productCommission = isset($settings['product_commission']) ? (float)$settings['product_commission'] : 0;
        $serviceCommission = isset($settings['service_commission']) ? (float)$settings['service_commission'] : 0;
        $platformCommission = ($productCommission * $productsTotal / 100) + ($serviceCommission * $servicesTotal / 100);

        // --- FEE LOGIC ---
        $deliveryFee = 0;
        $homeServiceFee = 0;
        $bookingFee = $cart->booking_fee ?? 0;
        $bookingType = isset($data['booking_type']) ? $data['booking_type'] : null;
        $deliveryType = isset($data['delivery_type']) ? $data['delivery_type'] : null;
        $hasProducts = $cart->items()->whereHasMorph('item', ['App\\Models\\Product'])->exists();
        $hasServices = $cart->items()->whereHasMorph('item', ['App\\Models\\Service'])->exists();

        // If only products in cart
        if ($hasProducts && !$hasServices) {
            if ($deliveryType === 'express') {
                $deliveryFee = isset($settings['express_delivery_fee']) ? (float)$settings['express_delivery_fee'] : 0;
            } else {
                $deliveryFee = isset($settings['normal_delivery_fee']) ? (float)$settings['normal_delivery_fee'] : 0;
            }
        }
        // If only services in cart
        if ($hasServices && !$hasProducts) {
            if ($bookingType === 'home' && $cart->provider) {
                $homeServiceFee = $cart->provider->home_fees ?? 0;
            }
        }
        // If mixed, keep existing cart values
        if ($hasProducts && $hasServices) {
            $deliveryFee = $cart->delivery_fee ?? 0;
            $homeServiceFee = $cart->home_service_fee ?? 0;
        }

        $orderData = [
            'order_number' => generatePaddedRandomCode(),
            'invoice_number' => generatePaddedRandomCode(),

            'user_id' => $user->id,
            'address_id' => $data['address_id'],
            'payment_method_id' => $data['payment_method_id'],
            'current_status' => OrderStatusEnum::PENDING_PAYMENT->value,
            'payment_status' => PaymentStatus::PENDING->value,
            'subtotal' => $cart->subtotal,
            'services_total' => $servicesTotal,
            'products_total' => $productsTotal,
            'discount_amount' => $cart->discount_amount ?? 0,
            'coupon_id' => $coupon?->id,
            'booking_fee' => $bookingFee,
            'home_service_fee' => $homeServiceFee,
            'delivery_fee' => $deliveryFee,
            'platform_commission' => $platformCommission,
            'total' => $cart->total,
            'loyalty_points_used' => $cart->loyalty_points_used ?? 0,
            'booking_type' => $bookingType,
            'delivery_type' => $deliveryType,
            'scheduled_at' => isset($data['scheduled_at']) ? Carbon::parse($data['scheduled_at']) : null,
            'city_id' => $data['city_id'] ?? null,
        ];

        return Order::create($orderData);
    }

    /**
     * Step 10: Clear cart if needed
     */
    private function clearCartIfNeeded(Cart $cart, PaymentMethod $paymentMethod): void
    {
        // Clear cart for immediate payment methods
        if ($paymentMethod->requiresImmediateProcessing() || $paymentMethod === PaymentMethod::BANK_TRANSFER) {
            $cart->items()->delete();
            $cart->delete();

            Log::info('Cart cleared after order creation', [
                'cart_id' => $cart->id,
                'payment_method' => $paymentMethod->value,
            ]);
        }
    }

    /**
     * Step 11: Update coupon usage
     */
    private function updateCouponUsage(Coupon $coupon): void
    {
        $coupon->increment('usage_time');
    }

    /**
     * Step 12: Format order response
     */
    private function formatOrderResponse(Order $order, array $paymentResult, PaymentMethod $paymentMethod): array|Order
    {
        // For electronic payments, return payment gateway response
        if ($paymentMethod->isElectronic() && isset($paymentResult['payment_url'])) {
            return $paymentResult;
        }

        // For other payment methods, return the order
        return $order->load(['items.item', 'address', 'providerSubOrders.provider', 'bankTransfer']);
    }

    /**
     * Rollback order creation on failure
     */
    private function rollbackOrderCreation(Order $order, Cart $cart): void
    {
        // Restore product quantities
        $this->rollbackProductQuantities($order);

        // Delete order and related records
        $order->providerSubOrders()->delete();
        $order->items()->delete();
        $order->delete();
    }

    /**
     * Calculate coupon discount
     */
    private function calculateCouponDiscount(Cart $cart, Coupon $coupon): float
    {
        if ($coupon->type === 'ratio') {
            return ($cart->subtotal * $coupon->discount) / 100;
        } else {
            return min($coupon->discount, $cart->subtotal);
        }
    }

    /**
     * Group cart items by provider
     */
    private function groupCartItemsByProvider(Cart $cart)
    {
        $providerGroups = [];

        foreach ($cart->items as $item) {
            $providerId = null;

            if ($item->item_type === 'App\Models\Service') {
                $providerId = $item->item->provider_id;
            } elseif ($item->item_type === 'App\Models\Product') {
                $providerId = $item->item->provider_id;
            }

            if ($providerId) {
                if (!isset($providerGroups[$providerId])) {
                    $providerGroups[$providerId] = [];
                }
                $providerGroups[$providerId][] = $item;
            }
        }

        return $providerGroups;
    }

    /**
     * Create multiple orders for different providers
     */



    /**
     * Apply fees to cart based on booking_type and delivery_type
     */
    private function applyFeesToCart(Cart $cart, array $data)
    {
        // Apply fees using the fee calculation service
        $this->feeCalculationService->updateCartFees($cart, [
            'booking_type' => $data['booking_type'] ?? 'salon',
            'delivery_type' => $data['delivery_type'] ?? 'normal'
        ]);

        // Recalculate cart totals after applying fees
        $this->updateCartTotals($cart);
    }

    /**
     * Update cart totals (similar to CartService)
     */
    private function updateCartTotals(Cart $cart)
    {
        $cart->refresh();

        // Calculate subtotal
        $subtotal = $cart->items()->sum('total');

        // Calculate final total including all fees
        $total = $subtotal + $cart->booking_fee + $cart->home_service_fee + $cart->delivery_fee - $cart->discount_amount - $cart->loyalty_points_used;
        $total = max(0, $total); // Ensure total is not negative

        $cart->update([
            'subtotal' => $subtotal,
            'total' => $total,
        ]);
    }

    /**
     * Validate order data
     */
    private function validateOrderData(Cart $cart, array $data)
    {
        // Validate address
        $address = Address::where('id', $data['address_id'])
            ->where('user_id', $cart->user_id)
            ->first();

        if (!$address) {
            throw new \Exception('Invalid address selected');
        }

        // Validate booking type against provider capabilities
        if ($cart->hasServices()) {
            $provider = $cart->provider;
            $bookingType = $data['booking_type'] ?? 'salon';

            if ($bookingType === 'home') {
                // Check if provider offers home services
                if (!$provider || !$provider->in_home) {
                    throw new \Exception('This service provider does not offer home services');
                }

                // Check if address is in provider's city for home booking
                if ($provider && $address->city_id !== $provider->user->city_id) {
                    throw new \Exception('Address must be in the service provider\'s city for home booking');
                }
            } elseif ($bookingType === 'salon') {
                // Check if provider offers salon services
                if (!$provider || !$provider->in_salon) {
                    throw new \Exception('This service provider does not offer salon services');
                }
            }
        }

        // Validate scheduled time for services
        if ($cart->hasServices() && isset($data['scheduled_at'])) {
            $scheduledTime = Carbon::parse($data['scheduled_at']);
            $provider = $cart->provider;

            if ($scheduledTime->isPast()) {
                throw new \Exception('Scheduled time cannot be in the past');
            }

            // Validate against provider's working hours
            if ($provider) {
                $this->validateBookingTime($provider, $scheduledTime, $data['booking_type'] ?? 'salon');
            }
        }

        // Check provider availability for services
        if ($cart->hasServices()) {
            $provider = Provider::find($cart->provider_id);
            if (!$provider || !$provider->accept_orders || $provider->status !== 'approved') {
                throw new \Exception('Service provider is currently unavailable');
            }
        }
    }

    /**
     * Validate wallet balance
     */
    private function validateWalletBalance(User $user, $totalAmount)
    {
        if ($user->wallet_balance < $totalAmount) {
            return [
                'valid' => false,
                'requires_payment_method_change' => true,
                'message' => 'Wallet balance is insufficient. Please choose another payment method.',
                'wallet_balance' => $user->wallet_balance,
                'required_amount' => $totalAmount,
            ];
        }

        return ['valid' => true];
    }

    /**
     * Check and reserve product quantities with proper locking
     */
    private function checkAndReserveProductQuantities(Cart $cart)
    {
        foreach ($cart->items as $cartItem) {
            if ($cartItem->isProduct()) {
                // Use lockForUpdate to prevent race conditions
                $product = Product::where('id', $cartItem->item_id)
                    ->lockForUpdate()
                    ->first();

                if (!$product) {
                    throw new \Exception("Product not found: {$cartItem->item->name}");
                }

                if ($product->quantity < $cartItem->quantity) {
                    throw new \Exception("Insufficient stock for product: {$product->name}. Available: {$product->quantity}, Requested: {$cartItem->quantity}");
                }

                // Reserve quantity atomically
                $updated = Product::where('id', $product->id)
                    ->where('quantity', '>=', $cartItem->quantity)
                    ->update(['quantity' => DB::raw('quantity - ' . $cartItem->quantity)]);

                if (!$updated) {
                    throw new \Exception("Failed to reserve stock for product: {$product->name}. Please try again.");
                }

                Log::info('Product quantity reserved', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'reserved_quantity' => $cartItem->quantity,
                    'remaining_quantity' => $product->quantity - $cartItem->quantity
                ]);
            }
        }
    }

    /**
     * Rollback product quantities with proper locking
     */
    private function rollbackProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->isProduct()) {
                $product = Product::where('id', $orderItem->item_id)
                    ->lockForUpdate()
                    ->first();

                if ($product) {
                    // Restore quantity atomically
                    Product::where('id', $product->id)
                        ->update(['quantity' => DB::raw('quantity + ' . $orderItem->quantity)]);

                    Log::info('Product quantity restored', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'restored_quantity' => $orderItem->quantity,
                        'order_id' => $order->id
                    ]);
                }
            }
        }
    }

    /**
     * Create order from cart
     */
    private function createOrder(User $user, Cart $cart, array $data)
    {
        // Calculate services and products totals
        $servicesTotal = $cart->items()
            ->whereHasMorph('item', ['App\Models\Service'])
            ->sum(DB::raw('quantity * price'));

        $productsTotal = $cart->items()
            ->whereHasMorph('item', ['App\Models\Product'])
            ->sum(DB::raw('quantity * price'));

        $orderData = [
            'order_number' => Order::generateOrderNumber(),
            'user_id' => $user->id,
            'provider_id' => $cart->provider_id,
            'address_id' => $data['address_id'],
            'status' => 'pending_payment',
            'payment_method' => $data['payment_method'],
            'payment_status' => 'pending',
            'subtotal' => $cart->subtotal,
            'services_total' => $servicesTotal,
            'products_total' => $productsTotal,
            'discount_amount' => $cart->discount_amount,
            'coupon_code' => $cart->coupon_code,
            'booking_fee' => $cart->booking_fee,
            'home_service_fee' => $cart->home_service_fee ?? 0,
            'delivery_fee' => $cart->delivery_fee ?? 0,
            'total' => $cart->total,
            'loyalty_points_used' => $cart->loyalty_points_used,
            'booking_type' => $data['booking_type'] ?? null,
            'delivery_type' => $data['delivery_type'] ?? 'normal',
            'scheduled_at' => isset($data['scheduled_at']) ? Carbon::parse($data['scheduled_at']) : null,
            'bank_account_id' => $data['bank_account_id'] ?? null,
        ];

        return Order::create($orderData);
    }

    /**
     * Create order items from cart items
     */
    private function createOrderItems(Order $order, Cart $cart)
    {
        foreach ($cart->items as $cartItem) {
            OrderItem::create([
                'order_id' => $order->id,
                'item_type' => $cartItem->item_type,
                'item_id' => $cartItem->item_id,
                'name' => $cartItem->item->name,
                'quantity' => $cartItem->quantity,
                'price' => $cartItem->price,
                'total' => $cartItem->total,
                'options' => $cartItem->options,
            ]);
        }
    }

    /**
     * Process payment based on method (public method for external use)
     */
    public function processPayment(Order $order, array $data)
    {
        return $this->processOrderPayment($order, $data);
    }

    /**
     * Process payment based on method (internal method)
     */
    private function processOrderPayment(Order $order, array $data, PaymentMethod $paymentMethod): array
    {
        return match ($paymentMethod) {
            PaymentMethod::WALLET => $this->processWalletPayment($order),
            PaymentMethod::BANK_TRANSFER => $this->processBankTransferPayment($order, $data),
            PaymentMethod::VISA,
            PaymentMethod::MADA,
            PaymentMethod::APPLE_PAY,
            PaymentMethod::GOOGLE_PAY => $this->processElectronicPayment($order, $data, $paymentMethod),
            default => ['success' => false, 'message' => 'Invalid payment method'],
        };
    }

    /**
     * Process wallet payment
     */
    private function processWalletPayment(Order $order)
    {
        $user = $order->user;

        if ($user->wallet_balance >= $order->total) {
            // Deduct from wallet
            $user->decrement('wallet_balance', $order->total);

            // Update order status
            $order->update([
                'payment_status' => PaymentStatus::SUCCESS->value,
                'current_status' => OrderStatusEnum::PROCESSING->value,
                'payment_reference' => 'WALLET-' . time(),
            ]);

            // Process loyalty points
            $this->processLoyaltyPoints($order, 'wallet');

            $this->walletService->createTransaction($user->id, $order->total, 'pay', 'success');
            $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'transactionable_id' => $order->id,
                'transactionable_type' => 'App\Models\Order',
                'amount' => $order->total,
                'payment_method_id' => PaymentMethod::WALLET->value,
                'transaction_type' => 'pay-order',
                'transaction_status' => 'success',
            ]);
            return ['success' => true, 'message' => 'Payment successful'];
        }

        return ['success' => false, 'message' => 'Insufficient wallet balance'];
    }

    /**
     * Process bank transfer payment
     */
    private function processBankTransferPayment(Order $order, array $data)
    {
        try {
            // Create bank transfer record
            $bankTransfer = $order->bankTransfer()->create([
                'sender_bank_name' => $data['sender_bank_name'],
                'sender_account_holder_name' => $data['sender_account_holder_name'],
                'sender_account_number' => $data['sender_account_number'],
                'sender_iban' => $data['sender_iban'] ?? null,
                'transfer_amount' => $data['transfer_amount'],
                'transfer_reference' => $data['transfer_reference'] ?? null,
            ]);

            // Update order status to pending verification
            $order->update([
                'payment_reference' => 'BANK-' . $order->order_number . '-' . time(),
            ]);

            Log::info('Bank transfer payment submitted', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'transfer_id' => $bankTransfer->id,
                'transfer_amount' => $data['transfer_amount'],
                'sender_bank' => $data['sender_bank_name']
            ]);

            return [
                'success' => true,
                'message' => 'Bank transfer details submitted successfully. Your order is pending verification.',
                'bank_transfer_id' => $bankTransfer->id,
                'bank_details' => $this->getBankTransferDetails($order),
                'requires_verification' => true,
            ];

        } catch (\Exception $e) {
            Log::error('Bank transfer processing failed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }



    /**
     * Process electronic payment using MyFatoorah
     */
    private function processElectronicPayment(Order $order, array $data, PaymentMethod $paymentMethod)
    {
        try {
            $myfatoorahService = app(\App\Services\Myfatoorah\OrderPaymentService::class);

            $user = $order->user;

            // Map payment method to gateway
            $gateway = $this->getPaymentGateway($paymentMethod, $data);

            // Create payment invoice with MyFatoorah
            $result = $myfatoorahService->createOrderPaymentInvoice(
                $order,
                $user,
                [
                    'gateway' => $gateway,
                    'payment_method' => $paymentMethod->label()
                ]
            );

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Redirect to MyFatoorah payment gateway',
                    'requires_payment_gateway' => true,
                    'payment_url' => $result['invoice_url'],
                    'invoice_id' => $result['invoice_id'],
                    'order_id' => $result['order_id'],
                    'payment_method' => $paymentMethod->label(),
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['message']
                ];
            }

        } catch (\Exception $e) {
            Log::error('Electronic payment processing failed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_method' => $paymentMethod->label(),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Payment gateway error. Please try again.'
            ];
        }
    }

    /**
     * Get payment gateway for electronic payment method
     */
    private function getPaymentGateway(PaymentMethod $paymentMethod, array $data): string
    {
        // If gateway is explicitly provided in data, use it
        if (isset($data['gateway'])) {
            return $data['gateway'];
        }

        // Map payment method to default gateway
        return match ($paymentMethod) {
            PaymentMethod::VISA => 'visa',
            PaymentMethod::MADA => 'mada',
            PaymentMethod::APPLE_PAY => 'applepay',
            PaymentMethod::GOOGLE_PAY => 'googlepay',
            default => 'myfatoorah',
        };
    }

    /**
     * Process loyalty points after successful payment
     */
    private function processLoyaltyPoints(Order $order, string $paymentMethod)
    {
        $loyaltyResult = $this->loyaltyPointsService->processOrderLoyaltyPoints(
            $order->user,
            $order->total,
            $order->loyalty_points_used,
            $paymentMethod
        );

        $order->update([
            'loyalty_points_earned' => $loyaltyResult['points_earned'],
        ]);
    }

    /**
     * Get bank transfer details
     */
    private function getBankTransferDetails(Order $order = null)
    {
        // If order has a specific bank account, use it
        if ($order && $order->bank_account_id && $order->bankAccount) {
            return [
                'bank_name' => $order->bankAccount->bank_name,
                'beneficiary_name' => $order->bankAccount->holder_name,
                'account_number' => $order->bankAccount->account_number,
                'iban' => $order->bankAccount->iban,
                'order_number' => $order->order_number,
                'instructions' => 'Please include your order number in the transfer reference',
            ];
        }

        // Fallback to default bank details
        return [
            'bank_name' => 'البنك الأهلي السعودي',
            'beneficiary_name' => 'شركة سوريسو للتقنية',
            'account_number' => '**********',
            'iban' => '************************',
            'order_number' => $order ? $order->order_number : null,
            'instructions' => 'Please include your order number in the transfer reference',
        ];
    }



    /**
     * Confirm payment (called by payment gateway webhook or admin)
     */
    public function confirmPayment(Order $order, string $paymentReference = null)
    {
        return DB::transaction(function () use ($order, $paymentReference) {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
                'payment_reference' => $paymentReference ?? $order->payment_reference,
            ]);

            // Clear the user's cart now that payment is confirmed
            $user = $order->user;
            if ($user->cart) {
                $user->cart->items()->delete();
                $user->cart->delete();

                Log::info('Cart cleared after payment confirmation', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'user_id' => $user->id
                ]);
            }

            // Process loyalty points for electronic payments
            if (in_array($order->payment_method, ['credit_card', 'mada', 'apple_pay'])) {
                $this->processLoyaltyPoints($order, 'electronic');
            }

            return $order;
        });
    }

    /**
     * Cancel order and restore product quantities
     */
    public function cancelOrder(Order $order, string $reason = null, $cancelledBy = null)
    {
        return DB::transaction(function () use ($order, $reason, $cancelledBy) {
            if (!$order->canBeCancelled()) {
                throw new \Exception('Order cannot be cancelled at this stage');
            }

            // Restore product quantities for all sub-orders
            $this->rollbackProductQuantities($order);

            // Process refunds based on payment method
            $this->processOrderRefund($order);

            // Restore loyalty points if used
            if ($order->loyalty_points_used > 0) {
                $order->user->increment('loyalty_points', $order->loyalty_points_used);
            }

            // Update main order status
            $order->update([
                'current_status' => OrderStatusEnum::CANCELLED->value,
                'cancellation_reason' => $reason,
            ]);

            // Cancel all provider sub-orders and create status records
            foreach ($order->providerSubOrders as $subOrder) {
                $this->cancelProviderSubOrder($subOrder, $reason, $cancelledBy);
            }

            // Create main order status record
            $this->createOrderStatusRecord($order, OrderStatusEnum::CANCELLED->value, $cancelledBy, $reason);



            return $order->fresh(['providerSubOrders', 'user']);
        });
    }

    /**
     * Cancel a provider sub-order
     */
    private function cancelProviderSubOrder(ProviderSubOrder $subOrder, string $reason = null, $cancelledBy = null)
    {
        $subOrder->update([
            'status' => OrderStatusEnum::CANCELLED->value,
            'cancellation_reason' => $reason,
        ]);

        // Create status record for sub-order
        OrderStatus::create([
            'provider_sub_order_id' => $subOrder->id,
            'status' => OrderStatusEnum::CANCELLED->value,
            'statusable_type' => $this->getStatusableType($cancelledBy),
            'statusable_id' => $this->getStatusableId($cancelledBy),
            'map_desc' => $reason ? "Order cancelled: {$reason}" : 'Order cancelled',
        ]);
    }

    /**
     * Process order refund based on payment method
     */
    private function processOrderRefund(Order $order)
    {
        $paymentMethod = PaymentMethod::from($order->payment_method_id);

        switch ($paymentMethod) {
            case PaymentMethod::WALLET:
                if ($order->payment_status === PaymentStatus::SUCCESS->value) {
                    $order->user->increment('wallet_balance', $order->total);
                    Log::info('Wallet refund processed', [
                        'order_id' => $order->id,
                        'amount' => $order->total,
                        'user_id' => $order->user_id
                    ]);
                }
                break;

            case PaymentMethod::BANK_TRANSFER:
                // For bank transfers, admin needs to manually process refund
                Log::info('Bank transfer refund required', [
                    'order_id' => $order->id,
                    'amount' => $order->total,
                    'user_id' => $order->user_id
                ]);
                break;

            case PaymentMethod::VISA:
            case PaymentMethod::MADA:
            case PaymentMethod::APPLE_PAY:
            case PaymentMethod::GOOGLE_PAY:
                // For electronic payments, initiate refund through payment gateway
                $this->processElectronicRefund($order);
                break;
        }
    }

    /**
     * Process electronic payment refund
     */
    private function processElectronicRefund(Order $order)
    {
        try {
            // TODO: Implement MyFatoorah refund API call
            Log::info('Electronic payment refund initiated', [
                'order_id' => $order->id,
                'amount' => $order->total,
                'payment_reference' => $order->payment_reference
            ]);
        } catch (\Exception $e) {
            Log::error('Electronic refund failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create order status record
     */
    private function createOrderStatusRecord(Order $order, string $status, $statusableBy = null, string $description = null)
    {
        OrderStatus::create([
            'order_id' => $order->id,
            'status' => $status,
            'statusable_type' => $this->getStatusableType($statusableBy),
            'statusable_id' => $this->getStatusableId($statusableBy),
            'map_desc' => $description,
        ]);
    }

    /**
     * Get statusable type from the entity that made the status change
     */
    private function getStatusableType($entity): string
    {
        if (is_null($entity)) {
            return 'App\Models\Admin'; // Default to admin
        }

        if (is_string($entity)) {
            return $entity; // Already a class name
        }

        return get_class($entity);
    }

    /**
     * Get statusable ID from the entity that made the status change
     */
    private function getStatusableId($entity): ?int
    {
        if (is_null($entity)) {
            return auth()->guard('admin')->id(); // Default to current admin
        }

        if (is_object($entity)) {
            return $entity->id;
        }

        if (is_numeric($entity)) {
            return $entity;
        }

        return null;
    }

    /**
     * Rollback coupon usage
     */
    private function rollbackCouponUsage(int $couponId)
    {
        $coupon = Coupon::find($couponId);
        if ($coupon && $coupon->used_count > 0) {
            $coupon->decrement('usage_time');
            Log::info('Coupon usage rolled back', [
                'coupon_id' => $couponId,
                'new_used_count' => $coupon->fresh()->used_count
            ]);
        }
    }

    /**
     * Get user orders with pagination and filtering
     *
     * @param User $user
     * @param array $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getUserOrders(User $user, array $filters = [])
    {
        $query = $user->orders()
            ->with(['items.item', 'address', 'provider', 'bankAccount'])
            ->orderBy('created_at', 'desc');

        // Apply filters if provided
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        if (isset($filters['payment_method'])) {
            $query->where('payment_method', $filters['payment_method']);
        }

        if (isset($filters['booking_type'])) {
            $query->where('booking_type', $filters['booking_type']);
        }

        if (isset($filters['delivery_type'])) {
            $query->where('delivery_type', $filters['delivery_type']);
        }

        // Date range filter
        if (isset($filters['from_date'])) {
            $query->whereDate('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->whereDate('created_at', '<=', $filters['to_date']);
        }

        return $query->paginate(15);
    }

    /**
     * Get a specific user order
     *
     * @param User $user
     * @param int $orderId
     * @return Order
     * @throws \Exception
     */
    public function getUserOrder(User $user, int $orderId)
    {
        $order = $user->orders()
            ->with(['items.item', 'address', 'provider', 'bankAccount', 'coupon'])
            ->find($orderId);

        if (!$order) {
            throw new \Exception('Order not found');
        }

        return $order;
    }

    /**
     * Validate booking time against provider's working hours
     *
     * @param Provider $provider
     * @param Carbon $scheduledTime
     * @param string $bookingType
     * @throws \Exception
     */
    private function validateBookingTime(Provider $provider, Carbon $scheduledTime, string $bookingType)
    {
        // Get the day of the week (lowercase)
        $dayOfWeek = strtolower($scheduledTime->format('l'));

        // Get provider's working hours for this day
        $workingHour = $provider->workingHours()
            ->where('day', $dayOfWeek)
            ->where('is_working', true)
            ->first();

        if (!$workingHour) {
            throw new \Exception("Service provider is not available on {$scheduledTime->format('l')}");
        }

        // Check if the scheduled time falls within working hours
        $scheduledTimeOnly = $scheduledTime->format('H:i');
        $startTime = $workingHour->start_time;
        $endTime = $workingHour->end_time;

        if ($scheduledTimeOnly < $startTime || $scheduledTimeOnly > $endTime) {
            throw new \Exception("Scheduled time must be between {$startTime} and {$endTime} on {$scheduledTime->format('l')}");
        }

        // Additional validation: ensure booking is at least 2 hours in advance
        $minimumAdvanceTime = Carbon::now()->addHours(2);
        if ($scheduledTime->lt($minimumAdvanceTime)) {
            throw new \Exception('Booking must be scheduled at least 2 hours in advance');
        }

        // For home services, add extra validation if needed
        if ($bookingType === 'home') {
            // Could add additional home service specific validations here
            // For example: travel time considerations, service area validation, etc.
        }
    }

    /**
     * Create provider sub-orders for tracking
     */
    private function createProviderSubOrders(Order $order, array $providerGroups)
    {
        $cart = $order->user->cart;

        foreach ($providerGroups as $providerId => $items) {
            // Calculate this provider's portion of the total
            $providerSubtotal = collect($items)->sum('total');
            $providerPortion = $cart->subtotal > 0 ? $providerSubtotal / $cart->subtotal : 1;

            // Calculate provider-specific totals
            $servicesTotal = 0;
            $productsTotal = 0;

            foreach ($items as $item) {
                if ($item->item_type === 'App\Models\Service') {
                    $servicesTotal += $item->total;
                } elseif ($item->item_type === 'App\Models\Product') {
                    $productsTotal += $item->total;
                }
            }

            // Distribute fees proportionally
            $providerTotal = $providerSubtotal +
                           (($cart->booking_fee ?? 0) * $providerPortion) +
                           (($cart->home_service_fee ?? 0) * $providerPortion) +
                           (($cart->delivery_fee ?? 0) * $providerPortion) -
                           (($cart->discount_amount ?? 0) * $providerPortion);

            // Create provider sub-order
            $subOrder = ProviderSubOrder::create([
                'order_id' => $order->id,
                'provider_id' => $providerId,
                'sub_order_number' => ProviderSubOrder::generateSubOrderNumber(),
                'status' => OrderStatusEnum::PENDING_PAYMENT->value,
                'subtotal' => $providerSubtotal,
                'services_total' => $servicesTotal,
                'products_total' => $productsTotal,
                'booking_fee' => ($cart->booking_fee ?? 0) * $providerPortion,
                'home_service_fee' => ($cart->home_service_fee ?? 0) * $providerPortion,
                'delivery_fee' => ($cart->delivery_fee ?? 0) * $providerPortion,
                'discount_amount' => ($cart->discount_amount ?? 0) * $providerPortion,
                'total' => $providerTotal,
            ]);

            // Create initial status record
            OrderStatus::create([
                'provider_sub_order_id' => $subOrder->id,
                'status' => OrderStatusEnum::PENDING_PAYMENT->value,
                'statusable_type' => 'App\Models\User',
                'statusable_id' => $order->user_id,
            ]);
        }
    }

    /**
     * Get provider orders with filtering and sorting
     *
     * @param int $providerId
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProviderOrders(int $providerId, array $filters = [])
    {
        $query = ProviderSubOrder::where('provider_id', $providerId)
            ->with(['order.user']);

        // Apply status filter if provided
        if (isset($filters['status']) && !empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Apply sorting if provided
        if (isset($filters['sort'])) {
            switch ($filters['sort']) {
                case 'old_to_new':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'new_to_old':
                default:
                    $query->orderBy('created_at', 'desc');
                    break;
            }
        }

        return $query->get();
    }

    /**
     * Get provider order details by ID
     *
     * @param int $providerId
     * @param int $subOrderId
     * @return \App\Models\ProviderSubOrder|null
     */
    public function getProviderOrderDetails(int $providerId, int $subOrderId)
    {
        return ProviderSubOrder::where('provider_id', $providerId)
            ->where('id', $subOrderId)
            ->with([
                'order.user',
                'order.paymentMethod',
                'order.items.item',
                'statusChanges'
            ])
            ->first();
    }







    /**
     * Update provider order status and send notification to user
     *
     * @param int $providerId
     * @param int $subOrderId
     * @param string $newStatus
     * @param string|null $mapDesc
     * @return array
     */
    public function updateProviderOrderStatus(int $providerId, int $subOrderId, string $newStatus, string $mapDesc = null): array
    {
        try {
            return DB::transaction(function () use ($providerId, $subOrderId, $newStatus, $mapDesc) {
                // Find the provider sub-order
                $subOrder = ProviderSubOrder::where('provider_id', $providerId)
                    ->where('id', $subOrderId)
                    ->with(['order.user'])
                    ->first();

                if (!$subOrder) {
                    return [
                        'success' => false,
                        'message' => __('apis.order_not_found')
                    ];
                }

                // Validate status using enum
                $allowedStatuses = OrderStatusEnum::values();
                if (!in_array($newStatus, $allowedStatuses)) {
                    return [
                        'success' => false,
                        'message' => __('apis.invalid_order_status')
                    ];
                }

                // Update the sub-order status
                $subOrder->updateStatus(
                    $newStatus,
                    'App\Models\Provider',
                    $providerId
                );

                // Send notification to the order user
                if ($subOrder->order && $subOrder->order->user) {
                    $this->sendOrderStatusNotification($subOrder->order, $newStatus);
                }

                return [
                    'success' => true,
                    'message' => __('apis.order_status_updated'),
                    'new_status' => $newStatus
                ];
            });

        } catch (\Exception $e) {
            Log::error('Failed to update provider order status', [
                'provider_id' => $providerId,
                'sub_order_id' => $subOrderId,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to update order status: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send order status notification to user
     *
     * @param Order $order
     * @param string $newStatus
     * @return void
     */
    private function sendOrderStatusNotification(Order $order, string $newStatus): void
    {
        try {
            $notification = new OrderStatusNotification($order, $newStatus);
            $order->user->notify($notification);

            Log::info('Order status notification sent', [
                'order_id' => $order->id,
                'user_id' => $order->user->id,
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send order status notification', [
                'order_id' => $order->id,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function ClientPayments()
    {
        $orders = Order::query()
        ->where('payment_status', 'success')
        ->where('current_status', 'completed')
        ->where('user_id',auth()->id());

        return $orders ;
    }

  

    public function ProviderPayments()
    {
        $orders = ProviderSubOrder::where('provider_id' , auth()->user()->provider->id)->whereHas('order' , function($q){
            $q->where('payment_status', 'success')
            ->where('current_status', 'completed');
        })->get();

        return $orders ;
    }

}