<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'name' => [
                    'en' => 'Visa',
                    'ar' => 'فيزا'
                ],
                
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Mada',
                    'ar' => 'مدى'
                ],
                
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Apple Pay',
                    'ar' => 'آبل باي'
                ],
                
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Google Pay',
                    'ar' => 'جوجل باي'
                ],
                
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'banke Transfer',
                    'ar' => 'تحويل بنكي'
                ],
                
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Wallet',
                    'ar' => 'المحفظه'
                ],
                
                'is_active' => true
            ],
         
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::create($method);
        }
    }
}
