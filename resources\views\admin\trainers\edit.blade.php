@extends('admin.layout.master')

{{-- Extra CSS --}}
@section('css')
<link rel="stylesheet" href="{{ asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css') }}">
<link rel="stylesheet" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">
<style>
    .image-upload-wrapper {
        position: relative;
        display: inline-block;
        cursor: pointer;
    }
    .image-upload-wrapper input[type="file"] {
        display: none;
    }
    .edit-image-icon {
        position: absolute;
        bottom: 5px;
        right: 5px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border-radius: 50%;
        padding: 6px;
    }
</style>
@endsection

{{-- Content --}}
@section('content')
<form method="POST" action="{{ route('admin.trainers.update', $trainer->id) }}" class="update form-horizontal" novalidate enctype="multipart/form-data">
    @csrf
    @method('PUT')
    <input type="hidden" name="type" value="client">

    <section class="mt-3">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body">

                    <div class="text-center mb-4">
                        <label class="d-block font-weight-bold">{{ __('admin.personal_image') }}</label>
                        <div class="image-upload-wrapper">
                            <label>
                                <img src="{{ $trainer->getFirstMediaUrl('personal_image') ?? asset('admin/default.png') }}"
                                     id="personalImagePreview" class="img-thumbnail shadow-sm"
                                     style="width: 150px; height: 150px; object-fit: cover;">
                                <span class="edit-image-icon"><i class="feather icon-edit"></i></span>
                                <input type="file" name="personal_image" accept="image/*"
                                       onchange="previewImage(this, 'personalImagePreview')">
                            </label>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header"><h5 class="mb-0">{{ __('admin.multilingual_info') }}</h5></div>
                        <div class="card-body">
                            <ul class="nav nav-tabs mb-3">
                                @foreach(languages() as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link @if($loop->first) active @endif" data-toggle="pill" href="#lang_{{ $lang }}">
                                            {{ strtoupper($lang) }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>

                            <div class="tab-content">
                                @foreach(languages() as $lang)
                                    <div class="tab-pane fade @if($loop->first) show active @endif" id="lang_{{ $lang }}">
                                        <div class="form-group">
                                            <label>{{ __('admin.name') }} ({{ strtoupper($lang) }})</label>
                                            <input type="text" name="name[{{ $lang }}]" class="form-control"
                                                   value="{{ old("name.$lang", $trainer->getTranslation('name', $lang)) }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label>{{ __('admin.bio') }} ({{ strtoupper($lang) }})</label>
                                            <textarea name="bio[{{ $lang }}]" class="form-control" required>{{ old("bio.$lang", $trainer->getTranslation('bio', $lang)) }}</textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>{{ __('admin.experience') }} ({{ strtoupper($lang) }})</label>
                                            <textarea name="experience[{{ $lang }}]" class="form-control" required>{{ old("experience.$lang", $trainer->getTranslation('experience', $lang)) }}</textarea>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="form-row mb-4">
                        <div class="form-group col-md-6">
                            <label>{{ __('admin.user') }}</label>
                            <select name="user_id" class="form-control select2" required>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ $trainer->user_id == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>{{ __('admin.training_price') }}</label>
                            <input type="number" step="0.01" name="training_price" class="form-control"
                                   value="{{ old('training_price', $trainer->training_price) }}" required>
                        </div>
                    </div>

                    {{-- ========== سكشن معلومات التواصل ========== --}}
                    <div class="form-row mb-4">
                        <div class="form-group col-md-4">
                            <label>{{ __('admin.contact_whatsapp') }}</label>
                            <input type="text" name="contact_whatsapp" class="form-control"
                                   value="{{ old('contact_whatsapp', $trainer->contact_whatsapp) }}" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ __('admin.contact_phone') }}</label>
                            <input type="text" name="contact_phone" class="form-control"
                                   value="{{ old('contact_phone', $trainer->contact_phone) }}" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ __('admin.contact_email') }}</label>
                            <input type="email" name="contact_email" class="form-control"
                                   value="{{ old('contact_email', $trainer->contact_email) }}">
                        </div>
                    </div>

                    <div class="form-row mb-4">
                        <div class="form-group col-md-6">
                            <label>{{ __('admin.city') }}</label>
                            <select name="city_id" class="form-control select2" required>
                                @foreach($cities as $city)
                                    <option value="{{ $city->id }}" {{ $trainer->city_id == $city->id ? 'selected' : '' }}>
                                        {{ $city->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>{{ __('admin.region') }}</label>
                            <select name="region_id" class="form-control select2" required>
                                @foreach($regions as $region)
                                    <option value="{{ $region->id }}" {{ $trainer->region_id == $region->id ? 'selected' : '' }}>
                                        {{ $region->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                        <div class="form-group col-md-6">
    <label>{{ __('admin.status') }}</label>
    <select name="status" class="form-control select2" required>
        <option value="pending" {{ $trainer->status == 'pending' ? 'selected' : '' }}>{{ __('admin.pending') }}</option>
        <option value="approved" {{ $trainer->status == 'approved' ? 'selected' : '' }}>{{ __('admin.approved') }}</option>
        <option value="rejected" {{ $trainer->status == 'rejected' ? 'selected' : '' }}>{{ __('admin.rejected') }}</option>
    </select>
</div>


                    <div class="card mb-4">
                        <div class="card-header"><h5 class="mb-0">{{ __('admin.certificate_image') }}</h5></div>
                        <div class="card-body text-center">
                            <div class="image-upload-wrapper">
                                <label>
                                    <img src="{{ $trainer->getFirstMediaUrl('certificate_image') ?? asset('admin/default.png') }}"
                                         id="certificateImagePreview" class="img-thumbnail shadow-sm"
                                         style="width: 150px; height: 150px; object-fit: cover;">
                                    <span class="edit-image-icon"><i class="feather icon-edit"></i></span>
                                    <input type="file" name="certificate_image" accept="image/*"
                                           onchange="previewImage(this, 'certificateImagePreview')">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header"><h5 class="mb-0">{{ __('admin.works') }}</h5></div>
                        <div class="card-body">
                            <input type="file" name="works[]" class="form-control mb-2" multiple accept="image/*">
                            <div class="d-flex flex-wrap">
                                @foreach($trainer->getMedia('works') as $media)
                                    <img src="{{ $media->getUrl() }}" class="img-thumbnail m-1"
                                         style="width: 80px; height: 80px; object-fit: cover;">
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">{{ __('admin.update') }}</button>
                        <a href="{{ route('admin.trainers.index') }}" class="btn btn-secondary">{{ __('admin.back') }}</a>
                    </div>

                </div>
            </div>
        </div>
    </section>
</form>
@endsection

{{-- JS --}}
@section('js')
<script>
    function previewImage(input, targetId) {
        const file = input.files[0];
        if (file) {
            document.getElementById(targetId).src = URL.createObjectURL(file);
        }
    }
</script>
<script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
<script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
<script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
<script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>
@include('admin.shared.regionCityDropdown')
@endsection
