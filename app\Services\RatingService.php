<?php

namespace App\Services;

use App\Repositories\RatingRepository;

class RatingService
{
    protected $repository;

    public function __construct(RatingRepository $repository)
    {
        $this->repository = $repository;
    }

    public function createRating(array $data, int $raterId)
    {
        $data['rater_id'] = $raterId;

        return $this->repository->create($data);
    }

    public function alreadyRated($raterId, $purchaseRequestId): bool
{
    return \App\Models\Rating::where('rater_id', $raterId)
        ->where('purchase_request_id', $purchaseRequestId)
        ->exists();
}

}
