<div class="position-relative">
    {{-- table loader  --}}
    {{-- <div class="table_loader" >
        {{__('admin.loading')}}
    </div> --}}
    {{-- table loader  --}}
    {{-- table content --}}
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>{{ __('admin.id_num') }}</th>
                <th>{{__('admin.date')}}</th>
                <th>{{__('admin.name')}}</th>
                <th>{{__('admin.region')}}</th>
                {{-- <th>{{__('admin.users')}}</th>
                <th>{{__('admin.providers')}}</th> --}}
                <th>{{__('admin.control')}}</th>
            </tr>
        </thead>
        <tbody>
            @foreach($cities as $city)
                <tr class="delete_city">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="{{$city->id}}">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $city->id }}</td>
                    <td>{{\Carbon\Carbon::parse($city->created_at)->format('d/m/Y')}}</td>
                    <td>{{$city->name}}</td>
                    <td>{{$city->region->name}}</td>
                    {{-- <td>
                        <a href="{{ route('admin.clients.index') }}?city_id={{ $city->id }}">
                            {{ $city->users_count }}
                        </a>
                    </td>
                    <td>
                        <a href="{{ route('admin.providers.index') }}?city_id={{ $city->id }}">
                            {{ $city->providers_count }}
                        </a>
                    </td> --}}

                    <td class="product-action">
                        <span class="text-primary"><a href="{{ route('admin.cities.show', ['id' => $city->id]) }}" class="btn btn-warning btn-sm"><i class="feather icon-eye"></i> {{ __('admin.show') }}</a></span>
                        <span class="action-edit text-primary"><a href="{{ route('admin.cities.edit', ['id' => $city->id]) }}" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i>{{ __('admin.edit') }}</a></span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="{{ url('admin/cities/' . $city->id) }}"><i class="feather icon-trash"></i>{{ __('admin.delete') }}</span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}
    {{-- no data found div --}}
    @if ($cities->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{asset('admin/app-assets/images/pages/404.png')}}" alt="">
            <span class="mt-2" style="font-family: cairo">{{__('admin.there_are_no_matches_matching')}}</span>
        </div>
    @endif
    {{-- no data found div --}}

</div>
{{-- pagination  links div --}}
@if ($cities->count() > 0 && $cities instanceof \Illuminate\Pagination\AbstractPaginator )
    <div class="d-flex justify-content-center mt-3">
        {{$cities->links()}}
    </div>
@endif
{{-- pagination  links div --}}