# H:i:s Time Format Implementation

## Overview
Updated the course progress system to accept and return time in H:i:s format (Hours:Minutes:Seconds) for better user experience and frontend integration.

## What Was Implemented

### **1. API Request Format**

#### **Input Validation**
```php
$request->validate([
    'last_watch_time' => 'required|string|regex:/^\d{1,2}:\d{2}:\d{2}$/', // H:i:s format
    'time_spent' => 'nullable|integer|min:0|max:86400', // Still in seconds
    'completed' => 'nullable|boolean' // 1 to mark as completed
]);
```

#### **Accepted Time Formats**
- ✅ **Valid**: `1:30:45` (1 hour, 30 minutes, 45 seconds)
- ✅ **Valid**: `0:05:30` (5 minutes, 30 seconds)
- ✅ **Valid**: `12:00:00` (12 hours)
- ❌ **Invalid**: `90:30` (missing seconds)
- ❌ **Invalid**: `1:5:3` (minutes/seconds must be 2 digits)

### **2. API Response Format**

#### **Enhanced Response**
```json
{
    "status": true,
    "data": {
        "stage_updated": true,
        "last_watch_time": "1:30:45",           // H:i:s format
        "last_watch_time_seconds": 5445,       // Seconds for calculations
        "time_spent": 300,
        "is_completed": false,
        "completed_at": null,
        "progress_percentage": 33.33,
        "total_time_spent": 1800
    }
}
```

### **3. CourseStagesResource Enhanced**

#### **Time Format in API Response**
```json
{
    "id": 1,
    "name": "Introduction to React",
    "media": {
        "id": 15,
        "url": "https://example.com/videos/intro-react.mp4",
        "type": "video"
    },
    "is_completed": false,
    "last_watch_time": "0:30:15"  // H:i:s format
}
```

### **4. Helper Methods**

#### **Time Conversion Functions**
```php
/**
 * Convert H:i:s time format to seconds
 */
private function convertTimeToSeconds($timeString)
{
    $parts = explode(':', $timeString);
    $hours = (int) $parts[0];
    $minutes = (int) $parts[1];
    $seconds = (int) $parts[2];
    
    return ($hours * 3600) + ($minutes * 60) + $seconds;
}

/**
 * Convert seconds to H:i:s time format
 */
private function convertSecondsToTime($seconds)
{
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $remainingSeconds = $seconds % 60;
    
    return sprintf('%d:%02d:%02d', $hours, $minutes, $remainingSeconds);
}
```

## API Usage Examples

### **1. Update Watch Time**
```bash
POST /api/client/courses/1/stages/2/update-watch-time
{
    "last_watch_time": "1:30:45",
    "time_spent": 300,
    "completed": 0
}

Response:
{
    "status": true,
    "data": {
        "stage_updated": true,
        "last_watch_time": "1:30:45",
        "last_watch_time_seconds": 5445,
        "time_spent": 300,
        "is_completed": false,
        "completed_at": null,
        "progress_percentage": 33.33,
        "total_time_spent": 1800
    }
}
```

### **2. Complete Stage**
```bash
POST /api/client/courses/1/stages/2/update-watch-time
{
    "last_watch_time": "2:15:30",
    "time_spent": 600,
    "completed": 1
}

Response:
{
    "status": true,
    "data": {
        "stage_updated": true,
        "last_watch_time": "2:15:30",
        "last_watch_time_seconds": 8130,
        "time_spent": 600,
        "is_completed": true,
        "completed_at": "2024-01-15T14:30:00Z",
        "progress_percentage": 66.67,
        "total_time_spent": 2400
    }
}
```

### **3. Get Course Stages**
```bash
GET /api/client/courses/1/stages

Response:
{
    "status": true,
    "data": [
        {
            "id": 1,
            "name": "Introduction",
            "media": {...},
            "is_completed": true,
            "last_watch_time": "1:45:00"
        },
        {
            "id": 2,
            "name": "Advanced Concepts",
            "media": {...},
            "is_completed": false,
            "last_watch_time": "0:30:15"
        }
    ]
}
```

## Frontend Integration

### **1. JavaScript Video Player**
```javascript
class CourseVideoPlayer {
    constructor(courseId, stageId, videoElement) {
        this.courseId = courseId;
        this.stageId = stageId;
        this.video = videoElement;
        this.lastSavedTime = 0;
        
        this.initializePlayer();
    }
    
    async initializePlayer() {
        // Get stage data with H:i:s format
        const stageData = await this.getStageData();
        
        // Convert H:i:s to seconds for video player
        const resumeSeconds = this.timeToSeconds(stageData.last_watch_time);
        if (resumeSeconds > 0) {
            this.video.currentTime = resumeSeconds;
        }
        
        // Set up periodic save
        setInterval(() => this.saveProgress(), 10000);
    }
    
    async saveProgress() {
        const currentSeconds = Math.floor(this.video.currentTime);
        const timeString = this.secondsToTime(currentSeconds);
        
        if (currentSeconds > this.lastSavedTime + 5) {
            await fetch(`/api/client/courses/${this.courseId}/stages/${this.stageId}/update-watch-time`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                },
                body: JSON.stringify({
                    last_watch_time: timeString,
                    time_spent: Math.floor((Date.now() - this.sessionStart) / 1000)
                })
            });
            
            this.lastSavedTime = currentSeconds;
        }
    }
    
    async completeStage() {
        const totalSeconds = Math.floor(this.video.duration);
        const timeString = this.secondsToTime(totalSeconds);
        
        await fetch(`/api/client/courses/${this.courseId}/stages/${this.stageId}/update-watch-time`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getToken()}`
            },
            body: JSON.stringify({
                last_watch_time: timeString,
                time_spent: Math.floor((Date.now() - this.sessionStart) / 1000),
                completed: 1
            })
        });
    }
    
    // Helper methods
    timeToSeconds(timeString) {
        const parts = timeString.split(':');
        return (parseInt(parts[0]) * 3600) + (parseInt(parts[1]) * 60) + parseInt(parts[2]);
    }
    
    secondsToTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}
```

### **2. Vue.js Component**
```vue
<template>
    <div class="video-player-container">
        <video 
            ref="videoPlayer"
            @loadedmetadata="onVideoLoaded"
            @timeupdate="onTimeUpdate"
            @ended="onVideoEnded"
            controls
        >
            <source :src="stage.media.url" type="video/mp4">
        </video>
        
        <div class="progress-info">
            <span>Progress: {{ stage.last_watch_time }}</span>
            <span v-if="stage.is_completed" class="completed">✓ Completed</span>
        </div>
    </div>
</template>

<script>
export default {
    props: ['stage', 'courseId'],
    
    data() {
        return {
            lastSaveTime: 0,
            sessionStart: Date.now()
        }
    },
    
    methods: {
        onVideoLoaded() {
            // Resume from last watch time
            const seconds = this.timeToSeconds(this.stage.last_watch_time);
            this.$refs.videoPlayer.currentTime = seconds;
        },
        
        onTimeUpdate() {
            // Save progress every 10 seconds
            const currentTime = Math.floor(this.$refs.videoPlayer.currentTime);
            if (currentTime > this.lastSaveTime + 10) {
                this.saveProgress(currentTime);
                this.lastSaveTime = currentTime;
            }
        },
        
        onVideoEnded() {
            this.completeStage();
        },
        
        async saveProgress(currentSeconds) {
            const timeString = this.secondsToTime(currentSeconds);
            const sessionTime = Math.floor((Date.now() - this.sessionStart) / 1000);
            
            try {
                await this.$http.post(`/api/client/courses/${this.courseId}/stages/${this.stage.id}/update-watch-time`, {
                    last_watch_time: timeString,
                    time_spent: sessionTime
                });
            } catch (error) {
                console.error('Failed to save progress:', error);
            }
        },
        
        async completeStage() {
            const totalSeconds = Math.floor(this.$refs.videoPlayer.duration);
            const timeString = this.secondsToTime(totalSeconds);
            const sessionTime = Math.floor((Date.now() - this.sessionStart) / 1000);
            
            try {
                const response = await this.$http.post(`/api/client/courses/${this.courseId}/stages/${this.stage.id}/update-watch-time`, {
                    last_watch_time: timeString,
                    time_spent: sessionTime,
                    completed: 1
                });
                
                if (response.data.data.is_completed) {
                    this.$emit('stage-completed', this.stage.id);
                }
            } catch (error) {
                console.error('Failed to complete stage:', error);
            }
        },
        
        timeToSeconds(timeString) {
            const parts = timeString.split(':');
            return (parseInt(parts[0]) * 3600) + (parseInt(parts[1]) * 60) + parseInt(parts[2]);
        },
        
        secondsToTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;
            return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
    }
}
</script>
```

## Benefits

### **1. User-Friendly Format**
- ✅ **Readable**: H:i:s format is intuitive for users
- ✅ **Standard**: Follows common time display conventions
- ✅ **Precise**: Shows exact hours, minutes, and seconds

### **2. Frontend Integration**
- ✅ **Easy Display**: Can be shown directly in UI without conversion
- ✅ **Video Player Compatible**: Easy to convert to/from video player time
- ✅ **Consistent**: Same format across all API responses

### **3. Backward Compatibility**
- ✅ **Dual Format**: Returns both H:i:s and seconds in responses
- ✅ **Database Storage**: Still stores as seconds for calculations
- ✅ **API Flexibility**: Supports both input and output formats

### **4. Validation & Security**
- ✅ **Input Validation**: Strict regex validation for H:i:s format
- ✅ **Error Handling**: Clear validation messages for invalid formats
- ✅ **Type Safety**: Proper conversion between formats

The H:i:s time format implementation provides a user-friendly interface while maintaining efficient database storage and calculation capabilities! ⏰📊✨
