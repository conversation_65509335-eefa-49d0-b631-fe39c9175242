<?php

namespace App\Http\Controllers\Api;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\RateRequest;
use App\Http\Resources\Api\RatingResource;
use App\Models\Rating;
use App\Models\TrainerApplication;
use App\Services\RatingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RatingController extends Controller
{
    protected RatingService $service;

    public function __construct(RatingService $service)
    {
        $this->service = $service;
    }

public function store(RateRequest $request)
{
    $raterId = auth()->id();

    if ($request->trainer_id) {
        $isSelfAdded = \App\Models\TrainerApplication::where('id', $request->trainer_id)
            ->where('user_id', $raterId)
            ->exists();

        if ($isSelfAdded) {
            return response()->json([
                'status' => false,
                'message' => 'لا يمكنك تقييم مدرب قمت بإضافته بنفسك.',
            ], 403);
        }
    }

    if ($request->purchase_request_id) {
        $purchaseRequest = \App\Models\PurchaseRequest::find($request->purchase_request_id);

        if (!$purchaseRequest) {
            return response()->json([
                'status' => false,
                'message' => 'طلب الشراء غير موجود.',
            ], 404);
        }

        if ($purchaseRequest->buyer_id === $raterId) {
            return response()->json([
                'status' => false,
                'message' => 'لا يمكنك تقييم طلب أنت المشتري فيه.',
            ], 403);
        }
    }

    // ✅ تحقق إذا كان قد قيم من قبل
    $existingRating = Rating::where('rater_id', $raterId)
        ->when($request->trainer_id, fn($q) => $q->where('trainer_id', $request->trainer_id))
        ->when($request->purchase_request_id, fn($q) => $q->where('purchase_request_id', $request->purchase_request_id))
        ->first();

    if ($existingRating) {
        return response()->json([
            'status' => false,
            'message' => 'لقد قمت بتقييم هذا العنصر من قبل.',
        ], 403);
    }

    Rating::create([
        'rater_id' => $raterId,
        'trainer_id' => $request->trainer_id,
        'purchase_request_id' => $request->purchase_request_id,
        'stars' => $request->stars,
        'comment' => $request->comment,
    ]);

    return response()->json([
        'status' => true,
        'message' => 'تم إضافة تقييمك بنجاح.',
    ]);
}














}
