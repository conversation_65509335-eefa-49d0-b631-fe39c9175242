<?php
namespace App\Models;

use Illuminate\Support\Collection;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Category extends BaseModel implements HasMedia
{
    use InteractsWithMedia;
    use HasTranslations;

    protected $fillable = ['name', 'parent_id', 'is_active'];
    public $translatable = ['name'];
    
    protected $appends = ['has_subcategory', 'image_url'];

    public function getImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('categories') ?: asset('admin/app-assets/images/portrait/small/avatar-s-11.jpg');
    }

    public function getHasSubcategoryAttribute()
    {
        return $this->children()->exists();
    }

    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id')->where('is_active', true);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }
}