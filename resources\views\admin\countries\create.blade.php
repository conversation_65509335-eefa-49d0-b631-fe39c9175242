@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                {{-- <div class="card-header">
                    <h4 class="card-title">{{__('admin.add')}}</h4>
                </div> --}}
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="{{route('admin.countries.store')}}" class="store form-horizontal" novalidate>
                            @csrf
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-12">
                                        {{-- Name fields beside each other --}}
                                        <div class="row">
                                            @foreach (languages() as $lang)
                                                <div class="col-md-{{ 12 / count(languages()) }}">
                                                    <div class="form-group">
                                                        <label>{{ __('admin.name') }} {{ $lang }} <span class="text-danger">*</span></label>
                                                        <input type="text" name="name[{{$lang}}]" class="form-control"
                                                            placeholder="{{__('admin.write') . __('admin.name')}} {{ $lang }}"
                                                            required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        {{-- Currency fields beside each other --}}
                                        <div class="row">
                                            @foreach (languages() as $lang)
                                                <div class="col-md-{{ 12 / count(languages()) }}">
                                                    <div class="form-group">
                                                        <label>{{ __('admin.currency') }} {{ $lang }} <span class="text-danger">*</span></label>
                                                        <input type="text" name="currency[{{$lang}}]" class="form-control"
                                                            placeholder="{{__('admin.write') . __('admin.currency')}} {{ $lang }}"
                                                            required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="col-md-12 col-12">
                                            <div class="form-group">
                                                <label for="first-name-column">{{__('admin.currency_code')}}</label>
                                                <div class="controls">
                                                    <input type="text" name="currency_code" class="form-control" placeholder="{{__('admin.currency_code')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-12">
                                            <div class="form-group">
                                                <label for="first-name-column">{{__('admin.country_code')}}</label>
                                                <div class="controls">
                                                    <input type="text" name="key" class="form-control" placeholder="{{__('admin.enter_country_code')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-12">
                                            <div class="form-group">
                                                <label for="first-name-column">{{__('admin.flag')}}</label>
                                                <div class="controls">
                                                    <input type="text" name="flag" class="form-control" placeholder="{{__('admin.flag')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}"  >
                                                </div>
                                            </div>
                                        </div>

                                    </div>


                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{__('admin.add')}}</button>
                                        <a href="{{ url()->previous() }}" type="reset" class="btn btn-outline-warning mr-1 mb-1">{{__('admin.back')}}</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection
@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')}}"></script>
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>

    {{-- submit add form script --}}
        @include('admin.shared.submitAddForm')
    {{-- submit add form script --}}

@endsection