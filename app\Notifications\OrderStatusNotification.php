<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use App\Traits\Firebase;

class OrderStatusNotification extends Notification
{
    use Queueable, Firebase;

    protected $order;
    protected $newStatus;
    protected $data;

    public function __construct($order, $newStatus)
    {
        $this->order = $order;
        $this->newStatus = $newStatus;
        
        $this->data = [
            'order_id'   => $order->id,
            'order_num'  => $order->order_number,
            'status'     => $newStatus,
            'type'       => 'order_status_update',
        ];
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toArray($notifiable)
    {
        $tokens = [];
        $types  = [];

        if (count($notifiable->devices)) {
            foreach ($notifiable->devices as $device) {
                $tokens[] = $device->device_id;
                $types[]  = $device->device_type;
            }
            $this->sendFcmNotification($tokens, $types, $this->data, $notifiable->lang);
        }

        return $this->data;
    }
}
