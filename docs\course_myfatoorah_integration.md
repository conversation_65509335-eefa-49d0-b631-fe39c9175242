# Course Enrollment with MyFatoorah Integration

## Overview
This document describes the complete integration of MyFatoorah payment gateway with the course enrollment system.

## What Was Fixed and Updated

### 1. **MyFatoorah Classes Fixed**
- ✅ Fixed namespace issues in `PaymentMyfatoorahApiV2.php`
- ✅ Fixed namespace issues in `MyfatoorahApiV2.php`
- ✅ Fixed namespace issues in `ShippingMyfatoorahApiV2.php`
- ✅ All classes now use proper `App\Services\Myfatoorah` namespace

### 2. **New Configuration Added**
- ✅ Created `config/myfatoorah.php` with comprehensive settings
- ✅ Environment variables for API keys, test mode, webhooks
- ✅ Course-specific payment URLs and settings
- ✅ Supported payment gateways configuration

### 3. **New Services Created**
- ✅ `CoursePaymentService` - Dedicated service for course payments
- ✅ Integration with existing `CourseService`
- ✅ MyFatoorah invoice creation and verification
- ✅ Payment status handling and enrollment updates

### 4. **Controllers Enhanced**
- ✅ Updated `CourseController` with payment gateway endpoints
- ✅ Created `PaymentController` for MyFatoorah callbacks
- ✅ Success, error, and webhook handling
- ✅ Payment gateway listing functionality

### 5. **Routes Added**
- ✅ Course enrollment routes in `routes/client.php`
- ✅ MyFatoorah callback routes (public access)
- ✅ Payment gateway listing endpoints
- ✅ Proper route naming and organization

## API Endpoints

### Course Enrollment Endpoints
```
GET    /api/client/courses                           - List all courses
GET    /api/client/courses/{id}                      - Get course details
POST   /api/client/courses/{id}/enroll               - Enroll in course
GET    /api/client/courses/{id}/payment-gateways     - Get available payment gateways
GET    /api/client/my-courses                        - Get user's enrolled courses
POST   /api/client/course-enrollments/{id}/confirm-payment - Confirm payment
```

### MyFatoorah Callback Endpoints (Public)
```
GET    /api/client/courses/payment/success           - Payment success callback
GET    /api/client/courses/payment/error             - Payment error callback
POST   /api/client/courses/payment/webhook           - Payment webhook
GET    /api/client/courses/payment/gateways          - Get available gateways
```

## Environment Variables

Add these to your `.env` file:

```env
# MyFatoorah Configuration
MYFATOORAH_API_KEY=your_api_key_here
MYFATOORAH_TEST_MODE=true
MYFATOORAH_WEBHOOK_SECRET=your_webhook_secret
MYFATOORAH_CURRENCY=SAR
MYFATOORAH_COUNTRY_CODE=+966
MYFATOORAH_LANGUAGE=ar
MYFATOORAH_NOTIFICATION_OPTION=Lnk

# Course Payment URLs
MYFATOORAH_COURSE_SUCCESS_URL=/api/client/courses/payment/success
MYFATOORAH_COURSE_ERROR_URL=/api/client/courses/payment/error
MYFATOORAH_COURSE_WEBHOOK_URL=/api/client/courses/payment/webhook

# Logging
MYFATOORAH_LOG_ENABLED=true
```

## Usage Examples

### 1. Enroll in Course with MyFatoorah
```json
POST /api/client/courses/1/enroll
{
    "payment_method": "credit_card",
    "gateway": "myfatoorah"
}

Response:
{
    "status": true,
    "data": {
        "success": true,
        "message": "Redirect to MyFatoorah payment gateway",
        "requires_payment_gateway": true,
        "payment_url": "https://portal.myfatoorah.com/...",
        "invoice_id": "12345",
        "enrollment": {
            "id": 1,
            "status": "pending_payment",
            "payment_status": "pending"
        }
    }
}
```

### 2. Get Available Payment Gateways
```json
GET /api/client/courses/1/payment-gateways

Response:
{
    "status": true,
    "data": {
        "course_id": 1,
        "course_price": "299.00",
        "gateways": [
            {
                "PaymentMethodId": 1,
                "PaymentMethodCode": "myfatoorah",
                "PaymentMethodEn": "MyFatoorah",
                "PaymentMethodAr": "ماي فاتورة"
            }
        ]
    }
}
```

## Payment Flow

1. **User Initiates Enrollment**
   - User selects course and payment method
   - System creates enrollment record with `pending_payment` status

2. **MyFatoorah Invoice Creation**
   - `CoursePaymentService` creates invoice with MyFatoorah
   - User redirected to MyFatoorah payment page

3. **Payment Processing**
   - User completes payment on MyFatoorah
   - MyFatoorah redirects to success/error URL

4. **Payment Verification**
   - System verifies payment with MyFatoorah API
   - Updates enrollment status to `active` if successful
   - Processes loyalty points if applicable

5. **Webhook Handling**
   - MyFatoorah sends webhook for payment status changes
   - System processes webhook and updates enrollment accordingly

## Security Features

- ✅ Webhook signature validation
- ✅ Payment verification with MyFatoorah API
- ✅ Enrollment ID validation
- ✅ User authentication for enrollment endpoints
- ✅ Comprehensive error logging
- ✅ Database transaction safety

## Error Handling

- ✅ Try-catch blocks with database rollback
- ✅ Detailed error logging for debugging
- ✅ User-friendly error messages
- ✅ Payment failure handling
- ✅ Webhook processing error recovery

## Testing

To test the integration:

1. Set `MYFATOORAH_TEST_MODE=true` in `.env`
2. Use test API key from MyFatoorah
3. Create a course and attempt enrollment
4. Verify payment flow and callbacks
5. Check enrollment status updates

## Next Steps

1. Configure MyFatoorah webhook URLs in their dashboard
2. Set up production API keys when ready
3. Test all payment methods (KNET, Visa, etc.)
4. Monitor payment logs and error handling
5. Implement additional payment gateways if needed

The course enrollment system is now fully integrated with MyFatoorah and ready for production use!
