<?php

namespace App\Http\Controllers\Api\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Cart\AddToCartRequest;
use App\Http\Requests\Api\Cart\RemoveFromCartRequest;
use App\Http\Requests\Api\Cart\UpdateCartItemRequest;
use App\Http\Requests\Api\Cart\ApplyCouponRequest;
use App\Http\Requests\Api\Cart\ApplyLoyaltyPointsRequest;
use App\Http\Resources\Api\CartResource;
use App\Services\CartService;
use App\Services\FeeCalculationService;
use App\Services\Responder;
use App\Traits\ResponseTrait;

class CartController extends Controller {
  use ResponseTrait;

  /**
   * @var CartService
   */
  protected $cartService;

  /**
   * @var FeeCalculationService
   */
  protected $feeCalculationService;

  /**
   * CartController constructor.
   *
   * @param CartService $cartService
   * @param FeeCalculationService $feeCalculationService
   */
  public function __construct(CartService $cartService, FeeCalculationService $feeCalculationService)
  {
    $this->cartService = $cartService;
    $this->feeCalculationService = $feeCalculationService;
  }

  /**
   * Get the current user's cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function getCart()
  {
      $user = auth()->user();
      $cart = $this->cartService->getCart($user);

      if (!$cart->items()->count() > 0) {
          return Responder::success([], ['message' => __('apis.cart_is_empty')]);
      }

      $cart->load('items.product');
      return Responder::success(new CartResource($cart));
  }

  /**
   * Add an item (product or service) to the cart
   *
   * @param \App\Http\Requests\Api\Cart\AddToCartRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function addToCart(AddToCartRequest $request)
  {
    try {
      $user = auth()->user();
      $result = $this->cartService->addToCart($user, $request->validated());

      // Check if confirmation is required
      if (is_array($result) && isset($result['requires_confirmation'])) {
        return Responder::success($result, ['message' => 'Confirmation required']);
      }

      // Normal cart response
      return Responder::success(new CartResource($result), ['message' => __('apis.product_added_to_cart')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Update a cart item quantity
   *
   * @param \App\Http\Requests\Api\Cart\UpdateCartItemRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function updateCartItem(UpdateCartItemRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->updateCartItem($user, $request->validated());

      return Responder::success(new CartResource($cart), ['message' => __('apis.cart_updated')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Remove a product from the cart
   *
   * @param \App\Http\Requests\Api\Cart\RemoveFromCartRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeFromCart(RemoveFromCartRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->removeFromCart($user, $request->validated()['cart_item_id']);

      return Responder::success(new CartResource($cart), ['message' => __('apis.product_removed_from_cart')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Clear the cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function clearCart()
  {
    try {
      $user = auth()->user();
      $this->cartService->clearCart($user);

      return Responder::success([], ['message' => __('apis.cart_cleared')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Apply coupon to cart
   *
   * @param \App\Http\Requests\Api\Cart\ApplyCouponRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function applyCoupon(ApplyCouponRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->applyCoupon($user, $request->validated()['coupon_code']);

      return Responder::success(new CartResource($cart), ['message' => __('apis.coupon_applied')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Apply loyalty points to cart
   *
   * @param \App\Http\Requests\Api\Cart\ApplyLoyaltyPointsRequest $request
   * @return \Illuminate\Http\JsonResponse
   */
  public function applyLoyaltyPoints(ApplyLoyaltyPointsRequest $request)
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->applyLoyaltyPoints($user, $request->validated()['points']);

      return Responder::success(new CartResource($cart), ['message' => __('apis.loyalty_points_applied')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Remove coupon from cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeCoupon()
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->removeCoupon($user);

      return Responder::success(new CartResource($cart), ['message' => __('apis.coupon_removed')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Remove loyalty points from cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function removeLoyaltyPoints()
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->getCart($user);

      $cart->update(['loyalty_points_used' => 0]);

      return Responder::success(new CartResource($cart->load(['items.item', 'provider'])), ['message' => __('apis.loyalty_points_removed')]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }

  /**
   * Get fee breakdown for cart
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function getFeeBreakdown()
  {
    try {
      $user = auth()->user();
      $cart = $this->cartService->getCart($user);

      if (!$cart->items()->count() > 0) {
        return Responder::success([], ['message' => __('apis.cart_is_empty')]);
      }

      // Get fee breakdown with default options (can be extended to accept booking_type, delivery_type)
      $feeBreakdown = $this->feeCalculationService->getFeeBreakdown($cart, [
        'booking_type' => 'salon', // Default to salon, can be changed based on request
        'delivery_type' => 'normal' // Default to normal delivery
      ]);

      return Responder::success([
        'fees' => $feeBreakdown,
        'total_fees' => array_sum(array_column($feeBreakdown, 'amount'))
      ]);
    } catch (\Exception $e) {
      return Responder::error($e->getMessage(), [], 422);
    }
  }
}
