<?php

namespace App\Models;

use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends BaseModel
{
    use HasFactory, SoftDeletes;

    // Payment type constants
    const PAY_TYPE_ONLINE = 'online';
    const PAY_TYPE_WALLET = 'wallet';
    const PAY_TYPE_CASH = 'cash';

    // Payment status constants
    const PAY_STATUS_PENDING = 'pending';
    const PAY_STATUS_DONE = 'done';
    const PAY_STATUS_PAID = 'paid';
    const PAY_STATUS_RETURNED = 'returned';

    protected $fillable = [
        'order_number',
        'user_id',
        'provider_id',
        'address_id',
        'city_id',
        'provider_latitude',
        'provider_longitude',
        'address_latitude',
        'address_longitude',
        'status',
        'current_status',
        'payment_method_id',
        'payment_status',
        'subtotal',
        'services_total',
        'products_total',
        'discount_amount',
        'discount_percentage',
        'coupon_id',
        'booking_fee',
        'home_service_fee',
        'delivery_fee',
        'platform_commission',
        'provider_share',
        'total',
        'cancel_fees',
        'amount_paid',
        'loyalty_points_earned',
        'loyalty_points_used',
        'cancellation_reason',
        'scheduled_at',
        'acceptance_deadline',
        'invoice_number',
        'payment_reference',
        'payment_date',
        'booking_type',
        'delivery_type',
        'bank_account_id',
        'cancel_reason_id',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'services_total' => 'decimal:2',
        'products_total' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'booking_fee' => 'decimal:2',
        'home_service_fee' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'platform_commission' => 'decimal:2',
        'provider_share' => 'decimal:2',
        'total' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'provider_latitude' => 'decimal:8',
        'provider_longitude' => 'decimal:8',
        'address_latitude' => 'decimal:8',
        'address_longitude' => 'decimal:8',
        'loyalty_points_earned' => 'integer',
        'loyalty_points_used' => 'integer',
        'scheduled_at' => 'datetime',
        'acceptance_deadline' => 'datetime',
        'payment_date' => 'datetime',
    ];

    /**
     * Get the user that owns the order
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the provider associated with the order
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function cancelReason()
    {
        return $this->belongsTo(CancelReason::class);
    }

    /**
     * Get the address for the order
     */
    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    /**
     * Get the order items
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the coupon applied to the order
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class, 'coupon_code', 'coupon_num');
    }

    /**
     * Get the bank account for bank transfer orders
     */
    public function bankAccount()
    {
        return $this->belongsTo(ProviderBankAccount::class, 'bank_account_id');
    }

    /**
     * Get the bank transfer details for this order
     */
    public function bankTransfer()
    {
        return $this->hasOne(OrderBankAccount::class);
    }

    /**
     * Get provider sub-orders for this order
     */
    public function providerSubOrders()
    {
        return $this->hasMany(ProviderSubOrder::class);
    }

    /**
     * Get sub-order for a specific provider
     */
    public function getProviderSubOrder($providerId)
    {
        return $this->providerSubOrders()->where('provider_id', $providerId)->first();
    }

    /**
     * Get all status changes for this order (from all sub-orders)
     */
    public function getAllStatusChanges()
    {
        return OrderStatus::whereHas('providerSubOrder', function($query) {
            $query->where('order_id', $this->id);
        })->orderBy('created_at', 'desc');
    }

    public function getStatusChangesAttribute(): Collection
{
    return OrderStatus::whereHas('providerSubOrder', function($query) {
        $query->where('order_id', $this->id);
    })->orderBy('created_at', 'desc')->get();
}


    /**
     * Check if order has multiple providers
     */
    public function hasMultipleProviders()
    {
        return $this->providerSubOrders()->count() > 1;
    }

    /**
     * Get order completion percentage
     */
    public function getCompletionPercentageAttribute()
    {
        $totalSubOrders = $this->providerSubOrders()->count();
        if ($totalSubOrders === 0) return 0;

        $completedSubOrders = $this->providerSubOrders()->whereIn('status', ['completed'])->count();
        return round(($completedSubOrders / $totalSubOrders) * 100);
    }

    /**
     * Check if order has services
     */
    public function hasServices()
    {
        return $this->items()->where('item_type', 'App\Models\Service')->exists();
    }

    /**
     * Check if order has products
     */
    public function hasProducts()
    {
        return $this->items()->where('item_type', 'App\Models\Product')->exists();
    }

    /**
     * Generate unique order number
     */
    public static function generateOrderNumber()
    {
        do {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    public static function generateInvoiceNumber()
    {
        do {
            $orderNumber = 'INV-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Check if order can be cancelled
     */
    public function canBeCancelled()
    {
        return in_array($this->current_status, [ 'processing']);
    }

    /**
     * Check if order requires provider availability
     */
    public function requiresProviderAvailability()
    {
        return $this->hasServices() && $this->booking_type === 'home';
    }

    /**
     * Update order status and track the change
     */
    public function updateStatus($newStatus, $statusableType, $statusableId, $mapDesc = null, $updatePaymentStatus = null)
    {
        // Update the order status
        $updateData = ['status' => $newStatus];
        if ($updatePaymentStatus !== null) {
            $updateData['payment_status'] = $updatePaymentStatus;
        }

        $this->update($updateData);

        // Track the status change
        OrderStatus::createStatusChange(
            $this->id,
            $newStatus,
            $statusableType,
            $statusableId,
            $mapDesc
        );

        return $this;
    }

    /**
     * Get the payment method for the order
     */
    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function rate()
    {
        return $this->hasOne(OrderRate::class);
    }

    public function transactions()
{
    return $this->morphMany(Transaction::class, 'transactionable');
}

    /**
     * Scope for search functionality
     */

}
