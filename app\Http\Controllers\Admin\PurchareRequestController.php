<?php
namespace App\Http\Controllers\Admin;

use App\Jobs\Notify;
use App\Models\City;
use App\Models\PurchaseRequest;
use App\Models\Rating;
use App\Models\User;
use App\Jobs\SendSms;
use App\Models\Order;
use App\Mail\SendMail;
use App\Models\Region;
use App\Traits\Report;
use App\Models\Country;
use App\Models\Complaint;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use App\Imports\ClientImport;
use App\Notifications\BlockUser;
use App\Notifications\NotifyUser;
use App\Notifications\UnBlockUser;
use Illuminate\Support\Facades\DB;
use App\Exports\LoyaltyPointsExport;
use App\Http\Controllers\Controller;
use App\Services\TransactionService;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Notification;
use App\Http\Requests\Admin\Client\BalanceRequest;
use App\Http\Requests\Admin\purchares\Update;

class PurchareRequestController extends Controller
{

 public function index($id = null) {
        if (request()->ajax()) {
            $rows = PurchaseRequest::search(request()->searchArray)->paginate(30);
            $html = view('admin.purchares.table', compact('rows'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.purchares.index');
    }


    

    public function create()
    {
        $regions = Region::whereHas('cities')->get();
        $cities = City::all();
        $users = User::all();

        $countries = Country::all();
        return view('admin.purchares.create', get_defined_vars());
    }

public function store(Store $request)
{
    $validated = $request->validated();

    $purchase = PurchaseRequest::create($validated);

    if ($request->hasFile('personal_image')) {
        $purchase->addMedia($request->file('personal_image'))->toMediaCollection('personal_image');
    }

    if ($request->hasFile('certificate_image')) {
        $purchase->addMedia($request->file('certificate_image'))->toMediaCollection('certificate_image');
    }

    if ($request->hasFile('works')) {
        foreach ($request->file('works') as $workImage) {
            $purchase->addMedia($workImage)->toMediaCollection('works');
        }
    }

    Report::addToLog('إضافة مدرب');

    return response()->json(['url' => route('admin.purchares.index')]);
}


 public function edit($id)
{
    $purchase = PurchaseRequest::findOrFail($id);
    $regions = Region::whereHas('cities')->get();

    $cities = $purchase->region_id ? City::where('region_id', $purchase->region_id)->get() : [];

    $users      = User::all();
    $countries  = Country::all();

    $settings = [];
    $defaultCountrySetting = SiteSetting::where('key', 'default_country')->first();
    $settings['default_country'] = $defaultCountrySetting ? $defaultCountrySetting->value : 1;

    return view('admin.purchares.edit', compact('purchare', 'regions', 'cities', 'countries', 'users', 'settings'));
}

public function update(Update $request, $id)
{
    $purchase = PurchaseRequest::findOrFail($id);

    $purchase->update($request->validated());

    if ($request->hasFile('personal_image')) {
        $purchase->clearMediaCollection('personal_image');
        $purchase->addMedia($request->file('personal_image'))->toMediaCollection('personal_image');
    }

    // رفع صورة الشهادة
    if ($request->hasFile('certificate_image')) {
        $purchase->clearMediaCollection('certificate_image');
        $purchase->addMedia($request->file('certificate_image'))->toMediaCollection('certificate_image');
    }

    // رفع صور الأعمال
    if ($request->hasFile('works')) {
        $purchase->clearMediaCollection('works');
        foreach ($request->file('works') as $file) {
            $purchase->addMedia($file)->toMediaCollection('works');
        }
    }

    Report::addToLog('تعديل مدرب');

    return redirect()->route('admin.purchares.index')->with('success', 'تم التعديل بنجاح');
}


    /** public function Update Balance **/
    public function updateBalance(BalanceRequest $request)
    {
        $user   = User::findOrFail($request->user_id);
        $amount = convert2english($request->balance);
        DB::beginTransaction();
        try {
            if ($amount > 0) {
                (new TransactionService)->adminAddtoUserWallet($user, $amount);
            } elseif ($amount < 0) {
                (new TransactionService)->adminCutFromUserWallet($user, $amount);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
        }

        return redirect()->back()->with('success', __('admin.update_successfullay'));
    }

public function show($id)
{
    $purchase = PurchaseRequest::with(['advertisement', 'buyer', 'seller','ratings'])->findOrFail($id);

    return view('admin.purchares.show', compact('purchase'));
}

    public function showfinancial($id)
    {
        $complaints = Complaint::where('user_id', $id)->paginate(10);
        return view('admin.complaints.user_complaints', ['complaints' => $complaints]);
    }

    public function showorders($id)
    {
        $orders = Order::where('user_id', $id)->paginate(10);
        return view('admin.purchares.orders', ['orders' => $orders]);
    }

    public function destroy($id)
    {
        $purchare = PurchaseRequest::findOrFail($id)->delete();
        Report::addToLog('  حذف مستخدم');
        return response()->json(['id' => $id]);
    }

    public function block(Request $request)
    {
        $user = User::findOrFail($request->id);
        if ($user->status == 'blocked') {
            $user->update(['status' => 'pending']);
            Notification::send($user, new UnBlockUser($request->all()));
        } else {
            $user->update(['status' => 'blocked']);
            Notification::send($user, new BlockUser($request->all()));

        }

        return response()->json(['message' => $user->refresh()->status == 'blocked' ? __('admin.client_blocked') : __('admin.client_unblocked')]);
    }

    public function notify(Request $request)
    {
        if ($request->notify == 'notify') {
            if ('all' == $request->id) {
                $purchares = User::where('type', 'client')->latest()->get();
            } else {
                $purchares = User::findOrFail($request->id);
            }
            Notification::send($purchares, new NotifyUser($request->all()));
        } elseif ($request->notify == 'email') {
            if ('all' == $request->id) {
                $mails = User::where('type', 'client')->where('email', '!=', null)->get()->pluck('email')->toArray();
            } else {
                $mails = User::findOrFail($request->id)->email;
            }
            Mail::to($mails)->send(new SendMail(['title' => 'اشعار اداري', 'message' => $request->message]));
        } elseif ($request->notify == 'sms') {
            if ('all' == $request->id) {
                $phones = User::where('phone', '!=', null)->get()->pluck('phone')->toArray();
                dispatch(new SendSms($phones, $request->body));
            } else {
                $phone = User::findOrFail($request->id)->full_phone;
                dispatch(new SendSms($phone, $request->body));
                // $this->sendSms($phone , $request->body);
            }
        }
        return response()->json();
    }




    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (User::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من المستخدمين');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }

    public function importFile(Request $request)
    {
        Excel::import(new ClientImport, request()->file('file'));
        Report::addToLog(' رفع ملف بالعملاء');
        return response()->json(['url' => route('admin.purchares.index')]);
    }

    public function loyalityPoints()
    {
        if (request()->ajax()) {
            $rows = User::with(['city', 'region', 'orders'])
                ->search(request()->searchArray)
                ->where('type', 'client')
                ->where('loyalty_points' , '>' , 0)
                ->paginate(30);
            $html = view('admin.purchares.loyalitypoints.table', compact('rows'))->render();
            return response()->json(['html' => $html]);
        }
        // return view('admin.purchares.loyalitypoints.index');
    }

    public function exportLoyaltyPoints()
{
    return Excel::download(
        new LoyaltyPointsExport,
        'loyalty_points_' . now()->format('Y-m-d_H-i') . '.xlsx'
    );
    }

}
