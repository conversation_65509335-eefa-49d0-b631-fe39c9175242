<?php

namespace App\Services;

use App\Models\WalletTransaction;

class WalletService
{
    public function createTransaction($user_id, $amount, $type, $status)
    {
        $transaction = new WalletTransaction();
        $transaction->user_id = $user_id;
        $transaction->amount = $amount;
        $transaction->type = $type;
        $transaction->status = $status;
        $transaction->reference = generatePaddedRandomCode();

        $transaction->save();

        return $transaction;
    }

    public function getTransactions($user_id)
    {
        return WalletTransaction::where('user_id', $user_id)->get();
    }
}