<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RatingResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rater' => [
                'id' => $this->rater_id,
                'name' => optional($this->rater)->name,
            ],

            'client' => $this->when(!isset($this->trainer_id), [
                'id' => $this->client_id,
                'name' => optional($this->ratee)->name,
                // 'purchase_request_id' => $this->purchase_request_id,
            ]),

            'stars' => $this->stars,
            'comment' => $this->comment,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
