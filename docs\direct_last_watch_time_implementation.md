# Direct Last Watch Time Implementation

## Overview
Updated the course progress system to return `last_watch_time` directly from the `course_stage_completions` table in seconds, providing more efficient and accurate data retrieval.

## What Was Changed

### **1. CourseStagesResource Optimization**

#### **Before (Relationship-Based)**
```php
// Get enrollment and completion data through relationships
$enrollment = $user->courseEnrollments()
                  ->where('course_id', $this->course_id)
                  ->first();

if ($enrollment) {
    $completion = $enrollment->getStageCompletion($this->id);
    $isCompleted = $enrollment->isStageCompleted($this->id);
    $lastWatchTime = $completion ? $completion->last_watch_time : 0;
}
```

#### **After (Direct Database Query)**
```php
// Get completion data directly from course_stage_completions table
$completion = \DB::table('course_stage_completions')
                ->join('course_enrollments', 'course_stage_completions.enrollment_id', '=', 'course_enrollments.id')
                ->where('course_enrollments.user_id', $user->id)
                ->where('course_enrollments.course_id', $this->course_id)
                ->where('course_stage_completions.stage_id', $this->id)
                ->select('course_stage_completions.last_watch_time', 'course_stage_completions.completed_at')
                ->first();

if ($completion) {
    $isCompleted = $completion->completed_at !== null;
    $lastWatchTime = $completion->last_watch_time ?? 0;
}
```

### **2. API Response Format**

#### **CourseStagesResource Response**
```json
{
    "id": 1,
    "name": "Introduction to React",
    "media": {
        "id": 15,
        "url": "https://example.com/videos/intro-react.mp4",
        "type": "video"
    },
    "is_completed": false,
    "last_watch_time": 1800  // Direct seconds value
}
```

#### **CourseProgressController Response**
```json
{
    "status": true,
    "data": {
        "stage_updated": true,
        "last_watch_time": 1800,        // Direct seconds value
        "time_spent": 300,
        "is_completed": false,
        "completed_at": null,
        "progress_percentage": 33.33,
        "total_time_spent": 1800
    }
}
```

### **3. Input Processing**

#### **H:i:s to Seconds Conversion**
```php
// Input validation still accepts H:i:s format
$request->validate([
    'last_watch_time' => 'required|string|regex:/^\d{1,2}:\d{2}:\d{2}$/', // H:i:s format
    'time_spent' => 'nullable|integer|min:0|max:86400',
    'completed' => 'nullable|boolean'
]);

// Convert H:i:s to seconds for database storage
$watchTimeString = $request->input('last_watch_time');
$watchTimeSeconds = $this->convertTimeToSeconds($watchTimeString);

// Store as seconds in database
$updateData = ['last_watch_time' => $watchTimeSeconds];
```

## Benefits

### **1. Performance Optimization**
- ✅ **Fewer Queries**: Single direct query instead of multiple relationship queries
- ✅ **Reduced Memory**: No need to load full model instances
- ✅ **Faster Response**: Direct database access with specific field selection

### **2. Data Consistency**
- ✅ **Single Source**: Data comes directly from the source table
- ✅ **No Conversion Overhead**: Returns raw seconds value for calculations
- ✅ **Accurate Values**: No potential for conversion errors

### **3. API Efficiency**
- ✅ **Smaller Payloads**: Returns only necessary data
- ✅ **Frontend Ready**: Seconds format is ideal for video players
- ✅ **Calculation Friendly**: Easy to perform time calculations

### **4. Database Optimization**
- ✅ **Targeted Queries**: Only selects needed columns
- ✅ **Efficient Joins**: Minimal join operations
- ✅ **Index Utilization**: Leverages existing indexes

## Implementation Details

### **1. Database Query Structure**
```sql
SELECT 
    course_stage_completions.last_watch_time,
    course_stage_completions.completed_at
FROM course_stage_completions
JOIN course_enrollments ON course_stage_completions.enrollment_id = course_enrollments.id
WHERE course_enrollments.user_id = ?
  AND course_enrollments.course_id = ?
  AND course_stage_completions.stage_id = ?
```

### **2. Data Flow**
```
Frontend (H:i:s) → API (Convert to seconds) → Database (Store seconds) → API (Return seconds) → Frontend (Use directly)
```

### **3. Time Conversion Functions**
```php
// Convert H:i:s input to seconds for storage
private function convertTimeToSeconds($timeString)
{
    $parts = explode(':', $timeString);
    $hours = (int) $parts[0];
    $minutes = (int) $parts[1];
    $seconds = (int) $parts[2];
    
    return ($hours * 3600) + ($minutes * 60) + $seconds;
}

// Convert seconds to H:i:s for display (if needed)
private function convertSecondsToTime($seconds)
{
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $remainingSeconds = $seconds % 60;
    
    return sprintf('%d:%02d:%02d', $hours, $minutes, $remainingSeconds);
}
```

## Frontend Integration

### **1. Video Player Usage**
```javascript
// Direct usage without conversion
function loadVideo(stage) {
    const video = document.getElementById('courseVideo');
    video.src = stage.media.url;
    
    // Use last_watch_time directly (already in seconds)
    if (stage.last_watch_time > 0) {
        video.currentTime = stage.last_watch_time;
    }
}

// Update progress with H:i:s format
function updateProgress(currentSeconds) {
    const timeString = secondsToHMS(currentSeconds);
    
    fetch('/api/courses/1/stages/2/update-watch-time', {
        method: 'POST',
        body: JSON.stringify({
            last_watch_time: timeString  // Send as H:i:s
        })
    });
}
```

### **2. Progress Display**
```javascript
// Display progress using seconds directly
function displayProgress(stage) {
    const progressPercent = (stage.last_watch_time / stage.video_duration) * 100;
    const timeDisplay = formatSeconds(stage.last_watch_time);
    
    return `
        <div class="progress-bar" style="width: ${progressPercent}%"></div>
        <span>Watched: ${timeDisplay}</span>
    `;
}
```

### **3. Vue.js Component**
```vue
<template>
    <div class="stage-progress">
        <video 
            ref="video"
            @loadedmetadata="resumeVideo"
            @timeupdate="updateProgress"
        />
        <div class="progress-info">
            Progress: {{ formatTime(stage.last_watch_time) }} / {{ formatTime(stage.video_duration) }}
        </div>
    </div>
</template>

<script>
export default {
    props: ['stage'],
    
    methods: {
        resumeVideo() {
            // Use last_watch_time directly (already in seconds)
            this.$refs.video.currentTime = this.stage.last_watch_time;
        },
        
        updateProgress() {
            const currentTime = Math.floor(this.$refs.video.currentTime);
            if (currentTime > this.lastSaved + 10) {
                this.saveProgress(currentTime);
                this.lastSaved = currentTime;
            }
        },
        
        async saveProgress(seconds) {
            const timeString = this.secondsToHMS(seconds);
            
            await this.$http.post(`/api/courses/${this.courseId}/stages/${this.stage.id}/update-watch-time`, {
                last_watch_time: timeString
            });
        },
        
        formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        },
        
        secondsToHMS(seconds) {
            return this.formatTime(seconds);
        }
    }
}
</script>
```

## API Usage Examples

### **1. Update Watch Time**
```bash
POST /api/client/courses/1/stages/2/update-watch-time
{
    "last_watch_time": "0:30:15",  # Input as H:i:s
    "time_spent": 300,
    "completed": 0
}

Response:
{
    "status": true,
    "data": {
        "stage_updated": true,
        "last_watch_time": 1815,    # Output as seconds
        "time_spent": 300,
        "is_completed": false,
        "completed_at": null,
        "progress_percentage": 33.33,
        "total_time_spent": 1800
    }
}
```

### **2. Get Course Stages**
```bash
GET /api/client/courses/1/stages

Response:
{
    "status": true,
    "data": [
        {
            "id": 1,
            "name": "Introduction",
            "media": {...},
            "is_completed": false,
            "last_watch_time": 1815    # Direct seconds value
        }
    ]
}
```

## Performance Comparison

### **Before (Relationship-Based)**
- Multiple model instantiations
- Relationship loading overhead
- Method call chains
- Memory usage for full objects

### **After (Direct Query)**
- Single database query
- Minimal data transfer
- Direct field access
- Reduced memory footprint

The direct last_watch_time implementation provides significant performance improvements while maintaining data accuracy and API usability! ⚡📊🎯
