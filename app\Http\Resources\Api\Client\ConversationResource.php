<?php
namespace App\Http\Resources\Api\Client;

use Illuminate\Http\Resources\Json\JsonResource;

class ConversationResource extends JsonResource
{
    public function toArray($request)
    {
        $media = $this->getFirstMedia('chat-files');

        return [
            'id' => $this->id,
             'advertisement_name' => $this->advertisement?->name,
            // 'advertiser_name' => $this->receiver?->name,
            'last_message_time' => $this->created_at->format('Y-m-d H:i:s'),


            // 'sender_id' => $this->sender_id,
            // 'receiver_id' => $this->receiver_id,
            // 'advertisement_id' => $this->advertisement_id,
            // 'message' => $this->message,
            // 'message_type' => $this->message_type,
            // 'is_read' => $this->is_read,
            // 'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            // 'file' => $media ? [
            //     'url' => $media->getUrl(),
            //     'mime_type' => $media->mime_type,
            //     'name' => $media->name,
            // ] : null,
        ];
    }
}
