<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Traits\Report;
use Illuminate\Http\Request;
use App\Services\TransactionService;
use App\Enums\PaymentMethod;
    
class BankTransferOrderController extends Controller
{
    protected $transactionService;
    /**
     * Display a listing of bank transfer orders.
     */

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    public function index($id = null)
    {
        if (request()->ajax()) {
            $orders = Order::with(['user', 'provider', 'address', 'bankTransfer', 'paymentMethod'])
                ->where('payment_method_id', 5) // Bank Transfer payment method
                ->search(request()->searchArray)
                ->paginate(30);
            $html = view('admin.bank_transfer_orders.table', compact('orders'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.bank_transfer_orders.index');
    }

    /**
     * Display the specified bank transfer order.
     */
    public function show($id)
    {
        $order = Order::with([
            'user',
            'items.item',
            'coupon',
            'bankTransfer',
            'providerSubOrders' => function($query) {
                $query->with([
                    'provider.user',
                    'statusChanges' => function($statusQuery) {
                        $statusQuery->with('statusable')->orderBy('created_at', 'desc');
                    },
                    'orderItems' => function($itemsQuery) {
                        $itemsQuery->with(['item', 'service', 'product']);
                    }
                ]);
            }
        ])->where('payment_method_id', 5)->findOrFail($id);

        // Get payment method details
        $paymentMethod = \App\Models\PaymentMethod::find($order->payment_method_id);

        return view('admin.bank_transfer_orders.show', compact('order', 'paymentMethod'));
    }

    /**
     * Verify bank transfer
     */
    public function verifyTransfer(Request $request, $id)
    {
        try {
            $order = Order::with('bankTransfer')->findOrFail($id);

            if (!$order->bankTransfer) {
                return response()->json([
                    'success' => false,
                    'message' => 'No bank transfer found for this order'
                ], 404);
            }

            if ($order->payment_status !== \App\Enums\PaymentStatus::PENDING->value) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment has already been processed'
                ], 400);
            }

            // Get current admin
            $admin = auth()->guard('admin')->user();
            $notes = $request->input('notes', 'Bank transfer verified by admin');

            // Update order payment status and order status
            $order->update([
                'current_status' => \App\Enums\OrderStatus::PROCESSING->value,
                'payment_status' => \App\Enums\PaymentStatus::SUCCESS->value
            ]);

            // Update all provider sub-orders to processing and create status records
            foreach ($order->providerSubOrders as $subOrder) {
                $subOrder->updateStatus(
                    \App\Enums\OrderStatus::PROCESSING->value,
                    'App\Models\Admin',
                    $admin->id,
                    "Bank transfer verified: {$notes}"
                );
            }

            // Create main order status record
            OrderStatus::createOrderStatusChange(
                $order->id,
                \App\Enums\OrderStatus::PROCESSING->value,
                'App\Models\Admin',
                $admin->id,
                "Bank transfer verified by admin: {$notes}"
            );

            $this->transactionService->createTransaction([
                'user_id' => $order->user_id,
                'transactionable_id' => $order->id,
                'transactionable_type' => 'App\Models\Order',
                'amount' => $order->total,
                'payment_method_id' => PaymentMethod::BANK_TRANSFER->value,
                'transaction_type' => 'pay-order',
                'transaction_status' => 'success',
            ]);

            // Log the action
            Report::addToLog("تأكيد التحويل البنكي للطلب رقم {$order->order_number}");

            return response()->json([
                'success' => true,
                'message' => 'Bank transfer verified successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to verify transfer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject bank transfer
     */
    public function rejectTransfer(Request $request, $id)
    {
        try {
            $order = Order::with('bankTransfer')->findOrFail($id);

            if (!$order->bankTransfer) {
                return response()->json([
                    'success' => false,
                    'message' => 'No bank transfer found for this order'
                ], 404);
            }

            if ($order->payment_status !== \App\Enums\PaymentStatus::PENDING->value) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment has already been processed'
                ], 400);
            }
            $reason = $request->input('reason', 'Transfer rejected by admin');
            $admin = auth()->guard('admin')->user();

            // Update order status
            $order->update([
                'current_status' => \App\Enums\OrderStatus::CANCELLED->value,
                'payment_status' => \App\Enums\PaymentStatus::FAILED->value
            ]);

            // Update all provider sub-orders to cancelled and create status records
            foreach ($order->providerSubOrders as $subOrder) {
                $subOrder->updateStatus(
                    \App\Enums\OrderStatus::CANCELLED->value,
                    'App\Models\Admin',
                    $admin->id,
                    "Bank transfer rejected: {$reason}"
                );
            }

            // Create main order status record
            OrderStatus::createOrderStatusChange(
                $order->id,
                \App\Enums\OrderStatus::CANCELLED->value,
                'App\Models\Admin',
                $admin->id,
                "Bank transfer rejected by admin: {$reason}"
            );

            // Restore product quantities if any
            $this->restoreProductQuantities($order);

            // Log the action
            Report::addToLog("رفض التحويل البنكي للطلب رقم {$order->order_number}: {$reason}");

            return response()->json([
                'success' => true,
                'message' => 'Bank transfer rejected successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject transfer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore product quantities when order is cancelled
     */
    private function restoreProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->item_type === 'App\Models\Product') {
                $product = \App\Models\Product::find($orderItem->item_id);
                if ($product) {
                    $product->increment('quantity', $orderItem->quantity);
                }
            }
        }
    }
}
