{{-- <div class="tab-pane fade" id="orders">
    @if($row->orders->count() > 0)
        <div class="card">
            <div class="card-header header-elements-inline">
                <h5 class="card-title">{{ __('admin.orders') }}</h5>
                <div class="header-elements">
                    <span class="badge badge-primary">
                        {{ $row->orders->count() }} {{ __('admin.orders') }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <div class="contain-table text-center">
                        <table class="table datatable-button-init-basic text-center table-hover">
                            <thead class="thead-light">
                                <tr class="text-center">
                                    <th class="text-center">#</th>
                                    <th class="text-center">{{__('admin.delivery')}}</th>

                                    <th class="text-center">{{__('admin.order_num')}}</th>

                                    <th class="text-center">{{__('admin.final_total')}}</th>


                                    <th class="text-center">{{__('admin.order_status')}}</th>
                                </tr>
                            </thead>
                            <tbody class="text-center">
                                @forelse($row->orders as $key => $order)
                                    <tr class="delete_row text-center">
                                        <td class="text-center align-middle">{{ $key + 1 }}</td>
                                        <td class="text-center align-middle">{{ $order->delivey?->name }}</td>
                                        <td class="text-center align-middle">{{ $order->order_num }}</td>
                                        <td class="text-center align-middle">{{ $order->final_total }}</td>
                                      
                                      
                                        <td class="text-center align-middle">
                                            <span class="badge badge-info">{{ __('order.'.$order->status) }}</span>
                                        </td>
                                    </tr>
                                @empty
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-5">
            <div class="empty-state">
                <img src="{{ asset('admin/app-assets/images/pages/404.png') }}"
                     alt="{{ __('admin.no_orders_found') }}"
                     class="img-fluid mb-3"
                     style="max-width: 200px;">
                <h5 class="text-muted mb-2">{{ __('admin.no_orders_found') }}</h5>
                <p class="text-muted" style="font-family: cairo">
                    {{ __('admin.there_are_no_matches_matching') }}
                </p>
            </div>
        </div>
    @endif
</div> --}}
