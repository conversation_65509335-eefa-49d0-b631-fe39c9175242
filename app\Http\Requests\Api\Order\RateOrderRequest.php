<?php

namespace App\Http\Requests\Api\Order;

use App\Http\Requests\Api\BaseApiRequest;

class RateOrderRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_id' => 'required|exists:orders,id',
            'timing_rate' => 'required|numeric|min:1|max:5',
            'quality_rate' => 'required|numeric|min:1|max:5',
            'service_rate' => 'required|numeric|min:1|max:5',
            'body' => 'nullable|string|max:1000',
    
            'images' => 'sometimes|nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Max 5MB per image
    
            'videos' => 'sometimes|nullable|array|max:3',
            'videos.*' => 'file|mimetypes:video/mp4,video/quicktime,video/x-msvideo|max:51200', // Max 50MB per video
        ];
    }
    

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'order_id.required' => __('apis.order_id_required'),
            'order_id.exists' => __('apis.order_not_found'),
            'rate.required' => __('apis.rate_required'),
            'rate.numeric' => __('apis.rate_must_be_numeric'),
            'rate.min' => __('apis.rate_min_value'),
            'rate.max' => __('apis.rate_max_value'),
            'note.max' => __('apis.note_max_length'),
        ];
    }
}
