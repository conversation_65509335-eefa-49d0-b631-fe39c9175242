<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Polymorphic ratings for services and providers
        
        DB::table('rates')->insert([
            [
                'user_id' => 1,
                'rateable_type' => 'App\\Models\\Service',
                'rateable_id' => 1,
                'rate' => 5,
                'body' => 'Excellent service! Very professional and the results were amazing.',
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'user_id' => 2,
                'rateable_type' => 'App\\Models\\Service',
                'rateable_id' => 1,
                'rate' => 4,
                'body' => 'Good service, but the appointment was delayed by 15 minutes.',
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 3,
                'rateable_type' => 'App\\Models\\Provider',
                'rateable_id' => 1,
                'rate' => 5,
                'body' => 'Amazing provider! Very skilled and friendly. Highly recommended.',
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'user_id' => 1,
                'rateable_type' => 'App\\Models\\Provider',
                'rateable_id' => 2,
                'rate' => 3,
                'body' => 'Average service. The provider was nice but the results could be better.',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => 2,
                'rateable_type' => 'App\\Models\\Service',
                'rateable_id' => 2,
                'rate' => 5,
                'body' => 'Perfect! Exactly what I wanted. Will definitely book again.',
                'created_at' => now()->subHours(12),
                'updated_at' => now()->subHours(12),
            ],
            [
                'user_id' => 3,
                'rateable_type' => 'App\\Models\\Service',
                'rateable_id' => 3,
                'rate' => 4,
                'body' => 'Very good service. Clean environment and professional staff.',
                'created_at' => now()->subHours(6),
                'updated_at' => now()->subHours(6),
            ],
            [
                'user_id' => 1,
                'rateable_type' => 'App\\Models\\Provider',
                'rateable_id' => 3,
                'rate' => 5,
                'body' => 'Outstanding provider! Very experienced and attentive to details.',
                'created_at' => now()->subHours(3),
                'updated_at' => now()->subHours(3),
            ],
        ]);

        $this->command->info('Rates seeded successfully!');
    }
}
