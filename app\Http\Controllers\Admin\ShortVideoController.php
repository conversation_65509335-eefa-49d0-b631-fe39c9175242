<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\shortvideos\Store;
use App\Http\Requests\Admin\shortvideos\Update;
use App\Models\ShortVideo ;
use App\Traits\Report;


class ShortVideoController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {
            $shortvideos = ShortVideo::search(request()->searchArray)->paginate(30);
            $html = view('admin.shortvideos.table' ,compact('shortvideos'))->render() ;
            return response()->json(['html' => $html]);
        }
        return view('admin.shortvideos.index');
    }

    public function create()
    {
        return view('admin.shortvideos.create');
    }


   
    public function show($id)
    {
        $shortvideo = ShortVideo::findOrFail($id);
        return view('admin.shortvideos.show' , ['shortvideo' => $shortvideo]);
    }
    public function destroy($id)
    {
        $shortvideo = ShortVideo::findOrFail($id)->delete();
        Report::addToLog('  حذف الفيديو') ;
        return response()->json(['id' =>$id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);
        
        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (ShortVideo::whereIntegerInRaw('id',$ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من الفيديوهات') ;
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }
}
