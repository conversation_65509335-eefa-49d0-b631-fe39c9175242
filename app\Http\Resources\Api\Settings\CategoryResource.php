<?php

namespace App\Http\Resources\Api\Settings;

use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    public function toArray($request)
    {
        $baseData = [
            'id' => $this->id,
            'name' => $this->name,
            'image' => $this->image_url,
        ];

        if ($this->parent_id === null) {
            $baseData['has_subcategory'] = $this->children()->exists();
        }

        return $baseData;
    }
}