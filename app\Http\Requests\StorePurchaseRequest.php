<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePurchaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

   public function rules(): array
{
    return [
        'advertisement_id' => 'required|exists:advertisements,id',
        // 'seller_id' => 'required|exists:users,id',
        // 'status' => 'nullable|in:under_review,waiting_buyer_confirmation,completed,problem,cancelled',
        // 'amount_paid' => 'required|numeric|min:0',
        'wallet_credit_used' => 'nullable|numeric|min:0',
        'payment_method' => 'required|string|in:online,wallet', 

    ];
}
}
