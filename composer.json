{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.1", "fakerphp/faker": "^1.23", "guzzlehttp/guzzle": "^7.0", "intervention/image": "^3.0", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^3.0", "livewire/livewire": "^3.4", "maatwebsite/excel": "^3.1", "niklasravnsborg/laravel-pdf": "*", "propaganistas/laravel-phone": "^6.0", "spatie/laravel-medialibrary": "^11.13", "spatie/laravel-translatable": "^6.0.0"}, "require-dev": {"driftingly/rector-laravel": "^2.0", "mockery/mockery": "^1.5", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Illuminate\\Routing\\": "app\\Overrides\\"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}