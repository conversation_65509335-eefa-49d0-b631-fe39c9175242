<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InsertMigrationRecordSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Insert a record for the users table migration to mark it as completed
        DB::table('migrations')->insert([
            'migration' => '2020_08_21_000000_create_users_table',
            'batch' => 1
        ]);

        $this->command->info('Migration record for users table inserted successfully!');
    }
}
