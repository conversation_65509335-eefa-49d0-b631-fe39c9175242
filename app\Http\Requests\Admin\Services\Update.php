<?php

namespace App\Http\Requests\Admin\Services;

use Illuminate\Foundation\Http\FormRequest;

class Update extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $default_lang = config('app.locale');

        return [
            'name'                      => 'required|array',
            "name.{$default_lang}"      => 'required|string|max:191',
            'name.*'                    => 'nullable|string|max:191',
            'description'               => 'required|array',
            "description.{$default_lang}" => 'required|string',
            'description.*'             => 'nullable|string',
            'price'                     => 'required|numeric|min:0|max:999999.99',
            'duration'                  => 'required|integer|min:1|max:1440', // max 24 hours in minutes
            'expected_time_to_accept'   => 'required|integer|min:1|max:1440', // max 24 hours in minutes
            'is_active'                 => 'nullable|boolean',
            'provider_id'               => 'required|exists:providers,id',
            'category_id'               => 'required|exists:categories,id',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name'                      => __('admin.service_name'),
            'description'               => __('admin.service_description'),
            'price'                     => __('admin.service_price'),
            'duration'                  => __('admin.service_duration'),
            'expected_time_to_accept'   => __('admin.expected_time_to_accept'),
            'is_active'                 => __('admin.service_status'),
            'provider_id'               => __('admin.service_provider'),
            'category_id'               => __('admin.service_category'),
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'price.max'                 => __('admin.max_amount_999999'),
            'duration.max'              => 'Duration cannot exceed 24 hours (1440 minutes)',
            'expected_time_to_accept.max' => 'Expected time cannot exceed 24 hours (1440 minutes)',
            'provider_id.exists'        => __('admin.provider_not_found'),
            'category_id.exists'        => __('admin.category_not_found'),
        ];
    }
}
