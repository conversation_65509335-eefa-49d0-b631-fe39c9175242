<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Course enrollments matching the migration structure

        DB::table('course_enrollments')->insert([
            [
                'user_id' => 1,
                'course_id' => 1,
                'enrolled_at' => now()->subDays(15),
                'status' => 'active',
                'progress_percentage' => 60.00,
                'total_time_spent' => 2160, // 36 minutes in seconds
                'last_accessed_at' => now()->subHours(2),
                'completed_stages_count' => 2,
                'completed_at' => null,
                'payment_method' => 'credit_card',
                'payment_status' => 'paid',
                'amount_paid' => 50.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => now()->subDays(15),
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subHours(2),
            ],
            [
                'user_id' => 2,
                'course_id' => 1,
                'enrolled_at' => now()->subDays(12),
                'status' => 'completed',
                'progress_percentage' => 100.00,
                'total_time_spent' => 3600, // 60 minutes in seconds
                'last_accessed_at' => now()->subDays(2),
                'completed_stages_count' => 3,
                'completed_at' => now()->subDays(2),
                'payment_method' => 'wallet',
                'payment_status' => 'paid',
                'amount_paid' => 50.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => now()->subDays(12),
                'created_at' => now()->subDays(12),
                'updated_at' => now()->subDays(2),
            ],
            [
                'user_id' => 3,
                'course_id' => 2,
                'enrolled_at' => now()->subDays(10),
                'status' => 'active',
                'progress_percentage' => 33.33,
                'total_time_spent' => 1800, // 30 minutes in seconds
                'last_accessed_at' => now()->subDays(1),
                'completed_stages_count' => 1,
                'completed_at' => null,
                'payment_method' => 'mada',
                'payment_status' => 'paid',
                'amount_paid' => 75.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => now()->subDays(10),
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => 1,
                'course_id' => 3,
                'enrolled_at' => now()->subDays(8),
                'status' => 'active',
                'progress_percentage' => 66.67,
                'total_time_spent' => 2700, // 45 minutes in seconds
                'last_accessed_at' => now()->subHours(6),
                'completed_stages_count' => 2,
                'completed_at' => null,
                'payment_method' => 'apple_pay',
                'payment_status' => 'paid',
                'amount_paid' => 60.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => now()->subDays(8),
                'created_at' => now()->subDays(8),
                'updated_at' => now()->subHours(6),
            ],
            [
                'user_id' => 4,
                'course_id' => 2,
                'enrolled_at' => now()->subDays(3),
                'status' => 'pending_payment',
                'progress_percentage' => 0.00,
                'total_time_spent' => 0,
                'last_accessed_at' => null,
                'completed_stages_count' => 0,
                'completed_at' => null,
                'payment_method' => 'bank_transfer',
                'payment_status' => 'pending',
                'amount_paid' => 75.00,
                'payment_reference' => null,
                'bank_account_id' => 1,
                'payment_completed_at' => null,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 5,
                'course_id' => 3,
                'enrolled_at' => now()->subDays(5),
                'status' => 'completed',
                'progress_percentage' => 100.00,
                'total_time_spent' => 4500, // 75 minutes in seconds
                'last_accessed_at' => now()->subHours(12),
                'completed_stages_count' => 3,
                'completed_at' => now()->subHours(12),
                'payment_method' => 'credit_card',
                'payment_status' => 'paid',
                'amount_paid' => 60.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => now()->subDays(5),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subHours(12),
            ],
            [
                'user_id' => 2,
                'course_id' => 4,
                'enrolled_at' => now()->subDays(7),
                'status' => 'suspended',
                'progress_percentage' => 25.00,
                'total_time_spent' => 1200, // 20 minutes in seconds
                'last_accessed_at' => now()->subDays(3),
                'completed_stages_count' => 1,
                'completed_at' => null,
                'payment_method' => 'wallet',
                'payment_status' => 'paid',
                'amount_paid' => 85.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => now()->subDays(7),
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 3,
                'course_id' => 5,
                'enrolled_at' => now()->subDays(4),
                'status' => 'cancelled',
                'progress_percentage' => 0.00,
                'total_time_spent' => 300, // 5 minutes in seconds
                'last_accessed_at' => now()->subDays(4),
                'completed_stages_count' => 0,
                'completed_at' => null,
                'payment_method' => 'credit_card',
                'payment_status' => 'refunded',
                'amount_paid' => 40.00,
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'bank_account_id' => null,
                'payment_completed_at' => null,
                'created_at' => now()->subDays(4),
                'updated_at' => now()->subDays(4),
            ],
        ]);

        $this->command->info('Course enrollments seeded successfully!');
    }
}
