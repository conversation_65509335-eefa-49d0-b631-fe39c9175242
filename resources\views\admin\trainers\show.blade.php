@extends('admin.layout.master')

@section('content')
<section id="trainer-show" class="mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card border-0 shadow-sm rounded">
                <div class="card-header bg-white border-bottom">
                    <h4 class="mb-0 text-dark">{{ __('admin.trainer_details') }}</h4>
                </div>
                <div class="card-body" style="background-color: #f9f9f9;">

                    {{-- Section: Personal Image --}}
                    <div class="text-center mb-4">
                        <strong class="d-block mb-2">{{ __('admin.personal_image') }}</strong>
                        @if($trainer->getFirstMediaUrl('personal_image'))
                            <img src="{{ $trainer->getFirstMediaUrl('personal_image') }}" alt="Personal Image" class="img-thumbnail rounded-circle shadow" style="width: 150px; height: 150px; object-fit: cover;">
                        @else
                            <p class="text-muted">{{ __('admin.no_image') }}</p>
                        @endif
                    </div>

                    {{-- Tabs for Languages --}}
                    <section class="mb-4">
                        <ul class="nav nav-tabs" id="langTabs" role="tablist">
                            @foreach(languages() as $lang)
                                <li class="nav-item">
                                    <a class="nav-link @if($loop->first) active @endif" id="tab-{{ $lang }}" data-toggle="tab" href="#lang-{{ $lang }}" role="tab">
                                        {{ strtoupper($lang) }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                        <div class="tab-content p-3 rounded shadow-sm" style="background-color: #fdfdfd;">
                            @foreach(languages() as $lang)
                                <div class="tab-pane fade @if($loop->first) show active @endif" id="lang-{{ $lang }}" role="tabpanel">
                                    <p><strong>{{ __('admin.name') }}:</strong> {{ $trainer->getTranslation('name', $lang) }}</p>
                                    <p><strong>{{ __('admin.bio') }}:</strong> {{ $trainer->getTranslation('bio', $lang) }}</p>
                                    <p><strong>{{ __('admin.experience') }}:</strong> {{ $trainer->getTranslation('experience', $lang) }}</p>
                                </div>
                            @endforeach
                        </div>
                    </section>

                    {{-- Section: Trainer Info --}}
                    <section class="mb-4">
                        <h5 class="text-dark font-weight-bold mb-3">{{ __('admin.trainer_info') }}</h5>
                        <ul class="list-group list-group-flush rounded shadow-sm" style="background-color: #fdfdfd;">
                            <li class="list-group-item"><strong>{{ __('admin.contact_phone') }}:</strong> {{ $trainer->contact_phone }}</li>
                            <li class="list-group-item"><strong>{{ __('admin.contact_email') }}:</strong> {{ $trainer->contact_email }}</li>
                            <li class="list-group-item"><strong>{{ __('admin.contact_whatsapp') }}:</strong> {{ $trainer->contact_whatsapp }}</li>
                            <li class="list-group-item"><strong>{{ __('admin.training_price') }}:</strong> {{ $trainer->training_price }} {{ __('admin.currency') }}</li>
                            <li class="list-group-item"><strong>{{ __('admin.city') }}:</strong> {{ $trainer->city->name ?? '-' }}</li>
                            <li class="list-group-item"><strong>{{ __('admin.region') }}:</strong> {{ $trainer->region->name ?? '-' }}</li>
                            <li class="list-group-item"><strong>{{ __('admin.status') }}:</strong> 
                                <span class="badge badge-secondary">{{ __('admin.' . $trainer->status) }}</span>
                            </li>
                        </ul>
                    </section>

                    {{-- Section: Certificate Image --}}
                    <section class="mb-4">
                        <h5 class="text-dark font-weight-bold mb-3">{{ __('admin.certificate_image') }}</h5>
                        <div class="p-3 bg-white rounded shadow-sm text-center">
                            @if($trainer->getFirstMediaUrl('certificate_image'))
                                <img src="{{ $trainer->getFirstMediaUrl('certificate_image') }}" alt="Certificate" class="img-thumbnail shadow" style="max-width: 250px;">
                            @else
                                <p class="text-muted">{{ __('admin.no_image') }}</p>
                            @endif
                        </div>
                    </section>

                    {{-- Section: Works Gallery --}}
                    <section class="mb-4">
                        <h5 class="text-dark font-weight-bold mb-3">{{ __('admin.works') }}</h5>
                        <div class="p-3 bg-white rounded shadow-sm d-flex flex-wrap justify-content-start gap-2">
                            @forelse($trainer->getMedia('works') as $media)
                                <img src="{{ $media->getUrl() }}" alt="Work Image" class="img-thumbnail m-1" style="width: 100px; height: 100px; object-fit: cover;">
                            @empty
                                <p class="text-muted">{{ __('admin.no_images') }}</p>
                            @endforelse
                        </div>
                    </section>


                    {{-- Section: Ratings --}}
<section class="mb-4">
    <h5 class="text-dark font-weight-bold mb-3">{{ __('admin.ratings') }}</h5>

    {{-- Display Average Stars --}}
    {{-- <div class="mb-3">
        <strong>{{ __('admin.average_rating') }}:</strong>
        {{ round($trainer->ratings->avg('stars'), 1) ?? '-' }} / 5
    </div> --}}

    {{-- Display Ratings List --}}
    {{-- @forelse($trainer->user->ratings as $rating)
        <div class="border rounded p-3 mb-2 bg-white">
            <p><strong>{{ $rating->user->name ?? __('admin.anonymous') }}</strong></p>
            <p>{{ __('admin.rating') }}: {{ $rating->stars }} ⭐</p>
            @if($rating->comment)
                <p>{{ __('admin.comment') }}: {{ $rating->comment }}</p>
            @endif
            <p class="text-muted small">{{ $rating->created_at->diffForHumans() }}</p>
        </div>
    @empty
        <p class="text-muted">{{ __('admin.no_ratings_yet') }}</p>
    @endforelse
</section> --}}


{{-- Section: Ratings --}}
<section class="mb-4 mt-5">
    {{-- <h5 class="text-dark font-weight-bold mb-3">{{ __('admin.ratings') }}</h5> --}}

    {{-- Average rating & count --}}
    @php
        $ratings = $trainer->ratings ?? collect();
        $average = $ratings->avg('stars');
        $count = $ratings->count();
    @endphp

    @if($count > 0)
        <p><strong>{{ __('admin.average_rating') }}:</strong>
            @for($i = 1; $i <= 5; $i++)
                @if($i <= round($average)) ⭐ @else ☆ @endif
            @endfor
            ({{ number_format($average, 1) }} / 5)
        </p>
        <p><strong>{{ __('admin.rating') }}:</strong> {{ $count }} {{ __('admin.ratings') }}</p>
    @else
        <p class="text-muted">{{ __('admin.no_ratings_yet') }}</p>
    @endif

    {{-- Add Rating Button --}}
    @if(auth()->check() && !$trainer->user->ratings->where('rater_id', auth()->id())->count())
        <button class="btn btn-primary my-2" data-toggle="collapse" data-target="#addRatingForm">
            {{ __('admin.add_rating') }}
        </button>

        {{-- Add Rating Form --}}
        {{-- <div id="addRatingForm" class="collapse mt-3">
            <form method="POST" action="{{ route('ratings.store') }}">
                @csrf
                <input type="hidden" name="ratee_id" value="{{ $trainer->user_id }}">

                <div class="form-group">
                    <label for="stars">{{ __('admin.rating') }}</label>
                    <select name="stars" id="stars" class="form-control" required>
                        <option value="">-- {{ __('admin.choose_rating') }} --</option>
                        @for($i = 1; $i <= 5; $i++)
                            <option value="{{ $i }}">{{ $i }} ⭐</option>
                        @endfor
                    </select>
                </div>

                <div class="form-group">
                    <label for="comment">{{ __('admin.comment') }}</label>
                    <textarea name="comment" id="comment" class="form-control" minlength="5" maxlength="100" required></textarea>
                </div>

                <button type="submit" class="btn btn-success">{{ __('admin.send') }}</button>
                <button type="button" class="btn btn-secondary" data-toggle="collapse" data-target="#addRatingForm">{{ __('admin.cancel') }}</button>
            </form>
        </div> --}}
    @endif

    {{-- Ratings List --}}
    <div class="mt-4">
    @forelse($ratings as $rating)
        <div class="border p-3 rounded mb-3 bg-white shadow-sm">
            <div class="d-flex justify-content-between">
                <strong>{{ $rating->rater?->name ?? __('admin.anonymous') }}</strong>
                <small class="text-muted">{{ $rating->created_at->format('Y-m-d H:i') }}</small>
            </div>

            <div class="my-1">
                @for($i = 1; $i <= 5; $i++)
                    @if($i <= $rating->stars) ⭐ @else ☆ @endif
                @endfor
            </div>

            <p class="mb-2">{{ $rating->comment }}</p>

            {{-- عرض الحالة الحالية وزر القبول/الرفض إذا الحالة "pending" --}}
            <div class="mt-2 d-flex align-items-center justify-content-between">
                <span>
                    <strong>{{ __('admin.status') }}:</strong>
                    <span class="badge 
                        @if($rating->status == 'pending') badge-warning 
                        @elseif($rating->status == 'approved') badge-success 
                        @else badge-danger 
                        @endif">
                        {{ __('admin.' . $rating->status) }}
                    </span>
                </span>

                @if($rating->status === 'pending')
                    <div>
                        {{-- زر الموافقة --}}
                        <button class="btn btn-sm btn-success swal-confirm"
                            data-id="{{ $rating->id }}"
                            data-status="approved"
                            data-message="هل أنت متأكد من قبول هذا التقييم؟">
                            ✔ {{ __('admin.approve') }}
                        </button>

                        {{-- زر الرفض --}}
                        <button class="btn btn-sm btn-danger swal-confirm ml-1"
                            data-id="{{ $rating->id }}"
                            data-status="rejected"
                            data-message="هل أنت متأكد من رفض هذا التقييم؟">
                            ✖ {{ __('admin.reject') }}
                        </button>
                    </div>
                @endif
            </div>
        </div>
    @empty
        <p class="text-muted">{{ __('admin.no_ratings_yet') }}</p>
    @endforelse
</div>

</section>




                    

                    {{-- Back Button --}}
                    <div class="text-center mt-4">
                        <a href="{{ route('admin.trainers.index') }}" class="btn btn-dark">
                            <i class="feather icon-arrow-left"></i> {{ __('admin.back') }}
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>
@endsection


{{-- SweetAlert2 --}}
@section('js')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.swal-confirm').forEach(function (button) {
            button.addEventListener('click', function (e) {
                e.preventDefault();

                let ratingId = this.dataset.id;
                let status = this.dataset.status;
                let message = this.dataset.message;

                Swal.fire({
                    title: 'تأكيد العملية',
                    text: message,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، تأكيد',
                    cancelButtonText: 'إلغاء',
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#d33',
                }).then((result) => {
                    if (result.isConfirmed) {
                        let form = document.createElement('form');
                        form.method = 'POST';
                        form.action = `/admin/ratings/${ratingId}/status`;

                        let csrf = document.createElement('input');
                        csrf.type = 'hidden';
                        csrf.name = '_token';
                        csrf.value = '{{ csrf_token() }}';
                        form.appendChild(csrf);

                        let method = document.createElement('input');
                        method.type = 'hidden';
                        method.name = '_method';
                        method.value = 'PUT';
                        form.appendChild(method);

                        let input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'status';
                        input.value = status;
                        form.appendChild(input);

                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            });
        });
    });
</script>
@endsection
