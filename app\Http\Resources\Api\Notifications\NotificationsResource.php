<?php

namespace App\Http\Resources\Api\Notifications;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationsResource extends JsonResource
{
    public function toArray($request)
    {
        $data = $this->data; // البيانات المخزنة من toArray()

        return [
            'id'         => $this->id,
            'type'       => $data['type'] ?? 'unknown',
            'title'      => $data['title'] ?? __('apis.notification_type_not_defined'),
            'body'       => $data['body'] ?? __('apis.notification_type_not_defined'),
            'created_at' => $this->created_at->diffForHumans(),
        ];
    }
}

