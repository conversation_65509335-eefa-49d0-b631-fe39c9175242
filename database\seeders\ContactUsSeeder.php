<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ContactUsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Contact us messages from users
        
        DB::table('contact_us')->insert([
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+96512345678',
                'subject' => 'Service Inquiry',
                'message' => 'I would like to know more about your hair styling services and pricing.',
                'status' => 'pending',
                'admin_reply' => null,
                'replied_at' => null,
                'replied_by' => null,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'name' => 'Fatima Al-Zahra',
                'email' => '<EMAIL>',
                'phone' => '+96587654321',
                'subject' => 'Booking Issue',
                'message' => 'I am having trouble booking an appointment through the app. Can you help?',
                'status' => 'replied',
                'admin_reply' => 'Thank you for contacting us. We have resolved the booking issue. Please try again.',
                'replied_at' => now()->subDays(1),
                'replied_by' => 1,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(1),
            ],
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'phone' => '+96555123456',
                'subject' => 'Product Quality',
                'message' => 'I received a damaged product in my last order. How can I get a replacement?',
                'status' => 'replied',
                'admin_reply' => 'We apologize for the inconvenience. A replacement has been sent to your address.',
                'replied_at' => now()->subHours(6),
                'replied_by' => 1,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(6),
            ],
            [
                'name' => 'Mohammad Al-Ahmad',
                'email' => '<EMAIL>',
                'phone' => '+96599887766',
                'subject' => 'Partnership Inquiry',
                'message' => 'I am a beauty professional interested in joining your platform. What are the requirements?',
                'status' => 'pending',
                'admin_reply' => null,
                'replied_at' => null,
                'replied_by' => null,
                'created_at' => now()->subHours(12),
                'updated_at' => now()->subHours(12),
            ],
            [
                'name' => 'Layla Al-Mansouri',
                'email' => '<EMAIL>',
                'phone' => '+96566778899',
                'subject' => 'App Feedback',
                'message' => 'The app is great! I love the easy booking system. Keep up the good work!',
                'status' => 'replied',
                'admin_reply' => 'Thank you for your positive feedback! We are glad you enjoy using our app.',
                'replied_at' => now()->subHours(2),
                'replied_by' => 1,
                'created_at' => now()->subHours(4),
                'updated_at' => now()->subHours(2),
            ],
        ]);

        $this->command->info('Contact us messages seeded successfully!');
    }
}
