<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Provider ratings (legacy table - will be migrated to polymorphic rates)
        
        DB::table('provider_rates')->insert([
            [
                'user_id' => 1,
                'provider_id' => 1,
                'rate' => 5,
                'body' => '<PERSON> is an amazing hair stylist! Very professional and skilled. Highly recommend her services.',
                'created_at' => now()->subDays(12),
                'updated_at' => now()->subDays(12),
            ],
            [
                'user_id' => 2,
                'provider_id' => 1,
                'rate' => 4,
                'body' => 'Good provider with nice skills. The salon is clean and well-maintained.',
                'created_at' => now()->subDays(9),
                'updated_at' => now()->subDays(9),
            ],
            [
                'user_id' => 3,
                'provider_id' => 2,
                'rate' => 5,
                'body' => '<PERSON> is excellent at nail art! Very creative and attention to detail is amazing.',
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(7),
            ],
            [
                'user_id' => 1,
                'provider_id' => 2,
                'rate' => 5,
                'body' => 'Best nail technician in Kuwait! Always delivers perfect results.',
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'user_id' => 4,
                'provider_id' => 3,
                'rate' => 4,
                'body' => 'Fatima is very knowledgeable about skincare. Great facial treatments.',
                'created_at' => now()->subDays(4),
                'updated_at' => now()->subDays(4),
            ],
            [
                'user_id' => 2,
                'provider_id' => 3,
                'rate' => 5,
                'body' => 'Amazing skincare specialist! My skin has never looked better.',
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 5,
                'provider_id' => 4,
                'rate' => 5,
                'body' => 'Layla is a makeup artist extraordinaire! Made me look like a princess.',
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'user_id' => 3,
                'provider_id' => 4,
                'rate' => 4,
                'body' => 'Very talented makeup artist. Professional and friendly service.',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => 1,
                'provider_id' => 5,
                'rate' => 4,
                'body' => 'Good eyebrow shaping service. Quick and efficient.',
                'created_at' => now()->subHours(18),
                'updated_at' => now()->subHours(18),
            ],
            [
                'user_id' => 4,
                'provider_id' => 5,
                'rate' => 5,
                'body' => 'Perfect eyebrow threading! Very precise and professional.',
                'created_at' => now()->subHours(8),
                'updated_at' => now()->subHours(8),
            ],
        ]);

        $this->command->info('Provider rates seeded successfully!');
    }
}
