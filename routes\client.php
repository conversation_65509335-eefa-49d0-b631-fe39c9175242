<?php

use App\Http\Controllers\Api\Client\AdController;
use App\Http\Controllers\Api\Client\ConversationController;
use App\Http\Controllers\Api\Client\PurchaseRequestController;
use App\Http\Controllers\Api\FollowController;
use App\Http\Controllers\Api\RatingController;
use App\Http\Controllers\Api\TrainerApplicationController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Client\AuthController;
use App\Http\Controllers\Api\Client\CartController;
use App\Http\Controllers\Api\Client\RateController;
use App\Http\Controllers\Api\Clinet\BlogController;
use App\Http\Controllers\Api\Client\OrderController;
use App\Http\Controllers\Api\Client\CourseController;
use App\Http\Controllers\Api\Client\AddressController;
use App\Http\Controllers\Api\Client\ProductController;
use App\Http\Controllers\Api\Client\CategoryController;
use App\Http\Controllers\Api\Client\FavoriteController;
use App\Http\Controllers\Api\Client\ProviderController;
use App\Http\Controllers\Api\Client\OrderRateController;
use App\Http\Controllers\Api\Client\OrderReportController;
use App\Http\Controllers\Api\Clinet\UserAffiliateController;
use App\Http\Controllers\Api\Client\CourseProgressController;
use App\Http\Controllers\Api\Clinet\ReferralLinkController;

Route::group([
    'namespace'  => 'Api',
    'middleware' => ['api-cors', 'api-lang'],
    'prefix'     => 'client',
], function () {

    

    Route::group(['middleware' => ['guest']], function () {
        /***************************** AuthController Start *****************************/
        Route::post('register', [AuthController::class, 'register'])->name('client.auth.register');
        Route::post('attempt-login', [AuthController::class, 'login'])->name('client.auth.login');
        Route::post('verify-login-code', [AuthController::class, 'verifyLoginCode'])->name('client.auth.verify-login-code');
/***************************** AuthController End *****************************/

/***************************** CategoryController start *****************************/

// Route::middleware('auth:sanctum')->group(function () {
// Route::resource('ads', AdController::class);
// });     



        Route::get('categories', [CategoryController::class, 'index'])->name('client.categories.index');
        Route::get('categories/{id}', [CategoryController::class, 'show'])->name('client.categories.show');
        /***************************** CategoryController End *****************************/
    });

    Route::group(['middleware' => ['auth:sanctum', 'is-active']], function () {
        /***************************** AuthController Start *****************************/
    
        /***************************** AuthController End *****************************/
    Route::post('follow/{user}', [FollowController::class, 'toggle']);
    Route::get('profile/{user}', [FollowController::class, 'profile']);


Route::prefix('ads')->group(function () {
    Route::get('my-ads', [AdController::class, 'myAds']);
    Route::get('others', [AdController::class, 'otherAds']);
});

Route::apiResource('ads', AdController::class);      
  Route::apiResource('trainer-applications', TrainerApplicationController::class);
        Route::post('ratings', [RatingController::class, 'store']);
        // Route::apiResource('purchase-requests', PurchaseRequestController::class);
        Route::get('purchase-requests/buyer', [PurchaseRequestController::class, 'getAsBuyer'])->name('client.purchase-requests.buyer') ;
        Route::get('purchase-requests/seller', [PurchaseRequestController::class, 'getAsSeller']);
        Route::apiResource('purchase-requests', PurchaseRequestController::class)->except(['index']);
        //withdrawals
        Route::get('withdrawals', [\App\Http\Controllers\Api\Client\WithdrawalController::class, 'index'])->name('client.withdrawals.index');
        Route::post('withdrawals', [\App\Http\Controllers\Api\Client\WithdrawalController::class, 'store'])->name('client.withdrawals.store');
        Route::delete('withdrawals/{id}', [\App\Http\Controllers\Api\Client\WithdrawalController::class, 'cancel'])->name('client.withdrawals.cancel');



Route::get('conversations', [ConversationController::class, 'index']);

Route::post('conversations/messages', [ConversationController::class, 'store']);

Route::get('conversations/{conversation_id}/messages', [ConversationController::class, 'getMessages']);



Route::prefix('purchase-requests')->middleware('auth:sanctum')->group(function () {
    Route::post('{id}/seller-confirm', [\App\Http\Controllers\Api\Client\PurchaseRequestController::class, 'sellerConfirm']);
    Route::post('{id}/buyer-confirm', [\App\Http\Controllers\Api\Client\PurchaseRequestController::class, 'buyerConfirm']);
    Route::post('{id}/report-problem', [\App\Http\Controllers\Api\Client\PurchaseRequestController::class, 'reportProblem']);
    Route::post('{id}/cancel-by-admin', [\App\Http\Controllers\Api\Client\PurchaseRequestController::class, 'cancelByAdmin']);
});





        /***************************** AddressController start *****************************/
        Route::apiResource('addresses', AddressController::class, [
            'names' => [
                'index' => 'client.addresses.index',
                'store' => 'client.addresses.store',
                'show' => 'client.addresses.show',
                'update' => 'client.addresses.update',
                'destroy' => 'client.addresses.destroy',
            ]
        ]);

        /***************************** FavoriteController start *****************************/
        Route::get('favorites', [FavoriteController::class, 'index'])->name('client.favorites.index');
        Route::post('favorites/toggle', [FavoriteController::class, 'toggle'])->name('client.favorites.toggle');
        Route::get('favorites/check', [FavoriteController::class, 'check'])->name('client.favorites.check');
        /***************************** FavoriteController end *****************************/
        /***************************** AddressController end *****************************/

        /***************************** CartController start *****************************/
        Route::get('cart', [CartController::class, 'getCart'])->name('client.cart.get');
        Route::post('cart/add', [CartController::class, 'addToCart'])->name('client.cart.add');
        Route::put('cart/update', [CartController::class, 'updateCartItem'])->name('client.cart.update');
        Route::delete('cart/remove', [CartController::class, 'removeFromCart'])->name('client.cart.remove');
        Route::delete('cart/clear', [CartController::class, 'clearCart'])->name('client.cart.clear');

        // Coupon and loyalty points
        Route::post('cart/apply-coupon', [CartController::class, 'applyCoupon'])->name('client.cart.apply-coupon');
        Route::delete('cart/remove-coupon', [CartController::class, 'removeCoupon'])->name('client.cart.remove-coupon');
        Route::post('cart/apply-loyalty-points', [CartController::class, 'applyLoyaltyPoints'])->name('client.cart.apply-loyalty-points');
        Route::delete('cart/remove-loyalty-points', [CartController::class, 'removeLoyaltyPoints'])->name('client.cart.remove-loyalty-points');

        // Fee breakdown
        Route::get('cart/fee-breakdown', [CartController::class, 'getFeeBreakdown'])->name('client.cart.fee-breakdown');
        Route::post('cart/apply-fees', [CartController::class, 'applyFees'])->name('client.cart.apply-fees');
        /***************************** CartController end *****************************/

     
    
        /***************************** OrderController end *****************************/

        /***************************** OrderRateController start *****************************/
        Route::post('orders/rate', action: [OrderRateController::class, 'store'])->name('client.orders.store');
        /***************************** OrderRateController end *****************************/

        /***************************** OrderReportController start *****************************/
        Route::post('orders/report', action: [OrderReportController::class, 'store'])->name('client.orders.store');
        /***************************** OrderReportController end *****************************/
        /***************************** ProviderController start *****************************/

        Route::get('providers/{cityId}' , [ProviderController::class , 'getProviderWithCity']);
        Route::get('provider/suggestions' , [ProviderController::class , 'getProvidersForUser']);
        Route::get('provider/most-ordered' , [ProviderController::class , 'getMostOrderedProviders']);
        Route::get('provider/filter' , [ProviderController::class , 'filterProviders']);
        Route::get('provider/{id}' , [ProviderController::class , 'show']);
        /***************************** ProviderController start *****************************/
        /***************************** ProviderRateController start *****************************/
        Route::post('rates/store' , [RateController::class , 'store']);
        /***************************** ProviderRateController end *****************************/
        /***************************** ProductController start *****************************/
        Route::get('products' , [ProductController::class , 'index'])->name('products.index');
        Route::get('products/{id}' , [ProductController::class , 'show'])->name('products.show');
        /***************************** ProductController end *****************************/
                /***************************** CourseController start *****************************/
        Route::get('courses' , [CourseController::class, 'index'])->name('client.courses.index');
        Route::get('courses/{id}' , [CourseController::class, 'show'])->name('client.courses.show');
        Route::post('courses/{id}/enroll', [CourseController::class, 'enroll'])->name('client.courses.enroll');
        Route::get('courses/{id}/payment-gateways', [CourseController::class, 'paymentGateways'])->name('client.courses.payment-gateways');
        Route::get('my-courses', [CourseController::class, 'myEnrollments'])->name('client.courses.my-enrollments');
        Route::post('course-enrollments/{enrollmentId}/confirm-payment', [CourseController::class, 'confirmPayment'])->name('client.courses.confirm-payment');
        /***************************** CourseController end *****************************/

        /***************************** CourseProgressController start *****************************/
        Route::post('courses/{courseId}/stages/{stageId}/update-watch-time', [CourseProgressController::class, 'updateWatchTime'])->name('client.courses.stages.update-watch-time');
        /***************************** CourseProgressController end *****************************/

        
    });

    // MyFatoorah Payment Callbacks (public routes - no authentication required)

    // Course Payment Callbacks
  

    // Order Payment Callbacks
    // Route::get('latest', [AdController::class, 'latestAds']); // آخر الإعلانات
    // Route::get('{id}', [AdController::class, 'show']); // تفاصيل الإعلان
    // Route::post('{id}/favorite', [AdController::class, 'toggleFavorite']); // إدارة المفضلة
});
