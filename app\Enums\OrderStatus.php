<?php

namespace App\Enums;

enum OrderStatus: string
{
    case PENDING_PAYMENT = 'pending_payment';
    case PROCESSING = 'processing';
    case CONFIRMED = 'confirmed';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';
    case PARTIALLY_CANCELLED = 'partially_cancelled';
    case PENDING_VERIFICATION = 'pending_verification';
    case REQUEST_CANCEL = 'request_cancel';

    /**
     * Get all status values
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get status label
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING_PAYMENT => 'Pending Payment',
            self::PROCESSING => 'Processing',
            self::CONFIRMED => 'Confirmed',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
            self::PARTIALLY_CANCELLED => 'Partially Cancelled',
            self::PENDING_VERIFICATION => 'Pending Verification',
            self::REQUEST_CANCEL => 'Request Cancel',
        };
    }

    /**
     * Get status color for UI
     */
    public function color(): string
    {
        return match($this) {
            self::PENDING_PAYMENT => 'warning',
            self::PROCESSING => 'info',
            self::CONFIRMED => 'primary',
            self::COMPLETED => 'success',
            self::CANCELLED => 'danger',
            self::PARTIALLY_CANCELLED => 'warning',
            self::PENDING_VERIFICATION => 'secondary',
            self::REQUEST_CANCEL => 'warning',
        };
    }

    /**
     * Get status icon
     */
    public function icon(): string
    {
        return match($this) {
            self::PENDING_PAYMENT => 'clock',
            self::PROCESSING => 'refresh-cw',
            self::CONFIRMED => 'check-circle',
            self::COMPLETED => 'check-circle-2',
            self::CANCELLED => 'x-circle',
            self::PARTIALLY_CANCELLED => 'alert-circle',
            self::PENDING_VERIFICATION => 'help-circle',
            self::REQUEST_CANCEL => 'alert-triangle',
        };
    }

    /**
     * Check if status can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this, [
            self::PENDING_PAYMENT,
            self::PROCESSING,
            self::CONFIRMED,
        ]);
    }

    /**
     * Check if status is final
     */
    public function isFinal(): bool
    {
        return in_array($this, [
            self::COMPLETED,
            self::CANCELLED,
        ]);
    }

    /**
     * Get next possible statuses
     */
    public function nextStatuses(): array
    {
        return match($this) {
            self::PENDING_PAYMENT => [self::PROCESSING, self::CANCELLED],
            self::PROCESSING => [self::CONFIRMED, self::COMPLETED, self::CANCELLED],
            self::CONFIRMED => [self::COMPLETED, self::CANCELLED],
            self::COMPLETED => [],
            self::CANCELLED => [],
            self::PARTIALLY_CANCELLED => [self::CANCELLED],
            self::PENDING_VERIFICATION => [self::PROCESSING, self::CANCELLED],
        };
    }
}

