<?php

namespace App\Http\Resources\Api\Client;

use Illuminate\Http\Request;
use App\Http\Resources\Api\OrderRateResource;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        // Get payment method details
        $paymentMethod = \App\Models\PaymentMethod::find($this->payment_method_id);

        return [
            // Basic order information
            'id' => $this->id,
            'order_number' => $this->order_number,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'current_status' => $this->current_status,
            'payment_status' => $this->payment_status,
            'is_multi_provider' => $this->hasMultipleProviders(),

            // Provider sub-orders information
            'provider_orders' => $this->whenLoaded('providerSubOrders', function() {
                return $this->providerSubOrders->map(function($subOrder) {
                    return [
                        'id' => $subOrder->id,
                        'sub_order_number' => $subOrder->sub_order_number,
                        'status' => $subOrder->status,
                        'provider' => [
                            'id' => $subOrder->provider->id,
                            'name' => $subOrder->provider->commercial_name ?? $subOrder->provider->user->name,
                            'commercial_name' => $subOrder->provider->commercial_name,
                            'phone' => $subOrder->provider->user->phone,
                            'city' => $subOrder->provider->user->city->name ?? null,
                            'type' => $this->getProviderType($subOrder->provider),
                        ],
                        'items' => $subOrder->orderItems->map(function($item) {
                            return [
                                'name' => $item->name,
                                'type' => $item->item_type === 'App\Models\Service' ? 'service' : 'product',
                                'price' => (float) $item->price,
                                'quantity' => (int) $item->quantity,
                                'total' => (float) $item->total,
                                'options' => $item->options,
                                'item_details' => $this->getItemResource($item),
                            ];
                        }),
                        'location' => $this->getProviderLocation($subOrder->provider),
                        'totals' => [
                            'services_total' => (float) $subOrder->services_total,
                            'products_total' => (float) $subOrder->products_total,
                            'subtotal' => (float) $subOrder->subtotal,
                            'booking_fee' => (float) $subOrder->booking_fee,
                            'home_service_fee' => (float) $subOrder->home_service_fee,
                            'delivery_fee' => (float) $subOrder->delivery_fee,
                            'total' => (float) $subOrder->total,
                        ],
                    ];
                });
            }),

            // Address information
            'address' => $this->whenLoaded('address', function() {
                return [
                    'id' => $this->address->id,
                    'details' => $this->address->details,
                    'city' => $this->address->city->name ?? null,
                    'is_default' => $this->address->is_default,
                ];
            }),

            // Location information
            'booking_type' => $this->booking_type,
            'delivery_type' => $this->delivery_type,
            'scheduled_at' => $this->scheduled_at ? $this->scheduled_at->format('Y-m-d H:i:s') : null,

            // Order items (all items grouped)
            'all_items' => $this->whenLoaded('items', function() {
                return $this->items->map(function($item) {
                    return [
                        'name' => $item->name,
                        'type' => $item->item_type === 'App\Models\Service' ? 'service' : 'product',
                        'price' => (float) $item->price,
                        'quantity' => (int) $item->quantity,
                        'total' => (float) $item->total,
                        'options' => $item->options,
                        'item_details' => $this->getItemResource($item),
                    ];
                });
            }),

            // Cost breakdown
            'cost_details' => [
                'subtotal' => (float) $this->subtotal,
                'services_total' => (float) $this->services_total,
                'products_total' => (float) $this->products_total,
                'discount' => $this->when($this->discount_amount > 0, [
                    'coupon_id' => $this->coupon_id,
                    'code' => $this->whenLoaded('coupon', $this->coupon->coupon_num ?? null),
                    'percentage' => (float) $this->discount_percentage,
                    'amount' => (float) $this->discount_amount,
                ]),
                'fees' => [
                    'booking_fee' => (float) $this->booking_fee,
                    'home_service_fee' => $this->when($this->booking_type === 'home', (float) $this->home_service_fee),
                    'delivery_fee' => $this->when($this->delivery_type, (float) $this->delivery_fee),
                ],
                'loyalty_points_used' => $this->when($this->loyalty_points_used > 0, (int) $this->loyalty_points_used),
                'loyalty_points_earned' => (int) $this->loyalty_points_earned,
                'final_total' => (float) $this->total,
            ],

            // Payment details
            'payment_details' => [
                'payment_method_id' => $this->payment_method_id,
                'payment_method' => $paymentMethod ? $paymentMethod->name : 'Unknown',
                'payment_status' => $this->payment_status,
                'amount_paid' => (float) ($this->total ?? 0),
                'payment_date' => $this->payment_date ? $this->payment_date->format('Y-m-d H:i:s') : null,
                'payment_reference' => $this->payment_reference,
                'invoice_number' => $this->invoice_number,
                'bank_transfer_details' => $this->when($this->payment_method_id === 5 && $this->bankTransfer, [
                    'bank_name' => $this->bankTransfer?->sender_bank_name,
                    'account_holder' => $this->bankTransfer?->sender_account_holder_name,
                    'account_number' => $this->bankTransfer?->sender_account_number,
                    'iban' => $this->bankTransfer?->sender_iban,
                    'transfer_amount' => (float) $this->bankTransfer?->transfer_amount,
                    'transfer_date' => $this->bankTransfer?->transfer_date ? $this->bankTransfer?->transfer_date->format('Y-m-d H:i:s') : null,
                    'status' => $this->bankTransfer?->status,
                ]),
            ],

'status_history' => $this->statusChanges->map(function ($status) {
    return [
        'status' => $status->status,
        'created_at' => $status->created_at->format('Y-m-d H:i:s'),
        'created_at_formatted' => $status->created_at->format('d M Y, h:i A'),
        'description' => $status->map_desc ?? ucfirst(str_replace('_', ' ', $status->status)),
    ];
}),

            'rate' => $this->when($this->current_status == 'completed' , OrderRateResource::make($this->rate))
        ];
    }

    /**
     * Get provider type description
     */
    private function getProviderType($provider)
    {
        if ($provider->in_home && $provider->in_salon) {
            return 'home_and_salon';
        } elseif ($provider->in_home) {
            return 'home_service';
        } elseif ($provider->in_salon) {
            return 'salon_service';
        }
        return 'unknown';
    }

    /**
     * Get provider location information
     */
    private function getProviderLocation($provider)
    {
        $location = [];

        if ($this->booking_type === 'salon' && $provider->lat && $provider->lng) {
            $location['salon'] = [
                'latitude' => (float) $provider->lat,
                'longitude' => (float) $provider->lng,
                'address' => $provider->address ?? null,
            ];
        }

        if ($this->booking_type === 'home' && $this->address) {
            $location['home'] = [
                'address_details' => $this->address->details,
                'city' => $this->address->city->name ?? null,
            ];
        }

        return $location;
    }

    /**
     * Get item resource (ProductResource or ServiceResource)
     */
    private function getItemResource($item)
    {
        if (!$item->item) {
            return null;
        }

        if ($item->item_type === 'App\Models\Product') {
            return new ProductResource($item->item);
        } elseif ($item->item_type === 'App\Models\Service') {
            return new ServiceResource($item->item);
        }

        return null;
    }
}
