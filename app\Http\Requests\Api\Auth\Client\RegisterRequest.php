<?php

namespace App\Http\Requests\Api\Auth\Client;

use App\Http\Requests\Api\BaseApiRequest;
use App\Rules\ClientPhoneUnique;
use Illuminate\Http\Request;

class RegisterRequest extends BaseApiRequest {

  public function __construct(Request $request) {
    $request['phone']        = fixPhone($request['phone']);
    $request['country_code'] = fixPhone($request['country_code']);
  }

  public function rules() {
    return [
      'name'         => 'required|min:5|max:20',
      'country_code' => 'required|numeric|digits_between:2,5',
      'phone'        => [
                'required',
                'phone:SA',
                'unique:users,phone,NULL,id,deleted_at,NULL'
            ],
      'email'        => 'nullable|email|unique:users,email,NULL,id,deleted_at,NULL|max:50',
      'password'     => 'required|min:6|max:100|confirmed',
      'image'        => 'nullable',
      'gender' => 'required|in:male,female',
      'city_id'      => 'nullable|exists:cities,id',
      'region_id'      => 'nullable|exists:regions,id',

    ];
  }

  public function messages() {
    return [
      'phone.phone' => __('validation.phone_format'),
    ];
  }

}

