<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Services\ProductService;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Client\ProductResource;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * @var ProductService
     */
    protected $productService;

    /**
     * ProductController constructor.
     *
     * @param ProductService $productService
     */
    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Get all products with filtering and pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = [
                'category_id' => $request->input('category_id'), // Filter by product category
            ];

            $products = $this->productService->getAllProducts($filters);

            return Responder::success(ProductResource::collection($products));

        } catch (\Exception $e) {
            return Responder::error('Failed to fetch products', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get a specific product by ID
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(int $id)
    {
        try {
            $product = $this->productService->getProductById($id);

            if (!$product) {
                return Responder::error(__('apis.product_not_found'), [], 404);
            }

            return Responder::success(new ProductResource($product));

        } catch (\Exception $e) {
            return Responder::error('Failed to fetch product', ['error' => $e->getMessage()], 500);
        }
    }
}
