<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\orders\Store;
use App\Http\Requests\orders\Update;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\ProviderSubOrder;
use App\Traits\Report;

class OrderController extends Controller
{
    /**
     * Display the orders dashboard with tabs
     */
    public function dashboard()
    {
        return view('admin.orders.dashboard');
    }

    public function index($id = null)
    {
        if (request()->ajax()) {
            $orders = Order::with(['user', 'provider', 'address', 'bankTransfer', 'paymentMethod'])
                ->search(request()->searchArray)
                ->paginate(30);
            $html = view('admin.orders.table', compact('orders'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.orders.index');
    }

    public function create()
    {
        return view('admin.orders.create');
    }

    public function store(Store $request)
    {
        $order = Order::create($request->validated());

        // Log success
        Report::addToLog('اضافة منتج');

        return response()->json(['url' => route('admin.orders.index')]);
    }

    public function edit($id)
    {
        $order = Order::findOrFail($id);
        return view('admin.orders.edit', ['order' => $order]);
    }

    public function update(Update $request, $id)
    {
        $order = Order::findOrFail($id);
        $order->update($request->validated());

        // Log success
        Report::addToLog('تعديل منتج');

        return response()->json(['url' => route('admin.orders.index')]);
    }

    public function show($id)
    {
        $order = Order::with([
            'user',
            'items.item',
            'coupon',
            'bankTransfer',
            'providerSubOrders' => function($query) {
                $query->with([
                    'provider.user',
                    'statusChanges' => function($statusQuery) {
                        $statusQuery->with('statusable')->orderBy('created_at', 'desc');
                    },
                    'orderItems' => function($itemsQuery) {
                        $itemsQuery->with(['item', 'service', 'product']);
                    }
                ]);
            }
        ])->findOrFail($id);

        // Get payment method details
        $paymentMethod = \App\Models\PaymentMethod::find($order->payment_method_id);

        return view('admin.orders.show', compact('order', 'paymentMethod'));
    }

    public function destroy($id)
    {
        Order::findOrFail($id)->delete();
        Report::addToLog('حذف منتج');
        return response()->json(['id' => $id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        $ids = collect($requestIds)->pluck('id')->toArray();

        if (Order::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('حذف العديد من المنتجات');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $order = Order::findOrFail($id);
            $newStatus = $request->input('status');
            $mapDesc = $request->input('map_desc', null);

            // Validate status using enum
            $allowedStatuses = \App\Enums\OrderStatus::values();
            if (!in_array($newStatus, $allowedStatuses)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid status provided'
                ], 400);
            }

            // Get current admin
            $admin = auth()->guard('admin')->user();

            // Update main order status
            $order->update([
                'current_status' => $newStatus
            ]);

            // Update all provider sub-orders to the same status
            foreach ($order->providerSubOrders as $subOrder) {
                $subOrder->updateStatus(
                    $newStatus,
                    'App\Models\Admin',
                    $admin->id,
                    $mapDesc
                );
            }

            // Log the action
            Report::addToLog("تحديث حالة الطلب رقم {$order->order_number} إلى {$newStatus}");

            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order status: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * Mark payment as paid for bank transfer orders
     */
    public function markPaymentAsPaid($id)
    {
        try {
            $order = Order::findOrFail($id);

            // Validate that this is a bank transfer order (ID 5 = Bank Transfer)
            if ($order->payment_method_id !== \App\Enums\PaymentMethod::BANK_TRANSFER->value) {
                return response()->json([
                    'success' => false,
                    'message' => 'This action is only available for bank transfer orders'
                ], 400);
            }

            // Validate current status
            if ($order->payment_status !== \App\Enums\PaymentStatus::PENDING->value) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment has already been processed'
                ], 400);
            }

            $admin = auth()->guard('admin')->user();

            // Update payment status, paid amount, and payment date
            $order->update([
                'payment_status' => \App\Enums\PaymentStatus::SUCCESS->value,
                'amount_paid' => $order->total,
                'current_status' => \App\Enums\OrderStatus::PROCESSING->value,
            ]);

            // Update all provider sub-orders to processing and create status records
            foreach ($order->providerSubOrders as $subOrder) {
                $subOrder->updateStatus(
                    \App\Enums\OrderStatus::PROCESSING->value,
                    'App\Models\Admin',
                    $admin->id
                );
            }

            // Create main order status record
            OrderStatus::createOrderStatusChange(
                $order->id,
                \App\Enums\OrderStatus::PROCESSING->value,
                'App\Models\Admin',
                $admin->id,
                "Payment marked as paid by admin. Amount: {$order->total} SAR"
            );

            // Log the action
            Report::addToLog("تم تحديد الدفع كمدفوع للطلب رقم {$order->order_number} بمبلغ {$order->total} ريال");

            return response()->json([
                'success' => true,
                'message' => 'Payment marked as paid successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark payment as paid: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update provider sub-order status
     */
    public function updateProviderStatus(Request $request, $orderId, $providerId)
    {
        try {
            $order = Order::findOrFail($orderId);
            $providerSubOrder = $order->getProviderSubOrder($providerId);

            if (!$providerSubOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Provider sub-order not found for this order'
                ], 404);
            }

            $newStatus = $request->input('status');

            $admin = auth('admin')->user();

            // Update provider sub-order status
            $providerSubOrder->updateStatus(
                $newStatus,
                'App\Models\Admin',
                $admin->id
            );

            // Log the action
            Report::addToLog("تم تحديث حالة المزود {$providerSubOrder->provider->name} للطلب رقم {$order->order_number} إلى {$newStatus}");

            return response()->json([
                'success' => true,
                'message' => 'Provider status updated successfully',
                'order_status' => $order->fresh()->status,
                'sub_order_number' => $providerSubOrder->sub_order_number
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update provider status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore product quantities when order is cancelled
     */
    private function restoreProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->item_type === 'App\Models\Product') {
                $product = \App\Models\Product::find($orderItem->item_id);
                if ($product) {
                    $product->increment('quantity', $orderItem->quantity);
                }
            }
        }
    }
}
