<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class TrainerApplication extends  BaseModel implements HasMedia
{
    use HasFactory, HasTranslations, InteractsWithMedia;

    protected $fillable = [
        'user_id',
        'name',
        'bio',
        'experience',
        'region_id',
        'city_id',
        'training_price',
        'contact_phone',
        'contact_email',
        'contact_whatsapp',
        'status'
    ];

    public $translatable = ['name', 'bio', 'experience'];


    protected $casts = [
        'training_price' => 'decimal:2',
    ];

    // Relationships


    public function ratings()
{
    return $this->hasMany(Rating::class, 'trainer_id');
        // ->where('status', 'approved');
}

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function city()
{
    return $this->belongsTo(City::class);
}

public function region()
{
    return $this->belongsTo(Region::class);
}
}