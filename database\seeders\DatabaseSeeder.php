<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{

    public function run()
    {
        // Phase 1: Core settings and basic data
            $this->call(SettingSeeder::class);
            $this->call(RolesTableSeeder::class);
            $this->call(PermissionTableSeeder::class);
            $this->call(SocialTableSeeder::class);
            $this->call(CountryTableSeeder::class);
            $this->call(RegionTableSeeder::class);
            $this->call(CityTableSeeder::class);

        
            // Phase 3: Content and categories
            // $this->call(FqsTableSeeder::class);
            // $this->call(IntroTableSeeder::class);
            $this->call(CategoryTableSeeder::class);
            $this->call(ProductCategoryTableSeeder::class);
            // $this->call(CouponTableSeeder::class);
            $this->call(SmsTableSeeder::class);
            // $this->call(CancelReasonSeeder::class);
            // $this->call(DeliveryPeriodSeeder::class);
            $this->call(PaymentMethodSeeder::class);

            // Phase 4: Users and providers
            $this->call(AdminTableSeeder::class);
            $this->call(UserTableSeeder::class);
            // $this->call(ProviderSeeder::class);

            // Phase 5: User/Provider dependent tables
            // $this->call(RateSeeder::class);
            // $this->call(FavoriteSeeder::class);
            // $this->call(ProviderBankAccountSeeder::class);
            // $this->call(ProviderWorkingHourSeeder::class);
            // $this->call(ServiceSeeder::class);
            $this->call(AccountDeletionRequestSeeder::class);
            $this->call(AddressSeeder::class);

            // Phase 6: Courses
            // $this->call(CourseSeeder::class);
            // $this->call(CourseStageSeeder::class);
            // $this->call(CourseEnrollmentSeeder::class);
            // $this->call(CourseStageCompletionSeeder::class);

            // Phase 7: Products and orders
            // $this->call(class: ProductSeeder::class);
     
        // $this->call(OrderSeeder::class);
        // $this->call(OrderItemSeeder::class);
        // $this->call(ProviderSubOrderSeeder::class);
        // $this->call(OrderStatusSeeder::class);
        // $this->call(OrderBankAccountSeeder::class);

     
        // Phase 9: Ratings and reviews
        // $this->call(ServiceRateSeeder::class);
        // $this->call(ProviderRateSeeder::class);

        // Phase 10: Blog interactions
        // $this->call(BlogCategoryTableSeeder::class);
        // $this->call(BlogTableSeeder::class);
        // $this->call(BlogCommentSeeder::class);
        // $this->call(BlogReactionSeeder::class);

        // Phase 11: Other dependent tables
        // $this->call(TransactionSeeder::class);
        // $this->call(ComplaintTableSeeder::class);
        // $this->call(ComplaintReplaySeeder::class);
        // $this->call(AdvTableSeeder::class);
        // $this->call(NotificationSeeder::class);
                    $this->call(AdvertisementSeeder::class);
                    $this->call(TrainerApplicationSeeder::class);
                    $this->call(PurchaseRequestSeeder::class);

    }
}
