# Relationship-Based Last Watch Time Implementation

## Overview
Updated the course progress system to use Eloquent relationships between CourseStage and CourseStageCompletion models instead of direct database queries, providing better code organization and leveraging <PERSON><PERSON>'s ORM capabilities.

## What Was Implemented

### **1. CourseStage Model Relationships**

#### **New Relationships Added**
```php
/**
 * Get all stage completions for this stage
 */
public function stageCompletions()
{
    return $this->hasMany(CourseStageCompletion::class, 'stage_id');
}

/**
 * Get the stage completion for the current authenticated user
 */
public function userCompletion()
{
    return $this->hasOne(CourseStageCompletion::class, 'stage_id')
                ->whereHas('enrollment', function ($query) {
                    $query->where('user_id', auth()->id());
                });
}
```

### **2. CourseStagesResource Optimization**

#### **Before (Direct Database Query)**
```php
$completion = \DB::table('course_stage_completions')
                ->join('course_enrollments', 'course_stage_completions.enrollment_id', '=', 'course_enrollments.id')
                ->where('course_enrollments.user_id', $user->id)
                ->where('course_enrollments.course_id', $this->course_id)
                ->where('course_stage_completions.stage_id', $this->id)
                ->select('course_stage_completions.last_watch_time', 'course_stage_completions.completed_at')
                ->first();
```

#### **After (Relationship-Based)**
```php
// Get completion data using relationship
if ($user) {
    // Load the user completion relationship
    $this->loadMissing(['userCompletion']);
    $completion = $this->userCompletion;

    if ($completion) {
        $isCompleted = $completion->completed_at !== null;
        $lastWatchTime = $completion->last_watch_time ?? 0;
    }
}
```

### **3. Eager Loading Implementation**

#### **CourseController Show Method**
```php
public function show($id)
{
    $user = auth()->user();
    $course = $this->courseService->getCourseWithEnrollmentStatus($id, $user?->id);
    
    // Load stages with user completion data if user is authenticated
    if ($user) {
        $course->load(['stages.userCompletion']);
    } else {
        $course->load('stages');
    }
    
    return Responder::success(CourseResource::make($course));
}
```

#### **CourseService getUserEnrolledCourses Method**
```php
public function getUserEnrolledCourses(int $userId)
{
    return CourseEnrollment::with(['course.stages.userCompletion'])
        ->where('user_id', $userId)
        ->whereIn('status', ['active', 'completed'])
        ->get()
        ->pluck('course');
}
```

## Benefits

### **1. Laravel ORM Best Practices**
- ✅ **Eloquent Relationships**: Uses proper model relationships instead of raw queries
- ✅ **Model Encapsulation**: Logic contained within model relationships
- ✅ **Type Safety**: Returns Eloquent models with proper casting and attributes

### **2. Performance Optimization**
- ✅ **Eager Loading**: Prevents N+1 query problems with `with()` and `load()`
- ✅ **Conditional Loading**: Only loads completion data when user is authenticated
- ✅ **Efficient Queries**: Laravel optimizes relationship queries automatically

### **3. Code Maintainability**
- ✅ **Readable Code**: Relationship names clearly indicate purpose
- ✅ **Reusable Logic**: Relationships can be used across different controllers
- ✅ **Testable**: Easier to mock and test relationships

### **4. Data Consistency**
- ✅ **Model Casting**: Automatic type casting through Eloquent models
- ✅ **Attribute Accessors**: Can use model accessors and mutators
- ✅ **Relationship Constraints**: Database constraints enforced through relationships

## Implementation Details

### **1. Relationship Structure**
```
CourseStage (1) ──── (Many) CourseStageCompletion
     │                           │
     │                           │
     └── userCompletion (1) ─────┘
```

### **2. Query Optimization**
```php
// Single query with eager loading
$course->load(['stages.userCompletion']);

// Equivalent SQL (optimized by Laravel)
SELECT * FROM course_stages WHERE course_id = ?
SELECT * FROM course_stage_completions 
WHERE stage_id IN (?, ?, ?) 
AND enrollment_id IN (
    SELECT id FROM course_enrollments 
    WHERE user_id = ? AND course_id = ?
)
```

### **3. Conditional Relationship Loading**
```php
// Only load completion data for authenticated users
if ($user) {
    $course->load(['stages.userCompletion']);
} else {
    $course->load('stages');
}
```

## API Response Format

### **CourseStagesResource Response**
```json
{
    "id": 1,
    "name": "Introduction to React",
    "media": {
        "id": 15,
        "url": "https://example.com/videos/intro-react.mp4",
        "type": "video"
    },
    "is_completed": false,
    "last_watch_time": 1800
}
```

### **Course Show Response (with stages)**
```json
{
    "id": 1,
    "name": "Web Development Course",
    "stages": [
        {
            "id": 1,
            "name": "Introduction",
            "is_completed": true,
            "last_watch_time": 3600
        },
        {
            "id": 2,
            "name": "Advanced Concepts",
            "is_completed": false,
            "last_watch_time": 1800
        }
    ]
}
```

## Usage Examples

### **1. Accessing User Completion in Controllers**
```php
// Load stage with user completion
$stage = CourseStage::with('userCompletion')->find($stageId);

// Check completion status
if ($stage->userCompletion) {
    $isCompleted = $stage->userCompletion->completed_at !== null;
    $lastWatchTime = $stage->userCompletion->last_watch_time;
}
```

### **2. Bulk Loading for Multiple Stages**
```php
// Load all stages with user completions
$stages = CourseStage::with('userCompletion')
                    ->where('course_id', $courseId)
                    ->orderBy('order')
                    ->get();

foreach ($stages as $stage) {
    $progress = $stage->userCompletion ? $stage->userCompletion->last_watch_time : 0;
}
```

### **3. Conditional Queries**
```php
// Only load completion data if user is authenticated
$query = CourseStage::query();

if (auth()->check()) {
    $query->with('userCompletion');
}

$stages = $query->where('course_id', $courseId)->get();
```

## Frontend Integration

### **1. Vue.js Component**
```vue
<template>
    <div class="course-stages">
        <div 
            v-for="stage in stages" 
            :key="stage.id"
            class="stage-item"
            :class="{ 'completed': stage.is_completed }"
        >
            <h3>{{ stage.name }}</h3>
            <div class="progress-info">
                <span v-if="stage.is_completed" class="completed-badge">
                    ✓ Completed
                </span>
                <span v-else class="progress-time">
                    Progress: {{ formatSeconds(stage.last_watch_time) }}
                </span>
            </div>
            <button @click="playVideo(stage)" class="play-btn">
                {{ stage.last_watch_time > 0 ? 'Continue' : 'Start' }}
            </button>
        </div>
    </div>
</template>

<script>
export default {
    props: ['stages'],
    
    methods: {
        playVideo(stage) {
            // Use last_watch_time directly for video resumption
            this.$emit('play-video', {
                stageId: stage.id,
                resumeTime: stage.last_watch_time
            });
        },
        
        formatSeconds(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
}
</script>
```

### **2. JavaScript Video Player Integration**
```javascript
class CourseVideoPlayer {
    constructor(stage, courseId) {
        this.stage = stage;
        this.courseId = courseId;
        this.video = document.getElementById('courseVideo');
        
        this.initializePlayer();
    }
    
    initializePlayer() {
        // Resume from last watch time (already in seconds)
        if (this.stage.last_watch_time > 0) {
            this.video.currentTime = this.stage.last_watch_time;
        }
        
        // Set up progress tracking
        this.video.addEventListener('timeupdate', () => {
            this.trackProgress();
        });
    }
    
    trackProgress() {
        const currentTime = Math.floor(this.video.currentTime);
        
        // Update progress every 10 seconds
        if (currentTime % 10 === 0 && currentTime !== this.lastSavedTime) {
            this.saveProgress(currentTime);
            this.lastSavedTime = currentTime;
        }
    }
    
    async saveProgress(seconds) {
        const timeString = this.secondsToHMS(seconds);
        
        try {
            await fetch(`/api/courses/${this.courseId}/stages/${this.stage.id}/update-watch-time`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                },
                body: JSON.stringify({
                    last_watch_time: timeString
                })
            });
        } catch (error) {
            console.error('Failed to save progress:', error);
        }
    }
    
    secondsToHMS(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}
```

## Performance Comparison

### **Before (Direct Database Query)**
- Raw SQL queries in resource classes
- Manual join operations
- No relationship caching
- Potential for inconsistent queries

### **After (Relationship-Based)**
- Eloquent relationship queries
- Automatic query optimization
- Eager loading prevents N+1 problems
- Consistent query patterns

## Testing

### **1. Unit Tests for Relationships**
```php
public function test_stage_has_user_completion_relationship()
{
    $user = User::factory()->create();
    $course = Course::factory()->create();
    $stage = CourseStage::factory()->create(['course_id' => $course->id]);
    $enrollment = CourseEnrollment::factory()->create([
        'user_id' => $user->id,
        'course_id' => $course->id
    ]);
    
    $completion = CourseStageCompletion::factory()->create([
        'enrollment_id' => $enrollment->id,
        'stage_id' => $stage->id,
        'last_watch_time' => 1800
    ]);
    
    $this->actingAs($user);
    
    $stage->load('userCompletion');
    $this->assertNotNull($stage->userCompletion);
    $this->assertEquals(1800, $stage->userCompletion->last_watch_time);
}
```

### **2. Feature Tests for API Responses**
```php
public function test_course_stages_include_user_progress()
{
    $user = User::factory()->create();
    $course = Course::factory()->create();
    $stage = CourseStage::factory()->create(['course_id' => $course->id]);
    
    // Create enrollment and completion
    $enrollment = CourseEnrollment::factory()->create([
        'user_id' => $user->id,
        'course_id' => $course->id
    ]);
    
    CourseStageCompletion::factory()->create([
        'enrollment_id' => $enrollment->id,
        'stage_id' => $stage->id,
        'last_watch_time' => 1800,
        'completed_at' => null
    ]);
    
    $response = $this->actingAs($user)->getJson("/api/courses/{$course->id}");
    
    $response->assertStatus(200)
            ->assertJsonPath('data.stages.0.last_watch_time', 1800)
            ->assertJsonPath('data.stages.0.is_completed', false);
}
```

The relationship-based implementation provides better code organization, performance optimization, and maintainability while leveraging Laravel's powerful ORM capabilities! 🔗📊✨
