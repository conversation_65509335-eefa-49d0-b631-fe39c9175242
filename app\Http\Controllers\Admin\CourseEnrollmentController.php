<?php

namespace App\Http\Controllers\Admin;

use App\Models\CourseEnrollment;
use App\Models\Course;
use App\Models\User;
use App\Traits\Report;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use PDF;

class CourseEnrollmentController extends Controller
{
    use Report;

    public function index($id = null)
    {
        if (request()->ajax()) {
            $searchArray = request()->searchArray ?? [];

            // If no search criteria or all empty, get all enrollments
            if (empty($searchArray) || empty(array_filter($searchArray, function($value) {
                return $value !== '' && $value !== null;
            }))) {
                $enrollments = CourseEnrollment::with(['user', 'course'])->orderBy('enrolled_at', 'DESC')->paginate(30);
            } else {
                $enrollments = CourseEnrollment::with(['user', 'course'])->search($searchArray)->paginate(30);
            }

            $html = view('admin.course_enrollments.table', compact('enrollments'))->render();
            return response()->json(['html' => $html]);
        }

        // Get courses and users for search dropdowns
        $courses = Course::select('id', 'name')->get();
        $users = User::select('id', 'name')->get();

        return view('admin.course_enrollments.index', compact('courses', 'users'));
    }

    public function show($id)
    {
        $enrollment = CourseEnrollment::with(['user', 'course'])->findOrFail($id);
        return view('admin.course_enrollments.show', compact('enrollment'));
    }

    public function downloadPdf($id)
    {
        $enrollment = CourseEnrollment::with(['user', 'course'])->findOrFail($id);

        $pdf = \PDF::loadView('admin.course_enrollments.pdf', compact('enrollment'));

        return $pdf->download('enrollment-' . $enrollment->id . '.pdf');
    }

    public function confirmPayment(Request $request, $id)
    {
        $enrollment = CourseEnrollment::findOrFail($id);
        // ... existing code ...
    }

    public function destroy($id)
    {
        try {
            $enrollment = CourseEnrollment::findOrFail($id);
            $enrollment->delete();
            Report::addToLog('حذف اشتراك دورة تدريبية');
            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الاشتراك'], 500);
        }
    }

    public function deleteAll(Request $request)
    {
        try {
            $ids = $request->ids;
            CourseEnrollment::whereIn('id', $ids)->delete();
            Report::addToLog('حذف متعدد لاشتراكات الدورات التدريبية');
            return response()->json(['message' => 'تم حذف الاشتراكات بنجاح']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الاشتراكات'], 500);
        }
    }
}
