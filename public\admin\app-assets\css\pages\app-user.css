/*========================================================
        DARK LAYOUT
=========================================================*/
/* user page css */
/*---------------*/
.users-list-wrapper .actions-dropodown {
  box-shadow : 0 2px 8px 0 rgba(0, 0, 0, 0.14);
  border-radius : 0.5rem;
}

.users-list-wrapper a, .users-list-wrapper span {
  color : inherit;
}
.users-list-wrapper a i, .users-list-wrapper span i {
  cursor : pointer;
  font-size : 1.2rem;
}
.users-list-wrapper a i.users-delete-icon, .users-list-wrapper span i.users-delete-icon {
  font-size : 1.2rem;
}
.users-list-wrapper a i.users-delete-icon:hover, .users-list-wrapper span i.users-delete-icon:hover {
  color : #EA5455;
}
.users-list-wrapper a i.users-edit-icon:hover, .users-list-wrapper span i.users-edit-icon:hover {
  color : #7367F0;
}

.users-list-wrapper .badge {
  text-transform : uppercase;
}

.users-list-wrapper .ag-icon-checkbox-unchecked {
  color : #B8C2CC !important;
  font-size : 22px;
}

.users-list-wrapper .ag-icon-checkbox-checked, .users-list-wrapper .ag-icon-checkbox-indeterminate {
  font-size : 22px;
}

.page-users-view .users-view-image {
  width : 150px;
}

.page-users-view table td {
  padding-bottom : 0.8rem;
  min-width : 140px;
  word-break : break-word;
}

.page-users-view .users-view-permission table td, .page-users-view .users-view-permission table th {
  padding-bottom : 0;
}

.page-users-edit .users-avatar-shadow {
  box-shadow : 2px 4px 14px 0 rgba(34, 41, 47, 0.4);
}