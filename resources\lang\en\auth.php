<?php

return [

  /*
  |--------------------------------------------------------------------------
  | Authentication Language Lines
  |--------------------------------------------------------------------------
  |
  | The following language lines are used during authentication for various
  | messages that we need to display to the user. You are free to modify
  | these language lines according to your application's requirements.
  |
   */

  'failed'                  => 'These credentials do not match our records.',
  'throttle'                => 'Too many login attempts. Please try again in :seconds seconds.',
  'sms'                     => 'Activation code',
  'not_approved'            => 'This account is not approved by the admin',
  'registered'              => 'Profile created successfully',
  'activated'               => 'account activated successfully',
  'code_expired'            => 'Code expired',
  'code_invalid'            => 'Code invalid',
  'code_valid'              => 'Code is valid',
  'code_re_send'            => 'Code resend',
  'invalid_token'           => 'jwt token invalid',
  'expired_token'           => 'jwt token expired',
  'not_authorized'          => 'You are not authorized',
  'incorrect_pass_or_phone' => 'Incorrect password',
  'not_active'              => 'Account needs activation',
  'incorrect_pass'          => 'Incorrect password',
  'incorrect_old_pass'      => 'Incorrect Old password',
  'incorrect_key_or_phone'  => 'Check the phone number or email',
  'unauthenticated'         => 'Please login again',
  'blocked'                 => 'blocked',
  'account_deleted'                 => 'Account Deleted successfully',
  'same_password'                   => 'New password cannot be the same as your current password',
  'old_phone_mismatch'              => 'Old phone number does not match your current phone number',
  'phone_already_exists'            => 'This phone number is already registered',
  'no_pending_phone_update'         => 'No pending phone update request found',
  'invalid_code'                    => 'Invalid verification code',
  'phone_rejected_cannot_reregister' => 'This phone number is rejected and cannot be re-registered',
  'password_reset_success' => 'Password has been reset successfully.',





];
