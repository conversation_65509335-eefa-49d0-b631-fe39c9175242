<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderProviderStatus extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'provider_id',
        'status',
        'subtotal',
        'services_total',
        'products_total',
        'booking_fee',
        'home_service_fee',
        'delivery_fee',
        'discount_amount',
        'total',
        'notes',
        'confirmed_at',
        'completed_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'services_total' => 'decimal:2',
        'products_total' => 'decimal:2',
        'booking_fee' => 'decimal:2',
        'home_service_fee' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the order that owns this provider status
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the provider for this status
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the order items for this provider
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'order_id', 'order_id')
                    ->whereHas('item', function($query) {
                        $query->where('provider_id', $this->provider_id);
                    });
    }

    /**
     * Update provider status and track the change
     */
    public function updateStatus($newStatus, $statusableType, $statusableId, $notes = null)
    {
        $updateData = ['status' => $newStatus];

        if ($newStatus === 'confirmed') {
            $updateData['confirmed_at'] = now();
        } elseif ($newStatus === 'completed') {
            $updateData['completed_at'] = now();
        }

        if ($notes) {
            $updateData['notes'] = $notes;
        }

        $this->update($updateData);

        // Track the status change in the main order status table
        OrderStatus::createStatusChange(
            $this->order_id,
            $newStatus,
            $statusableType,
            $statusableId,
            "Provider {$this->provider->name}: {$notes}"
        );

        // Update main order status based on all providers' statuses
        $this->updateMainOrderStatus();

        return $this;
    }

    /**
     * Update main order status based on all providers' statuses
     */
    private function updateMainOrderStatus()
    {
        $order = $this->order;
        $allProviderStatuses = $order->providerStatuses;

        // Determine overall order status based on provider statuses
        $statuses = $allProviderStatuses->pluck('status')->toArray();

        if (in_array('cancelled', $statuses)) {
            // If any provider cancelled, check if all are cancelled
            if (collect($statuses)->every(fn($status) => $status === 'cancelled')) {
                $newStatus = 'cancelled';
            } else {
                $newStatus = 'partially_cancelled';
            }
        } elseif (collect($statuses)->every(fn($status) => $status === 'completed')) {
            $newStatus = 'completed';
        } elseif (collect($statuses)->every(fn($status) => in_array($status, ['completed', 'confirmed']))) {
            $newStatus = 'confirmed';
        } elseif (in_array('processing', $statuses) || in_array('confirmed', $statuses)) {
            $newStatus = 'processing';
        } else {
            $newStatus = 'pending_payment';
        }

        // Only update if status actually changed
        if ($order->status !== $newStatus) {
            $order->update(['status' => $newStatus]);
        }
    }

    /**
     * Check if this provider's items can be cancelled
     */
    public function canBeCancelled()
    {
        return in_array($this->status, ['pending_payment', 'processing']);
    }

    /**
     * Get status description with provider context
     */
    public function getStatusDescriptionAttribute()
    {
        return ucfirst(str_replace('_', ' ', $this->status));
    }
}
