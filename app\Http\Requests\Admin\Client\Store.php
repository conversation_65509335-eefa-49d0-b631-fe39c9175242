<?php

namespace App\Http\Requests\Admin\Client;

use App\Rules\ClientPhoneUnique;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class Store extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'     => 'required|min:3|max:191',
            'is_active'  => 'nullable',
            'country_code' => 'required',
            'city_id' => 'nullable|exists:cities,id',
            'region_id' => 'nullable|exists:regions,id',
            'phone'    => [
                'required',
                'phone:SA',
                 Rule::unique('users', 'phone')->whereNull('deleted_at'),
                // new ClientPhoneUnique()
            ],
            'email'    => 'nullable|email|max:191|unique:users,email,NULL,NULL,deleted_at,NULL',
            'password' => ['required', 'min:6'],
            'password_confirmation' => ['required', 'same:password'],
            'gender' => 'nullable|in:male,female',
            'image'   => ['nullable', 'image'],
        ];
    }
}
