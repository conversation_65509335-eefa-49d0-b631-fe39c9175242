<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AdUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'main_category_id' => 'nullable|exists:categories,id',
            'sub_category_id' => 'nullable|exists:categories,id',
            'gender_target' => 'nullable|in:male,female,both',
           'region_id' => 'nullable|exists:regions,id',
           'city_id'   => 'nullable|exists:cities,id',
            'status' => 'in:under_review,active,rejected,hidden,cancelled',
            'whatsapp_contact' => 'nullable|string|max:20',
            'is_main' => 'boolean',
            'main_image' => 'nullable|image|max:2048', 
            'images' => 'nullable|array',
            'images.*' => 'image|max:2048',
        ];
    }
}
