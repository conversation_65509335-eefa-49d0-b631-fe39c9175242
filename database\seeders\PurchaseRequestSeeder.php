<?php

namespace Database\Seeders;

use App\Models\PurchaseRequest;
use App\Models\User;
use App\Models\Advertisement;
use Illuminate\Database\Seeder;

class PurchaseRequestSeeder extends Seeder
{
    public function run(): void
    {
        $buyers = User::inRandomOrder()->limit(5)->get();
        $sellers = User::inRandomOrder()->limit(5)->get();
        $ads = Advertisement::inRandomOrder()->limit(5)->get();

        foreach (range(1, 10) as $i) {
            PurchaseRequest::create([
                'advertisement_id' => $ads->random()->id,
                'buyer_id' => $buyers->random()->id,
                'seller_id' => $sellers->random()->id,
               'status' => collect([
    'under_review',
    'waiting_buyer_confirmation',
    'completed',
    'problem',
    'cancelled'
    ])->random(),
                'amount_paid' => rand(100, 1000),
                'wallet_credit_used' => rand(0, 500),
                                'payment_method' => collect(['online', 'wallet'])->random(), 

            ]);
        }
    }
}
