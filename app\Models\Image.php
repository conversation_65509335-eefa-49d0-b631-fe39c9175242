<?php

namespace App\Models;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\Translatable\HasTranslations;
use Spa<PERSON>\MediaLibrary\InteractsWithMedia;


class Image extends BaseModel implements HasMedia
{

    use InteractsWithMedia , HasTranslations;

        public const IMAGEPATH = 'uploads/images/';

    protected $fillable = ['name' , 'is_active' , 'link'];
    public $translatable = ['name'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image_ar')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));

        $this->addMediaCollection('image_en')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));
    }

    public function getImageArAttribute()
    {
        return $this->getFirstMediaUrl('image_ar');
    }

    public function getImageEnAttribute()
    {
        return $this->getFirstMediaUrl('image_en');
    }
}
