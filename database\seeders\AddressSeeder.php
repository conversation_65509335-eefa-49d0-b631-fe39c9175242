<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AddressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // User addresses with detailed information stored as plain text

        DB::table('addresses')->insert([
            [
                'user_id' => 1,
                'details' => 'Home Address: Ahmed Al-Rashid, Salmiya, Block 12, Salem Al-Mubarak Street, Building 45, Floor 3, Apartment 12. Near the main mosque, blue building. Phone: +***********',
                'is_default' => true,
                'created_at' => now()->subDays(30),
                'updated_at' => now()->subDays(30),
            ],
            [
                'user_id' => 1,
                'details' => 'Work Address: Ahmed Al-Rashid, Kuwait City, Block 5, Fahad Al-Salem Street, Building 120, Floor 8. Office building, reception on ground floor. Phone: +***********',
                'is_default' => false,
                'created_at' => now()->subDays(25),
                'updated_at' => now()->subDays(25),
            ],
            [
                'user_id' => 2,
                'details' => 'Home Address: Fatima Al-Zahra, Hawally, Block 8, Tunis Street, Building 67, Floor 2, Apartment 8. White building with green gate. Phone: +***********',
                'is_default' => true,
                'created_at' => now()->subDays(20),
                'updated_at' => now()->subDays(20),
            ],
            [
                'user_id' => 3,
                'details' => 'Home Address: Sarah Johnson, Jabriya, Block 15, Block 15 Street, Building 89, Floor 1, Apartment 3. Ground floor apartment, garden view. Phone: +96555123456',
                'is_default' => true,
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(15),
            ],
            [
                'user_id' => 3,
                'details' => 'Weekend Address: Sarah Johnson, Mahboula, Block 3, Coastal Road, Building 234, Floor 5, Apartment 15. Beach house, weekend residence. Phone: +96555123456',
                'is_default' => false,
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ],
            [
                'user_id' => 4,
                'details' => 'Home Address: Mohammad Al-Ahmad, Farwaniya, Block 22, Abdullah Al-Mubarak Street, Building 156, Floor 4, Apartment 18. Near the shopping center. Phone: +***********',
                'is_default' => true,
                'created_at' => now()->subDays(8),
                'updated_at' => now()->subDays(8),
            ],
            [
                'user_id' => 5,
                'details' => 'Home Address: Layla Al-Mansouri, Ahmadi, Block 7, Ahmad Al-Jaber Street, Building 78, Floor 2, Apartment 6. Villa with red roof, large garden. Phone: +***********',
                'is_default' => true,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'user_id' => 2,
                'details' => 'Work Address: Fatima Al-Zahra, Sharq, Block 1, Gulf Road, Building 300, Floor 12. Business tower, sea view office. Phone: +***********',
                'is_default' => false,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 1,
                'details' => 'Alternative Address: Ahmed Al-Rashid, Mangaf, Block 4, Coastal Highway, Building 88, Floor 1, Apartment 5. Near the beach, weekend house. Phone: +***********',
                'is_default' => false,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'user_id' => 4,
                'details' => 'Work Address: Mohammad Al-Ahmad, Kuwait City, Block 2, Fahad Al-Salem Street, Building 200, Floor 6. Commercial building, elevator access. Phone: +***********',
                'is_default' => false,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
        ]);

        $this->command->info('Addresses seeded successfully!');
    }
}
