<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AdRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // 'name' => 'required|string|max:255',
            // 'description' => 'required|string|max:255',
              'name.ar' => 'required|string|max:255',
        'name.en' => 'required|string|max:255',

        'description.ar' => 'required|string|max:255',
        'description.en' => 'required|string|max:255',

            'price' => 'required|numeric|min:1',
            'main_category_id' => 'required|exists:categories,id',
            'sub_category_id' => 'required|exists:categories,id',
            'gender_target' => 'required|in:male,female,both',
           'region_id' => 'required|exists:regions,id',
           'city_id'   => 'required|exists:cities,id',
            'status' => 'in:under_review,active,rejected,hidden,cancelled',
            'whatsapp_contact' => 'nullable|string|max:20',
            'is_main' => 'boolean',
            'main_image' => 'required|image|max:2048',
            'images' => 'nullable|array',
            'images.*' => 'image|max:2048',
            
        ];
    }
}
