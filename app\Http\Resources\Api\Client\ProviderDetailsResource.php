<?php

namespace App\Http\Resources\Api\Client;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Api\Client\ProductResource;

class ProviderDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->commercial_name,
            'provider_type' => $this->salon_type,
'city' => optional(optional($this->user)->city)->name,

            'is_currently_available' => $this->is_currently_available,
            'rate' =>$this->rate_summary,
            'description' => 'daewq',
            'working_hours' => WorkingHoursResource::collection($this->whenLoaded('WorkingHours')),
            'services' => ServiceResource::collection($this->whenLoaded('activeServices')),
            'products' => ProductResource::collection($this->whenLoaded('activeProducts')),
            'rates' => RateResource::collection($this->whenLoaded('rates')),


        ];
    }
}
