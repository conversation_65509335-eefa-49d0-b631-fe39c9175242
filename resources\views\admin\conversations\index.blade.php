@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('admin/index_page.css') }}">
@endsection

@section('content')
    <x-admin.table
        extrabuttons="true"
        :searchArray="[]" {{-- ضيف عناصر البحث هنا إذا لزم --}}
    >
        <x-slot name="extrabuttonsdiv">
            {{-- أي أزرار إضافية هنا --}}
        </x-slot>

        <x-slot name="tableContent">
            <div class="table_content_append card">
                @include('admin.conversations.table') {{-- أو admin.consultations.table حسب اسم الملف --}}
            </div>
        </x-slot>
    </x-admin.table>
@endsection

@section('js')
    <script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>

    @include('admin.shared.deleteAll')
    @include('admin.shared.deleteOne')

    {{-- الـ URL هنا هو اللي بيستخدمه الفلترة والـ AJAX لجلب البيانات --}}
    @include('admin.shared.filter_js', ['index_route' => url('admin/consultations')])
@endsection
