<?php

namespace App\Http\Requests\Admin\blogs;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $default_lang = config('app.locale');

        return [
            'title'                 => 'required|array',
            "title.{$default_lang}" => 'required|string|max:191',
            'title.*'               => 'nullable|string|max:191',
            'content'               => 'required|array',
            "content.{$default_lang}" => 'required|string|min:30',
            'content.*'             => 'nullable|string',
            'category_id'           => 'required|exists:blog_categories,id',
            'image'                 => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'is_active'             => 'required|in:0,1',
        ];
    }
}
