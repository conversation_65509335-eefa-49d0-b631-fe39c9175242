<?php

namespace App\Services;

use App\Models\TrainerApplication;
use App\Repositories\TrainerApplicationRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use App\Facades\Responder;

class TrainerApplicationService
{
    protected TrainerApplicationRepository $repository;

    public function __construct(TrainerApplicationRepository $repository)
    {
        $this->repository = $repository;
    }

public function getUserApplications($userId, $cityId = null, $sort = 'latest', $search = null)
{
    $locale = app()->getLocale();

    $query = TrainerApplication::query()
        ->where('user_id', $userId)

//    ->when($search, function ($q) use ($search) {
//     $q->where("name->" . app()->getLocale(), 'LIKE', "%{$search}%");
// })


        ->when($cityId, fn($q) => $q->where('city_id', $cityId))
        // filter by name in the current language
        ->when($search, function ($q) use ($search, $locale) {
            $q->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.$locale')) LIKE ?", ["%$search%"]);
        })
        

        // العلاقات المطلوبة
        ->with([
            'city',
            'region',
            'user',
            'ratings' => fn($q) => $q->where('status', 'approved')
        ])

        // عدد التقييمات فقط المعتمدة
        ->withCount([
            'ratings as ratings_count' => fn($q) => $q->where('status', 'approved')
        ])

        // متوسط التقييمات فقط المعتمدة
        ->withAvg([
            'ratings as average_rating' => fn($q) => $q->where('status', 'approved')
        ], 'stars');

    // الترتيب حسب المطلوب
    switch ($sort) {
        case 'oldest':
            $query->orderBy('created_at', 'asc');
            break;
        case 'most_rated':
            $query->orderByDesc('average_rating');
            break;
        case 'least_rated':
            $query->orderBy('average_rating', 'asc');
            break;
        default:
            $query->orderByDesc('created_at');
            break;
    }

    return $query->paginate(10);
}






 public function getApplicationById($id)
{
    return TrainerApplication::with(['user', 'region', 'city', 'ratings.rater'])->find($id);
}


  public function createApplication(array $data, $userId, $image = null, $certificate = null, $works = [])
{
    $data['user_id'] = $userId;

    $application = $this->repository->create($data);

    if ($image && $image->isValid()) {
        $application->addMedia($image)->toMediaCollection('profile');
    }

    if ($certificate && $certificate->isValid()) {
        $application->addMedia($certificate)->toMediaCollection('certificate');
    }

    if (is_array($works)) {
        $validWorks = array_filter($works, fn ($file) => $file && $file->isValid());

        $limitedWorks = array_slice($validWorks, 0, 10); 

        foreach ($limitedWorks as $work) {
            $application->addMedia($work)->toMediaCollection('works');
        }
    }

    return $application;
}


    public function updateApplication($id, array $data, $image = null, $certificate = null, $works = [])
    {
        $application = $this->repository->findById($id);

        if (!$application || $application->user_id !== Auth::id()) {
            return Responder::error(null, ['message' => __('apis.not_authorized_to_update_application')], 403);
        }

        $this->repository->update($application, $data);

        if ($image) {
            $application->clearMediaCollection('profile');
            $application->addMedia($image)->toMediaCollection('profile');
        }

        if ($certificate) {
            $application->clearMediaCollection('certificate');
            $application->addMedia($certificate)->toMediaCollection('certificate');
        }

        if ($works) {
            $application->clearMediaCollection('works');
            foreach ($works as $work) {
                $application->addMedia($work)->toMediaCollection('works');
            }
        }

        return $application;
    }

    public function deleteApplication($id)
    {
        $application = $this->repository->findById($id);

        if (!$application || $application->user_id !== Auth::id()) {
            return response()->json([
                'status' => false,
                'message' => __('apis.not_authorized_to_delete_application')
            ], 403);
        }

        $this->repository->delete($application);

        return response()->json([
            'status' => true,
            'message' => __('apis.deleted_successfully')
        ]);
    }


    public function listTrainers($sort = 'latest')
{
    $query = TrainerApplication::query()
        ->with(['city', 'user']) // optional
        ->withCount([
            'ratings as ratings_count',
            'ratings as average_rating' => function ($q) {
                $q->select(\DB::raw('coalesce(avg(stars), 0)'));
            }
        ]);

    switch ($sort) {
        case 'oldest':
            $query->orderBy('created_at', 'asc');
            break;

        case 'most_rated':
            $query->orderByDesc('average_rating');
            break;

        case 'least_rated':
            $query->orderBy('average_rating', 'asc');
            break;

        default: 
            $query->orderByDesc('created_at');
    }

    return $query->paginate(10);
}
}
