<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use Illuminate\Http\Request;
use App\Services\ServiceService;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Client\ServiceResource;

class ServiceController extends Controller
{
    protected $serviceService;

    public function __construct(ServiceService $serviceService)
    {
        $this->serviceService = $serviceService;
    }


    

  

}
