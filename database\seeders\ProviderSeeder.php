<?php

namespace Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Support\Arr;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create('ar_SA');
        $fakerEn = Faker::create('en_US');

        // First, let's create some provider users in the users table
        $providerUsers = [];
        for ($i = 1; $i <= 10; $i++) {
            $providerUsers[] = [
                'name' => $faker->name,
                'country_code' => '965',
                'phone' => '5' . str_pad($i, 7, '0', STR_PAD_LEFT),
                'email' => "provider{$i}@sorriso.com",
                'password' => bcrypt('123456'),
                'gender' => Arr::random(['male', 'female']),
                'type' => 'provider',
                'status' => Arr::random(['pending', 'blocked']),
                'is_active' => Arr::random([true, false]),
                'is_notify' => true,
                'code' => null,
                'code_expire' => null,
                'region_id' => rand(1, 3),
                'city_id' => rand(1, 10),
                'wallet_balance' => round($faker->randomFloat(2, 0, 1000), 2),
                'loyalty_points' => rand(0, 500),
                'created_at' => now()->subDays(rand(1, 90)),
                'updated_at' => now()->subDays(rand(0, 30)),
            ];
        }

        DB::table('users')->insert($providerUsers);

        // Get the inserted user IDs (with type provider)
        $providerUserIds = DB::table('users')->where('type', 'provider')->orderBy('id')->pluck('id')->toArray();

        // Now create the providers
        $providers = [];
        for ($i = 0; $i < 10; $i++) {
            $userId = $providerUserIds[$i];
            $salonTypes = ['salon', 'beauty_center'];
            $statuses = ['in_review', 'pending', 'accepted', 'rejected', 'blocked'];
            $nationalities = ['kuwaiti', 'saudi', 'egyptian', 'lebanese', 'syrian', 'other'];
            $residenceTypes = ['resident', 'visitor', 'citizen'];

            $status = Arr::random($statuses);
            $nationality = Arr::random($nationalities);
            $isNonKuwaiti = $nationality !== 'kuwaiti';

            $providers[] = [
                'user_id' => $userId,
                'salon_type' => Arr::random($salonTypes),
                'commercial_name' => json_encode([
                    'en' => "{$fakerEn->company} Beauty Center",
                    'ar' => "مركز {$faker->company} للجمال"
                ]),
                'commercial_register_no' => str_pad(rand(**********, **********), 10, '0', STR_PAD_LEFT),
                'institution_name' => "{$faker->company} Institution",
                'sponsor_name' => $isNonKuwaiti ? $faker->name : null,
                'sponsor_phone' => $isNonKuwaiti ? '965' . rand(50000000, 99999999) : null,
                'is_mobile' => Arr::random([true, false]),
                'mobile_service_fee' => rand(5, 25),
                'description' => $faker->paragraph(3),
                'status' => $status,
                'rejection_reason' => $status === 'rejected' ? 'Incomplete documentation provided' : null,
                'is_active' => $status === 'accepted' ? Arr::random([true, false]) : false,
                'accept_orders' => $status === 'accepted' ? Arr::random([true, false]) : false,
                'wallet_balance' => round($faker->randomFloat(2, 0, 5000), 2),
                'withdrawable_balance' => round($faker->randomFloat(2, 0, 2000), 2),
                'lat' => $faker->latitude(29.0, 30.1), // Kuwait latitude range
                'lng' => $faker->longitude(47.5, 48.5), // Kuwait longitude range
                'map_desc' => $faker->streetAddress,
                'nationality' => $nationality,
                'residence_type' => $isNonKuwaiti ? Arr::random($residenceTypes) : null,
                'in_home' => Arr::random([true, false]),
                'in_salon' => Arr::random([true, false]),
                'home_fees' => rand(0, 15),
                'created_at' => now()->subDays(rand(1, 90)),
                'updated_at' => now()->subDays(rand(0, 30)),
            ];
        }

        DB::table('providers')->insert($providers);

        $this->command->info('Providers seeded successfully!');
    }
}