<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
            // Orders with complete structure matching the migration

        DB::table('orders')->insert([
            [
                'order_number' => 'ORD-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'address_id' => 1,
                'user_id' => 1,
                'payment_method_id' => 1,
                'current_status' => 'completed',
                'payment_status' => 'success',
                'subtotal' => 45.00,
                'services_total' => 25.00,
                'products_total' => 20.00,
                'discount_amount' => 0.00,
                'discount_percentage' => 0.00,
                'coupon_id' => null,
                'booking_fee' => 2.00,
                'home_service_fee' => 5.00,
                'delivery_fee' => 3.00,
                'platform_commission' => 4.50,
                'provider_share' => 40.50,
                'total' => 50.00,
                'loyalty_points_earned' => 50,
                'loyalty_points_used' => 0,
                'cancellation_reason' => null,
                'scheduled_at' => now()->addDays(2),
                'invoice_number' => 'INV-' . date('Ymd') . '-001',
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'city_id' => 1,
                'booking_type' => 'home',
                'delivery_type' => 'normal',
                'cancel_reason_id' => null,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(4),
                'deleted_at' => null,
            ],
            [
                'order_number' => 'ORD-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'address_id' => 2,
                'user_id' => 2,
                'payment_method_id' => 2,
                'current_status' => 'processing',
                'payment_status' => 'success',
                'subtotal' => 35.00,
                'services_total' => 35.00,
                'products_total' => 0.00,
                'discount_amount' => 5.00,
                'discount_percentage' => 12.50,
                'coupon_id' => 1,
                'booking_fee' => 1.50,
                'home_service_fee' => 0.00,
                'delivery_fee' => 0.00,
                'platform_commission' => 3.15,
                'provider_share' => 26.35,
                'total' => 31.50,
                'loyalty_points_earned' => 32,
                'loyalty_points_used' => 0,
                'cancellation_reason' => null,
                'scheduled_at' => now()->addDays(1),
                'invoice_number' => 'INV-' . date('Ymd') . '-002',
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'city_id' => 2,
                'booking_type' => 'salon',
                'delivery_type' => null,
                'cancel_reason_id' => null,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subHours(6),
                'deleted_at' => null,
            ],
            [
                'order_number' => 'ORD-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'address_id' => 3,
                'user_id' => 3,
                'payment_method_id' => 3,
                'current_status' => 'confirmed',
                'payment_status' => 'success',
                'subtotal' => 85.00,
                'services_total' => 85.00,
                'products_total' => 0.00,
                'discount_amount' => 0.00,
                'discount_percentage' => 0.00,
                'coupon_id' => null,
                'booking_fee' => 3.00,
                'home_service_fee' => 0.00,
                'delivery_fee' => 0.00,
                'platform_commission' => 8.50,
                'provider_share' => 79.50,
                'total' => 88.00,
                'loyalty_points_earned' => 88,
                'loyalty_points_used' => 0,
                'cancellation_reason' => null,
                'scheduled_at' => now()->addHours(12),
                'invoice_number' => 'INV-' . date('Ymd') . '-003',
                'payment_reference' => 'PAY_' . \Illuminate\Support\Str::random(10),
                'city_id' => 3,
                'booking_type' => 'salon',
                'delivery_type' => null,
                'cancel_reason_id' => null,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subHours(8),
                'deleted_at' => null,
            ],
            [
                'order_number' => 'ORD-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'address_id' => 4,
                'user_id' => 4,
                'payment_method_id' => 4,
                'current_status' => 'pending_payment',
                'payment_status' => 'pending',
                'subtotal' => 25.00,
                'services_total' => 25.00,
                'products_total' => 0.00,
                'discount_amount' => 2.50,
                'discount_percentage' => 10.00,
                'coupon_id' => 2,
                'booking_fee' => 1.00,
                'home_service_fee' => 0.00,
                'delivery_fee' => 2.00,
                'platform_commission' => 2.25,
                'provider_share' => 20.25,
                'total' => 25.50,
                'loyalty_points_earned' => 0,
                'loyalty_points_used' => 0,
                'cancellation_reason' => null,
                'scheduled_at' => now()->addDays(3),
                'invoice_number' => null,
                'payment_reference' => null,
                'city_id' => 1,
                'booking_type' => 'salon',
                'delivery_type' => 'normal',
                'cancel_reason_id' => null,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
                'deleted_at' => null,
            ],
            [
                'order_number' => 'ORD-' . date('Ymd') . '-' . strtoupper(\Illuminate\Support\Str::random(6)),
                'address_id' => 5,
                'user_id' => 5,
                'payment_method_id' => 1,
                'current_status' => 'cancelled',
                'payment_status' => 'failed',
                'subtotal' => 60.00,
                'services_total' => 60.00,
                'products_total' => 0.00,
                'discount_amount' => 0.00,
                'discount_percentage' => 0.00,
                'coupon_id' => null,
                'booking_fee' => 2.50,
                'home_service_fee' => 0.00,
                'delivery_fee' => 0.00,
                'platform_commission' => 0.00,
                'provider_share' => 0.00,
                'total' => 62.50,
                'loyalty_points_earned' => 0,
                'loyalty_points_used' => 0,
                'cancellation_reason' => 'Customer requested cancellation due to schedule conflict',
                'scheduled_at' => now()->subHours(2),
                'invoice_number' => null,
                'payment_reference' => null,
                'city_id' => 2,
                'booking_type' => 'salon',
                'delivery_type' => null,
                'cancel_reason_id' => 1,
                'created_at' => now()->subHours(8),
                'updated_at' => now()->subHours(2),
                'deleted_at' => null,
            ],
        ]);

        $this->command->info('Orders seeded successfully!');
    }
}
