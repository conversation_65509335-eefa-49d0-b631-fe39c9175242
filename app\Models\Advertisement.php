<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Advertisement extends BaseModel implements HasMedia
{
    use HasFactory, InteractsWithMedia ,HasTranslations;


        protected $table = 'advertisements'; 

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'price',
       'main_category_id',
       'sub_category_id',
        'gender_target',
        'region_id',
        'city_id',
        'status',
        'whatsapp_contact',
        'is_main',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_main' => 'boolean',
    ];

    public $translatable = ['name', 'description'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('main_image')->singleFile();
        $this->addMediaCollection('ads');
    }

    // app/Models/Advertisement.php
// public function favoritedBy()
// {
//     return $this->belongsToMany(User::class, 'favorites', 'advertisement_id', 'user_id');
// }
public function favoritedBy()
{
    return $this->belongsToMany(User::class, 'favorites', 'advertisement_id', 'user_id')->withTimestamps();
}





public function main_category()
{
    return $this->belongsTo(Category::class, 'main_category_id');
}


public function sub_category()
{
    return $this->belongsTo(Category::class, 'sub_category_id');
}



    public function imageUrl()
    {
        return $this->getFirstMediaUrl('main_image');


    }

       public function user()
    {
        return $this->belongsTo(User::class);
    }

    // public function images()
    // {
    //     return $this->hasMany(AdvertisementImage::class);
    // }
    

    public function mainCategory()
{
    return $this->belongsTo(Category::class, 'main_category_id');
}

public function subCategory()
{
    return $this->belongsTo(Category::class, 'sub_category_id');
}


public function city()
{
    return $this->belongsTo(City::class);
}

public function region()
{
    return $this->belongsTo(Region::class);
}
    public function purchaseRequests()
    {
        return $this->hasMany(PurchaseRequest::class);
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }
    

    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }

    public function ratings()
    {
        return $this->hasMany(Rating::class, 'client_id', 'user_id')
            ->where('status', 'approved');
    }
}

