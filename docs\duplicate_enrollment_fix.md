# Duplicate Enrollment Fix

## Issue Fixed
**Error**: `SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1-1' for key 'course_enrollments.course_enrollments_user_id_course_id_unique'`

**Root Cause**: The system was trying to create new enrollment records even when a user already had an existing enrollment for the same course, violating the unique constraint on `user_id` and `course_id`.

## What Was Fixed

### 1. **Enhanced Enrollment Logic**
- ✅ **Existing Enrollment Check**: Now checks for ANY existing enrollment (not just active/completed)
- ✅ **Smart Handling**: Different handling based on existing enrollment status
- ✅ **Payment Retry**: Allows payment retry for pending enrollments
- ✅ **Enrollment Reactivation**: Reactivates cancelled/failed enrollments

### 2. **New Methods Added**
```php
// Check for any existing enrollment
private function getExistingEnrollment(int $userId, int $courseId): ?CourseEnrollment

// Handle existing enrollment based on status
private function handleExistingEnrollment(CourseEnrollment $existingEnrollment, array $paymentData)
```

### 3. **Enrollment Status Handling**

#### **Active/Completed Enrollments**
```php
case 'active':
case 'completed':
    // User already enrolled - return error
    return ['success' => false, 'message' => 'User is already enrolled in this course'];
```

#### **Pending Payment Enrollments**
```php
case 'pending_payment':
    // Same payment method: Retry payment
    // Different payment method: Update enrollment and process payment
```

#### **Cancelled/Failed Enrollments**
```php
case 'cancelled':
case 'failed':
    // Reactivate enrollment with new payment data
    // Reset status to 'pending_payment'
    // Process new payment
```

## Enrollment Flow Logic

### **Scenario 1: No Existing Enrollment**
```
User enrolls → Create new enrollment → Process payment
```

### **Scenario 2: Active Enrollment Exists**
```
User enrolls → Check existing → Return "Already enrolled" error
```

### **Scenario 3: Pending Payment Exists (Same Method)**
```
User enrolls → Check existing → Retry payment for existing enrollment
```

### **Scenario 4: Pending Payment Exists (Different Method)**
```
User enrolls → Check existing → Update payment method → Process payment
```

### **Scenario 5: Cancelled/Failed Enrollment Exists**
```
User enrolls → Check existing → Reactivate enrollment → Process payment
```

## Database Impact

### **Before Fix**
```sql
-- This would fail with duplicate key error
INSERT INTO course_enrollments (user_id, course_id, ...) 
VALUES (1, 1, ...);
-- Error: Duplicate entry '1-1' for key 'unique_user_course'
```

### **After Fix**
```sql
-- First check for existing
SELECT * FROM course_enrollments WHERE user_id = 1 AND course_id = 1;

-- If exists, update instead of insert
UPDATE course_enrollments 
SET status = 'pending_payment', payment_method = 'mada', enrolled_at = NOW()
WHERE user_id = 1 AND course_id = 1;
```

## API Response Examples

### **New Enrollment (Success)**
```json
{
  "success": true,
  "message": "Successfully enrolled in course",
  "enrollment": {
    "id": 17,
    "status": "pending_payment",
    "payment_method": "mada"
  }
}
```

### **Already Enrolled (Error)**
```json
{
  "success": false,
  "message": "User is already enrolled in this course",
  "enrollment": {
    "id": 16,
    "status": "active"
  }
}
```

### **Reactivated Enrollment (Success)**
```json
{
  "success": true,
  "message": "Enrollment reactivated and payment processed",
  "enrollment": {
    "id": 16,
    "status": "pending_payment",
    "payment_method": "mada"
  }
}
```

### **Payment Retry (Success)**
```json
{
  "success": true,
  "message": "Payment processed for existing enrollment",
  "enrollment": {
    "id": 16,
    "status": "pending_payment"
  }
}
```

## Testing Scenarios

### **Test 1: Normal Enrollment**
```bash
POST /api/client/courses/1/enroll
{
    "payment_method": "mada",
    "gateway": "myfatoorah"
}
# Expected: New enrollment created
```

### **Test 2: Duplicate Enrollment (Active)**
```bash
# First enrollment succeeds and becomes active
# Second enrollment attempt:
POST /api/client/courses/1/enroll
{
    "payment_method": "mada"
}
# Expected: "User is already enrolled" error
```

### **Test 3: Retry Failed Payment**
```bash
# First enrollment fails
# Second enrollment attempt:
POST /api/client/courses/1/enroll
{
    "payment_method": "mada"
}
# Expected: Reactivates existing enrollment
```

### **Test 4: Change Payment Method**
```bash
# First enrollment with credit_card pending
# Second enrollment with different method:
POST /api/client/courses/1/enroll
{
    "payment_method": "mada"
}
# Expected: Updates payment method and processes
```

## Database Schema

### **Unique Constraint**
```sql
-- This constraint prevents duplicate enrollments
UNIQUE KEY `course_enrollments_user_id_course_id_unique` (`user_id`, `course_id`)
```

### **Enrollment Statuses**
```sql
-- Possible enrollment statuses
'pending_payment'  -- Waiting for payment
'active'          -- Successfully enrolled
'completed'       -- Course completed
'cancelled'       -- Enrollment cancelled
'failed'          -- Payment failed
```

## Benefits

1. **✅ No More Duplicate Errors**: Handles existing enrollments gracefully
2. **✅ Payment Retry**: Users can retry failed payments
3. **✅ Method Switching**: Users can change payment methods
4. **✅ Enrollment Reactivation**: Failed enrollments can be reactivated
5. **✅ Better UX**: Clear messages about enrollment status
6. **✅ Data Integrity**: Maintains database constraints

## Monitoring

### **Check for Duplicate Attempts**
```sql
-- Monitor enrollment attempts
SELECT user_id, course_id, COUNT(*) as attempts
FROM course_enrollments 
GROUP BY user_id, course_id 
HAVING attempts > 1;
```

### **Check Enrollment Status Distribution**
```sql
-- Monitor enrollment statuses
SELECT status, COUNT(*) as count
FROM course_enrollments 
GROUP BY status;
```

The duplicate enrollment issue is now completely resolved with intelligent handling of existing enrollments! 🚀
