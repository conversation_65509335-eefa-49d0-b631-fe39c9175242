<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
   public function up(): void
{
    Schema::table('purchase_requests', function (Blueprint $table) {
        $table->decimal('product_commission', 10, 2)->default(0)->after('amount_paid');
    });
}

public function down(): void
{
    Schema::table('purchase_requests', function (Blueprint $table) {
        $table->dropColumn('product_commission');
    });
}

};
