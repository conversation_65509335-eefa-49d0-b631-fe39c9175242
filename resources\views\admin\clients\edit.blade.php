@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<form method="POST" action="{{ route('admin.clients.update', ['id' => $row->id]) }}" class="store form-horizontal" novalidate enctype="multipart/form-data">
    <section id="multiple-column-form">
        <div class="row">
            <div class="col-md-3">
                <div class="col-12 card card-body">
                    <div class="imgMontg col-12 text-center">
                        <div class="dropBox">
                            <div class="textCenter">
                                <div class="imagesUploadBlock">
                                    <label class="uploadImg">
                                        <span><i class="feather icon-image"></i></span>
                                        <input type="file" accept="image/*" name="image" class="imageUploader">
                                    </label>
                                    <div class="uploadedBlock">
                                        <img src="{{ $row->image }}">
                                        <button class="close"><i class="la la-times"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-9">
                <div class="card">
                    <div class="card-content">
                        <div class="card-body">
                            @csrf
                            @method('PUT')
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.name') }}</label>
                                            <div class="controls">
                                                <input type="text" name="name" value="{{ $row->name }}" class="form-control" placeholder="{{ __('admin.write_the_name') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.phone_number') }}</label>
                                            <div class="row">
                                                <div class="col-md-4 col-12">
                                                    <select name="country_code" class="form-control select2">
                                                        @foreach($countries as $country)
                                                            <option value="{{ $country->key }}"
                                                                @if ($row->country_code == $country->key) selected @endif>

                                                               
                                                            {{ '+'.$country->key }}{{ $country->flag}}</option>
                                                        @endforeach
                                                    </select>
                                                 
                                                </div>
                                                <div class="col-md-8 col-12">
                                                    <div class="controls">
                                                        <input type="number" name="phone" value="{{ '0' . $row->phone }}" class="form-control" placeholder="{{ __('admin.enter_phone_number') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}" data-validation-number-message="{{ __('admin.the_phone_number_ must_not_have_charachters_or_symbol') }}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.email') }}</label>
                                            <div class="controls">
                                                <input type="email" name="email" value="{{ $row->email }}" class="form-control" placeholder="{{ __('admin.enter_the_email') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}" data-validation-email-message="{{ __('admin.email_formula_is_incorrect') }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.password') }}</label>
                                            <div class="controls">
                                                <input type="password" name="password" class="form-control">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="region_id">{{__('admin.Region')}}</label>
                                            <div class="controls">
                                                <select name="region_id" id="region_id" class="form-control select2" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                    <option value="">{{__('admin.choose_the_region')}}</option>
                                                    @foreach($regions as $region)
                                                        <option value="{{ $region->id }}" {{ (old('region_id', $row->region_id) == $region->id) ? 'selected' : '' }}>{{ $region->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="city_id">{{__('admin.City')}}</label>
                                            <div class="controls">
                                                <select name="city_id" id="city_id" class="form-control select2" >
                                                    <option value="">{{__('admin.select_city')}}</option>
                                                    @foreach($cities as $city)
                                                        <option value="{{ $city->id }}" {{ (old('city_id', $row->city_id) == $city->id) ? 'selected' : '' }}>{{ $city->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>


                                    {{-- gender --}}
<div class="col-md-6">

                                        <div class="form-group">
                                            <label>{{ __('admin.gender') }}</label>
                                            <select name="gender" class="form-control">
                                                <option value="male" {{ $row->gender == 'male' ? 'selected' : '' }}>{{
                                                    __('admin.male') }}</option>
                                                <option value="female" {{ $row->gender == 'female' ? 'selected' : ''
                                                    }}>{{ __('admin.female') }}</option>
                                            </select>
                                        </div>
                                    </div>

                                     <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="active">{{ __('admin.active') }}</label>
                                            <select name="active" class="select2 form-control">
                                                <option value="1" {{ old('active', $row->active ?? '') == '1' ?
                                                    'selected' : '' }}>
                                                    {{ __('admin.active') }}
                                                </option>
                                                <option value="0" {{ old('active', $row->active ?? '') == '0' ?
                                                    'selected' : '' }}>
                                                    {{ __('admin.inactive') }}
                                                </option>
                                            </select>
                                            @error('active')
                                            <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>


                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.Validity') }}</label>
                                            <div class="controls">
                                                <select name="is_blocked" class="select2 form-control" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                    <option value="">{{ __('admin.Select_the_blocking_status') }}</option>
                                                    <option value="1" {{ $row->is_blocked == 1 ? 'selected' : '' }}>{{ __('admin.Prohibited') }}</option>
                                                    <option value="0" {{ $row->is_blocked == 0 ? 'selected' : '' }}>{{ __('admin.Unspoken') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{ __('admin.update') }}</button>
                                        <a href="{{ url()->previous() }}" type="reset" class="btn btn-outline-warning mr-1 mb-1">{{ __('admin.back') }}</a>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>
@endsection

@section('js')
    <script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>

    {{-- show selected image script --}}
    @include('admin.shared.addImage')
    {{-- show selected image script --}}

    {{-- submit edit form script --}}
    @include('admin.shared.submitEditForm')
    {{-- submit edit form script --}}
    <script>
        $(document).ready(function() {
            $('#region_id').on('change', function() {
                var regionId = $(this).val();
                var citySelect = $('#city_id');

                // Clear cities dropdown
                citySelect.empty().append('<option value="">{{__('admin.select_city')}}</option>');

                if (regionId) {
                    // Show loading state
                    citySelect.append('<option value="">{{__('admin.loading')}}...</option>');

                    // Make AJAX request to get cities
                    $.ajax({
                        url: '/api/region/' + regionId + '/cities',
                        type: 'GET',
                        dataType: 'json',
                        success: function(response) {
                            // Clear loading state
                            citySelect.empty().append('<option value="">{{__('admin.select_city')}}</option>');

                            // Populate cities
                            if (response.data && response.data.length > 0) {
                                $.each(response.data, function(index, city) {
                                    citySelect.append('<option value="' + city.id + '">' + city.name + '</option>');
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error loading cities:', error);
                            citySelect.empty().append('<option value="">{{__('admin.select_city')}}</option>');
                            // You can add a toast notification here if needed
                        }
                    });
                }
            });
        });
    </script>
@endsection
