<?php

namespace App\Models;

use Spa<PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Auth;

class Product extends BaseModel implements HasMedia
{
    use HasFactory, SoftDeletes , InteractsWithMedia , HasTranslations;

    public $translatable = ['name'];

    protected $fillable = [
        'provider_id',
        'product_category_id',
        'name',
        'price',
        'quantity',
        'description',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];
    public function registerMediaCollections(): void
    {

        $this->addMediaCollection('product-images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));
    }

    /**
     * Get all ratings for this product
     */
    public function rates()
    {
        return $this->morphMany(Rate::class, 'rateable');
    }

    /**
     * Get all favorites for this product
     */
    public function favorites()
    {
        return $this->morphMany(Favorite::class, 'favoritable');
    }

    /**
     * Get the average rating for the product
     *
     * @return float
     */
    public function getAverageRateAttribute()
    {
        return $this->rates()->avg('rate') ?? 0;
    }

    /**
     * Get the total count of ratings for the product
     *
     * @return int
     */
    public function getRatesCountAttribute()
    {
        return $this->rates()->count();
    }

    /**
     * Get formatted average rating (rounded to 1 decimal place)
     *
     * @return float
     */
    public function getFormattedAverageRateAttribute()
    {
        return round($this->average_rate, 1);
    }

    /**
     * Check if the product is favorited by the current authenticated user
     *
     * @return bool
     */
    public function getIsFavoriteAttribute()
    {
        if (!Auth::check()) {
            return false;
        }

        return $this->favorites()
            ->where('user_id', Auth::id())
            ->exists();
    }


    public function getProductImagesUrlsAttribute()
    {
        return $this->getMedia('product-images')->map(function ($media) {
            return $media->getUrl();
        });
    }

    /**
     * Get the first product image URL
     */
    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('product-images') ?: asset('storage/images/default.png');
    }

    /**
     * Get the first product image URL (alias for consistency)
     */
    public function getImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('product-images') ?: asset('storage/images/default.png');
    }


    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    public function scopeForProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Custom search scope for products with translatable fields
     */
    public function scopeSearch($query, $searchArray = [])
    {
        $query->where(function ($query) use ($searchArray) {
            if ($searchArray) {
                foreach ($searchArray as $key => $value) {
                    if (str_contains($key, '_id')) {
                        if ($value != null) {
                            $query->where($key, $value);
                        }
                    } elseif ($key == 'order') {
                        // Skip order parameter
                    } elseif ($key == 'created_at_min') {
                        if ($value != null) {
                            $query->whereDate('created_at', '>=', $value);
                        }
                    } elseif ($key == 'created_at_max') {
                        if ($value != null) {
                            $query->whereDate('created_at', '<=', $value);
                        }
                    } elseif ($key == 'name' && $value != null) {
                        // Search in translatable name field
                        $query->where(function($q) use ($value) {
                            $q->where('name->ar', 'like', "%{$value}%")
                              ->orWhere('name->en', 'like', "%{$value}%");
                        });
                    } elseif ($key == 'description' && $value != null) {
                        // Search in description field
                        $query->where('description', 'like', "%{$value}%");
                    } elseif ($key == 'is_active' && $value !== '' && $value !== null) {
                        $query->where('is_active', $value);
                    } else {
                        if ($value != null) {
                            $query->where($key, 'like', "%{$value}%");
                        }
                    }
                }
            }
        });
        return $query->orderBy('created_at', request()->searchArray && request()->searchArray['order'] ? request()->searchArray['order'] : 'DESC');
    }

    /**
     * Get the display name for the product
     * Handles both translatable and simple string formats
     */
    public function getDisplayNameAttribute()
    {
        if (is_array($this->name)) {
            // If name is already an array (proper translatable format)
            return $this->getTranslation('name', app()->getLocale())
                ?: $this->getTranslation('name', 'ar')
                ?: $this->getTranslation('name', 'en')
                ?: 'No Name';
        } elseif (is_string($this->name)) {
            // If name is a simple string (legacy format)
            return $this->name;
        } else {
            return 'No Name';
        }
    }


    public function rateSummary(): Attribute
    {
        return Attribute::make(
            get: function () {
                $average = $this->rates()->avg('rate') ?? 0;
                $count = $this->rates()->count();

                return [
                    'average' => round($average, 2),
                    'count' => $count,
                ];
            }
        );
    }

}