<?php

namespace App\Services;

class Responder
{
    /**
     * Return success response with status code
     *
     * @param mixed $data
     * @param array|string $extra
     * @param int $code
     * @return \Illuminate\Http\JsonResponse
     */
    public static function success($data = [], $extra = [], $code = 200)
    {
        if (is_string($data)) {
            return response()->json([
                'status' => $code,
                'message' => $data
            ], $code);
        }

        if (is_string($extra)) {
            $response = [
                'status' => $code,
                'message' => $extra
            ];

            if ($data !== null) {
                $response['data'] = $data;
            }

            return response()->json($response, $code);
        }

        $response = [
            'status' => $code,
            'message' => $extra['message'] ?? __('apis.success')
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        foreach ($extra as $key => $value) {
            if ($key !== 'message') {
                $response[$key] = $value;
            }
        }

        return response()->json($response, $code);
    }

    /**
     * Return error response with status code
     *
     * @param string $message
     * @param array $dataOrErrors
     * @param int $code
     * @return \Illuminate\Http\JsonResponse
     */
    public static function error($message, $dataOrErrors = [], $code = 422)
    {
        $response = [
            'status'  => $code,
            'message' => $message,
            'data'    => null,
        ];

        if (!empty($dataOrErrors)) {
            // لو شكل البيانات فيه id نعتبره بيانات وليست errors
            if (is_array($dataOrErrors) && isset($dataOrErrors['id'])) {
                $response['data'] = $dataOrErrors;
            } else {
                $response['errors'] = $dataOrErrors;
            }
        }

        return response()->json($response, $code);
    }

    /**
     * Return a fully custom response (for full control)
     *
     * @param array $structure
     * @return \Illuminate\Http\JsonResponse
     */
    public static function custom(array $structure)
    {
        return response()->json($structure, $structure['status'] ?? 200);
    }

    /**
     * Return paginated response
     *
     * @param mixed $resourceCollection
     * @return \Illuminate\Http\JsonResponse
     */
    public static function paginated($resourceCollection)
    {
        if (method_exists($resourceCollection, 'items')) {
            return response()->json([
                'status' => 200,
                'message' => __('apis.success'),
                'data' => $resourceCollection->items(),
                'pagination' => [
                    'current_page' => $resourceCollection->currentPage(),
                    'last_page' => $resourceCollection->lastPage(),
                    'per_page' => $resourceCollection->perPage(),
                    'total' => $resourceCollection->total(),
                    'from' => $resourceCollection->firstItem(),
                    'to' => $resourceCollection->lastItem(),
                ]
            ]);
        }

        // التعامل مع Collections عادية (غير Resource)
        $page = request()->get('page', 1);
        $perPage = request()->get('per_page', 15);
        $offset = ($page - 1) * $perPage;

        $total = $resourceCollection->count();
        $paginatedItems = $resourceCollection->slice($offset, $perPage)->values();

        return response()->json([
            'status' => 200,
            'message' => __('apis.success'),
            'data' => $paginatedItems,
            'pagination' => [
                'current_page' => (int) $page,
                'last_page' => (int) ceil($total / $perPage),
                'per_page' => (int) $perPage,
                'total' => $total,
                'from' => $offset + 1,
                'to' => min($offset + $perPage, $total),
            ]
        ]);
    }
}
