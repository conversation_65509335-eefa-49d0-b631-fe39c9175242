<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use Illuminate\Http\Request;
use App\Services\ProviderService;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Client\ProviderResource;
use App\Http\Resources\Api\Client\ProviderDetailsResource;

class ProviderController extends Controller
{
    public function __construct(protected ProviderService $providerService)
    {
    }


    public function getProviderWithCity($cityId)
    {
        $data = $this->providerService->getProviderByCity($cityId);
        return Responder::success(ProviderResource::collection($data));
    }

    /**
     * Get providers based on user's gender preference and order history
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProvidersForUser(Request $request)
    {
            $user = auth()->user();

            $providers = $this->providerService->getProvidersForUser($user);

            return Responder::success(ProviderResource::collection($providers)
            );


    }


    /**
     * Get most ordered providers
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMostOrderedProviders(Request $request)
    {
        try {

            $providers = $this->providerService->mostOrdered();

            return Responder::success(ProviderResource::collection($providers));

        } catch (\Exception $e) {
            return Responder::error('Failed to retrieve most ordered providers', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Filter and search providers with advanced options
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filterProviders(Request $request)
    {
        try {
            $filters = [
                'search' => $request->input('search'), // Search in commercial_name or service names
                'gender' => $request->input('gender'), // male, female
                'provider_type' => $request->input('provider_type'), // salon, beauty_center
                'sort_by' => $request->input('sort_by', 'latest'), // latest, name_asc, name_desc, rates_desc, rates_asc
                'city_id' => $request->input('city_id'),
                'service_type' => $request->input('service_type'), // home, salon
                'category_id' => $request->input('category_id'), // filter by service category
                'limit' => $request->input('limit', 15),
                'page' => $request->input('page', 1)
            ];

            $providers = $this->providerService->filterProviders($filters);

            return Responder::success(ProviderResource::collection($providers));

        } catch (\Exception $e) {
            return Responder::error('Failed to filter providers', ['error' => $e->getMessage()], 500);
        }
    }

public function show(int $id)
{
    $provider = $this->providerService->getById($id);

    if (!$provider) {
        return Responder::error('not_found', [], 404);
    }

    $provider->load(['activeServices', 'activeProducts', 'WorkingHours', 'rates']);

    return Responder::success(ProviderDetailsResource::make($provider));
}

}