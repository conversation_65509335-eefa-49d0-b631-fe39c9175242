<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spa<PERSON>\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class PaymentMethod extends BaseModel implements HasMedia
{
    use HasTranslations , InteractsWithMedia ;
    public $translatable = ['name'];
    protected $fillable = ['name' , 'is_active'];

    public function registerMediaCollections(): void
    {

        $this->addMediaCollection('payment-methods')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));
    }

}
