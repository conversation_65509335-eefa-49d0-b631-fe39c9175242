<?php
namespace App\Models;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShortVideo extends BaseModel implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'video_id',
        'order_rate_id',
        'client_name',
        'published_at',
        'expired_at',
        'is_active',
    ];

    // Media collection for video
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('short_video')
            ->acceptsMimeTypes(['video/mp4', 'video/quicktime', 'video/x-msvideo'])
            ->useDisk('public'); // Adjust if using a different disk
    }

    public function orderRate()
    {
        return $this->belongsTo(OrderRate::class);
    }
}
