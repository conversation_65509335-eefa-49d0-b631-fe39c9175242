<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $walletCreditUsed = $this->wallet_credit_used ?? 0;
        $amountPaid = $this->amount_paid;
        $amountDue = max($amountPaid - $walletCreditUsed, 0);

        $baseData = [
            'id' => $this->id,

            'advertisement' => [
                'id' => $this->advertisement->id ?? null,
                'name' => $this->advertisement->name ?? null,
                'price' => $this->advertisement->price ?? $amountPaid,
            ],

            'user' => request()->routeIs('client.purchase-requests.buyer') 
                ? [
                    'id' => $this->seller->id ?? null,
                    'name' => $this->seller->name ?? null,
                    'current_wallet_balance' => $this->seller->wallet_balance ?? 0,
                ]
                : [
                    'id' => $this->buyer->id ?? null,
                    'name' => $this->buyer->name ?? null,
                    'current_wallet_balance' => $this->buyer->wallet_balance ?? 0,
                ],

            'wallet_credit_used' => $walletCreditUsed,

            'cost_details' => [
                'price' => $amountPaid,
                'wallet_applied' => $walletCreditUsed,
                'amount_due' => $amountDue,
            ],

            'problem_details' => $this->when(isset($this->problem_details), function () {
    return [
        'reason'     => $this->problem_details['reason'],
        'created_at' => $this->problem_details['created_at']->format('Y-m-d H:i:s'),
    ];
}),


            'payment_method' => $this->payment_method,
            'status' => $this->status,
'created_at' => optional($this->created_at)->format('Y-m-d H:i'),

                   'rate' => RatingResource::collection(
    $this->whenLoaded('ratings', function () {
        return $this->ratings->where('status', 'approved');
    })
),




            // 'ratings' => $this->whenLoaded('ratings', function () {
            //     return $this->ratings->map(function ($rating) {
            //         return [
            //             'id' => $rating->id,
            //             'rater' => [
            //                 'id' => $rating->rater_id,
            //                 'name' => optional($rating->rater)->name,
            //             ],
            //             'stars' => $rating->stars,
            //             'comment' => $rating->comment,
            //             'created_at' => $rating->created_at->format('Y-m-d H:i:s'),
            //         ];
            //     });
            // }),
        ];

    //   'user' => (request()->routeIs('client.purchase-request.show')) {
    //   'user' => (request()->routeIs('client.purchase-request.show')) {
    //                 $baseData['product_commission'] = $this->product_commission;
    //                 $baseData['amount_to_seller'] = $this->price - $this->product_commission;
    //             }

        return $baseData;
    }
}
