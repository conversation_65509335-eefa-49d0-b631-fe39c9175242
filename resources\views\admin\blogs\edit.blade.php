@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css"
        href="{{ asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">

@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                {{-- <div class="card-header">
                    <h4 class="card-title">{{__('admin.update') . ' ' . __('admin.blog')}}</h4>
                </div> --}}
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="{{route('admin.blogs.update' , ['id' => $blog->id])}}" class="store form-horizontal" enctype="multipart/form-data" novalidate>
                            @csrf
                            @method('PUT')
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-12">
                                        <div class="col-12">
                                            <ul class="nav nav-tabs mb-3">
                                                    @foreach (languages() as $lang)
                                                        <li class="nav-item">
                                                            <a class="nav-link @if($loop->first) active @endif"  data-toggle="pill" href="#first_{{$lang}}" aria-expanded="true">{{  __('admin.data') }} {{ $lang }}</a>
                                                        </li>
                                                    @endforeach
                                            </ul>
                                        </div>

                                        <div class="col-12">
                                            <div class="imgMontg col-12 text-center">
                                                <div class="dropBox">
                                                    <div class="textCenter">
                                                        <div class="imagesUploadBlock">
                                                            <label class="uploadImg">
                                                                <span><i class="feather icon-image"></i></span>
                                                                <input type="file" accept="image/*" name="image" class="imageUploader">
                                                            </label>
                                            
                                                            {{-- Display uploaded image preview --}}
                                                            <div class="uploadedBlock">
                                                                <img src="{{ $blog->getFirstMediaUrl('blogs') }}">
                                                                <button class="close"><i class="feather icon-x"></i></button>
                                                            </div>
                                                        </div>
                                            
                                                        {{-- ✅ Validation error BELOW image block --}}
                                                        @error('image')
                                                            <div class="text-danger mt-2">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                            
                                        </div>

                                    {{-- Language tabs content --}}
                                       <div class="tab-content">
                                                @foreach (languages() as $lang)
                                                    <div role="tabpanel" class="tab-pane fade @if($loop->first) show active @endif " id="first_{{$lang}}" aria-labelledby="first_{{$lang}}" aria-expanded="true">
                                                        <div class="row">
                                                            <div class="col-md-6 col-12">
                                                                <div class="form-group">
                                                                    <label for="title_{{$lang}}">{{__('admin.blog_title')}} {{ $lang }}</label>
                                                                    <div class="controls">
                                                                        <input type="text" value="{{$blog->getTranslations('title')[$lang]??''}}" name="title[{{$lang}}]" id="title_{{$lang}}" class="form-control" placeholder="{{__('admin.write') . ' ' . __('admin.blog_title')}} {{ $lang }}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @if($loop->first)
                                                            <div class="col-md-6 col-12">
                                                                <div class="form-group">
                                                                    <label for="category_id">{{__('admin.blog_category')}}</label>
                                                                    <div class="controls">
                                                                        <select name="category_id" id="category_id" class="form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                                            <option value="">{{__('admin.select_category')}}</option>
                                                                            @foreach($categories as $category)
                                                                                <option value="{{$category->id}}" {{$blog->category_id == $category->id ? 'selected' : ''}}>{{$category->name}}</option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @endif
                                                        </div>
                                                        <div class="row">
                                                            @if($loop->first)
                                                            <div class="col-md-6 col-12">
                                                                <div class="form-group">
                                                                    <label for="is_active">{{__('admin.status')}}</label>
                                                                    <div class="controls">
                                                                        <select name="is_active" id="is_active" class="form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                                            <option value="1" {{$blog->is_active == 1 ? 'selected' : ''}}>{{__('admin.active')}}</option>
                                                                            <option value="0" {{$blog->is_active == 0 ? 'selected' : ''}}>{{__('admin.inactive')}}</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @endif
                                                            <div class="col-md-12 col-12">
                                                                <div class="form-group">
                                                                    <label for="content_{{$lang}}">{{__('admin.blog_content')}} {{ $lang }}</label>
                                                                    <div class="controls">
                                                                        <textarea name="content[{{$lang}}]" id="content_{{$lang}}" class="form-control" placeholder="{{__('admin.write') . ' ' . __('admin.blog_content')}} {{ $lang }}">{{$blog->getTranslations('content')[$lang]??''}}</textarea>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                           
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                            <button type="submit"
                                                class="btn btn-primary mr-1 mb-1 submit_button">{{ __('admin.update') }}</button>
                                            <a href="{{ url()->previous() }}" type="reset"
                                                class="btn btn-outline-warning mr-1 mb-1">{{ __('admin.back') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('js')
    <script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>

    <!-- CKEditor JS -->
    <script src="https://cdn.ckeditor.com/4.16.2/full-all/ckeditor.js"></script>

    {{-- show selected image script --}}
    @include('admin.shared.addImage')
    {{-- show selected image script --}}

    {{-- submit edit form script --}}
    @include('admin.shared.submitEditForm')
    {{-- submit edit form script --}}

    <script>
        $(document).ready(function() {
            // Initialize CKEditor for all content textareas
            @foreach(languages() as $lang)
                CKEDITOR.replace('content_{{$lang}}', {
                    height: 300,
                    toolbar: [
                        { name: 'document', items: [ 'Source', '-', 'Save', 'NewPage', 'Preview', 'Print', '-', 'Templates' ] },
                        { name: 'clipboard', items: [ 'Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo' ] },
                        { name: 'editing', items: [ 'Find', 'Replace', '-', 'SelectAll', '-', 'Scayt' ] },
                        '/',
                        { name: 'basicstyles', items: [ 'Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'CopyFormatting', 'RemoveFormat' ] },
                        { name: 'paragraph', items: [ 'NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl' ] },
                        { name: 'links', items: [ 'Link', 'Unlink', 'Anchor' ] },
                        { name: 'insert', items: [ 'Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe' ] },
                        '/',
                        { name: 'styles', items: [ 'Styles', 'Format', 'Font', 'FontSize' ] },
                        { name: 'colors', items: [ 'TextColor', 'BGColor' ] },
                        { name: 'tools', items: [ 'Maximize', 'ShowBlocks' ] }
                    ]
                });
            @endforeach

            // Custom validation for CKEditor
            $('form.store').on('submit', function(e) {
                // Sync CKEditor content to textareas
                for (var instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].updateElement();
                }

                var isValid = true;
                @foreach(languages() as $lang)
                    var content_{{$lang}} = CKEDITOR.instances['content_{{$lang}}'].getData().trim();
                    if (content_{{$lang}} === '' || content_{{$lang}} === '<p>&nbsp;</p>' || content_{{$lang}} === '<p></p>') {
                            alert('{{__("admin.blog_content")}} {{$lang}} {{__("admin.this_field_is_required")}}');
                        isValid = false;
                        return false;
                    }
                @endforeach

                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>
@endsection
