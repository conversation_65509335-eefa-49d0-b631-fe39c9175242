<?php

namespace App\Http\Requests\Api\Course;

use App\Http\Requests\Api\BaseApiRequest;

class ConfirmCoursePaymentRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'payment_reference' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'payment_reference.string' => 'Payment reference must be a string',
            'payment_reference.max' => 'Payment reference cannot exceed 255 characters',
        ];
    }
}
