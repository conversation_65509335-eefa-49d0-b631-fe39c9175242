<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class FavoriteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'item_id' => 'required|integer',
            'item_type' => 'required|in:product,service,provider',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $itemType = $this->input('item_type');
            $itemId = $this->input('item_id');

            if ($itemType && $itemId) {
                // Check if the item exists based on type
                $exists = false;

                if ($itemType === 'product') {
                    $exists = \App\Models\Product::where('id', $itemId)->exists();
                } elseif ($itemType === 'service') {
                    $exists = \App\Models\Service::where('id', $itemId)->exists();
                } elseif ($itemType === 'provider') {
                    $exists = \App\Models\Provider::where('id', $itemId)->exists();
                }

                if (!$exists) {
                    $validator->errors()->add('item_id', 'The selected item does not exist.');
                }
            }
        });
    }
}
