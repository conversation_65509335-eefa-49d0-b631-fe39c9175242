<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderStatus extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'provider_sub_order_id',
        'status',
        'statusable_id',
        'statusable_type',
        'map_desc',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the provider sub-order that owns the status change
     */
    public function providerSubOrder()
    {
        return $this->belongsTo(ProviderSubOrder::class);
    }

    /**
     * Get the main order (direct relationship)
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the statusable model (user, provider, delegate)
     */
    public function statusable()
    {
        return $this->morphTo();
    }

    /**
     * Scope to get status changes for a specific order
     */
    public function scopeForOrder($query, $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    /**
     * Scope to get changes by user type
     */
    public function scopeByUserType($query, $userType)
    {
        return $query->where('statusable_type', $userType);
    }

    /**
     * Scope to get changes by specific user
     */
    public function scopeByUser($query, $userType, $userId)
    {
        return $query->where('statusable_type', $userType)
                    ->where('statusable_id', $userId);
    }

    /**
     * Get formatted status description
     */
    public function getStatusDescriptionAttribute()
    {
        return ucfirst(str_replace('_', ' ', $this->status));
    }

    /**
     * Get the person who made the change
     */
    public function getChangedByNameAttribute()
    {
        if (!$this->statusable) {
            return 'Unknown';
        }

        switch ($this->statusable_type) {
            case 'App\Models\User':
                return $this->statusable->name ?? 'User';
            case 'App\Models\Provider':
                return $this->statusable->commercial_name ?? $this->statusable->user->name ?? 'Provider';
            case 'App\Models\Admin':
                return $this->statusable->name ?? 'Admin';
            default:
                return 'Unknown';
        }
    }

    /**
     * Get the user type who made the change
     */
    public function getChangedByTypeAttribute()
    {
        switch ($this->statusable_type) {
            case 'App\Models\User':
                return 'user';
            case 'App\Models\Provider':
                return 'provider';
            case 'App\Models\Admin':
                return 'admin';
            default:
                return 'unknown';
        }
    }

    /**
     * Create a new status change record for provider sub-order
     */
    public static function createStatusChange($providerSubOrderId, $status, $statusableType, $statusableId, $mapDesc = null)
    {
        return self::create([
            'provider_sub_order_id' => $providerSubOrderId,
            'status' => $status,
            'statusable_type' => $statusableType,
            'statusable_id' => $statusableId,
            'map_desc' => $mapDesc,
        ]);
    }

    /**
     * Create a new status change record for main order
     */
    public static function createOrderStatusChange($orderId, $status, $statusableType, $statusableId, $mapDesc = null)
    {
        return self::create([
            'order_id' => $orderId,
            'status' => $status,
            'statusable_type' => $statusableType,
            'statusable_id' => $statusableId,
            'map_desc' => $mapDesc,
        ]);
    }
}
