<?php
namespace App\Services;

use App\Models\Advertisement;
use App\Models\User;
use App\Repositories\FavoriteRepository;

class FavoriteService
{
    public function __construct(protected FavoriteRepository $repository) {}

 public function toggleFavorite(User $user, $advertisementId)
{
    $advertisement = Advertisement::find($advertisementId);

    if (!$advertisement) {
        return [
            'message' => 'الإعلان غير موجود',
            'status' => false,
            'data' => null,
        ];
    }

    if ($user->favorites()->where('advertisement_id', $advertisementId)->exists()) {
        $user->favorites()->detach($advertisementId);
        return [
            'message' => 'تمت الإزالة من المفضلة',
            'status' => false,
            'data' => null,
        ];
    } else {
        $user->favorites()->attach($advertisementId);
        return [
            'message' => 'تمت الإضافة إلى المفضلة',
            'status' => true,
            // 'data' => $advertisement,
        ];
    }
}


    public function getUserFavorites($userId)
    {
        return $this->repository->getUserFavorites($userId);
    }
}
