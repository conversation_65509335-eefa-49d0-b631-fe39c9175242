<?php

namespace App\Http\Requests\Api\Course;

use App\Http\Requests\Api\BaseApiRequest;

class EnrollCourseRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'payment_method' => 'required|in:wallet,bank_transfer,credit_card,mada,apple_pay',
            'bank_account_id' => 'required_if:payment_method,bank_transfer|integer|exists:provider_bank_accounts,id',
            'gateway' => 'nullable|string|in:myfatoorah,knet,visa_master,amex,sadad,apple_pay,stc_pay',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'payment_method.required' => 'Payment method is required',
            'payment_method.in' => 'Invalid payment method selected',
            'bank_account_id.required_if' => 'Bank account is required for bank transfer payments',
            'bank_account_id.exists' => 'Selected bank account does not exist',
            'gateway.in' => 'Invalid payment gateway selected',
        ];
    }
}
