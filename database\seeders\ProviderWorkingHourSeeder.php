<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderWorkingHourSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Provider working hours for each day of the week

        $providers = [1, 2, 3, 4, 5];
        $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

        $workingHours = [];

        foreach ($providers as $providerId) {
            foreach ($days as $day) {
                // Most providers work Sunday to Thursday
                if (in_array($day, ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'])) {
                    $workingHours[] = [
                        'provider_id' => $providerId,
                        'day' => $day,
                        'is_working' => true,
                        'start_time' => '09:00:00',
                        'end_time' => '18:00:00',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                } elseif ($day === 'friday') {
                    // Some providers work Friday with shorter hours
                    $isWorking = $providerId % 2 === 0; // Even provider IDs work Friday
                    $workingHours[] = [
                        'provider_id' => $providerId,
                        'day' => $day,
                        'is_working' => $isWorking,
                        'start_time' => $isWorking ? '14:00:00' : '09:00:00', // Default time even if not working
                        'end_time' => $isWorking ? '20:00:00' : '18:00:00',   // Default time even if not working
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                } else { // Saturday
                    // Few providers work Saturday
                    $isWorking = $providerId === 1 || $providerId === 3;
                    $workingHours[] = [
                        'provider_id' => $providerId,
                        'day' => $day,
                        'is_working' => $isWorking,
                        'start_time' => $isWorking ? '10:00:00' : '09:00:00', // Default time even if not working
                        'end_time' => $isWorking ? '16:00:00' : '18:00:00',   // Default time even if not working
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }

        DB::table('provider_working_hours')->insert($workingHours);

        $this->command->info('Provider working hours seeded successfully!');
    }
}
