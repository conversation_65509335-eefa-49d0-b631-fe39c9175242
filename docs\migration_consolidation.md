# Migration Consolidation

## Overview
Consolidated separate `add_*_to_*_table` migration files into their main table creation migrations for cleaner schema organization and better dependency management.

## Migrations Consolidated

### **1. Course Enrollments Progress Fields**

#### **Removed Migration**: `2024_01_15_000002_add_progress_fields_to_course_enrollments_table.php`
#### **Consolidated Into**: `2024_01_14_130000_create_course_enrollments_table.php`

**Fields Added:**
```php
$table->decimal('progress_percentage', 5, 2)->default(0)->comment('Course completion percentage');
$table->integer('total_time_spent')->default(0)->comment('Total time spent in seconds');
$table->timestamp('last_accessed_at')->nullable()->comment('Last time student accessed the course');
$table->integer('completed_stages_count')->default(0)->comment('Number of completed stages');

// Indexes added:
$table->index(['progress_percentage']);
$table->index(['last_accessed_at']);
$table->index(['completed_stages_count']);
```

### **2. User Wallet Balance**

#### **Removed Migration**: `2024_01_15_000003_add_wallet_balance_to_users_table.php`
#### **Consolidated Into**: `2020_08_21_000000_create_users_table.php`

**Fields Added:**
```php
$table->decimal('wallet_balance', 10, 2)->default(0)->comment('User wallet balance');
```

### **3. User City ID**

#### **Removed Migration**: `2025_05_18_120058_add_city_id_to_users_table.php`
#### **Already Existed**: City ID was already in the users table creation migration

### **4. User Loyalty Points**

#### **Removed Migration**: `2025_05_28_120000_add_loyalty_points_to_users_table.php`
#### **Consolidated Into**: `2020_08_21_000000_create_users_table.php`

**Fields Added:**
```php
$table->integer('loyalty_points')->default(0)->comment('User loyalty points');
```

### **5. Orders Booking and Delivery Fields**

#### **Removed Migration**: `2025_05_28_140000_add_booking_and_delivery_fields_to_orders.php`
#### **Consolidated Into**: `2025_05_28_010023_create_orders_table.php`

**Fields Added:**
```php
$table->enum('booking_type', ['home', 'salon'])->nullable()->comment('Service booking type');
$table->enum('delivery_type', ['normal', 'express'])->nullable()->comment('Product delivery type');
```

### **6. Home Service Fee for Carts and Orders**

#### **Removed Migration**: `2025_05_29_000001_add_home_service_fee_to_carts_and_orders.php`
#### **Consolidated Into**: 
- `2025_05_28_004243_create_carts_table.php`
- `2025_05_28_010023_create_orders_table.php`

**Fields Added:**
```php
// In both carts and orders tables:
$table->decimal('home_service_fee', 12, 2)->default(0)->comment('Fee for home service');
```

### **7. Orders Bank Account ID**

#### **Removed Migration**: `2025_05_29_000002_add_bank_account_id_to_orders_table.php`
#### **Consolidated Into**: `2025_05_28_010023_create_orders_table.php`
#### **Foreign Key Added**: `2025_05_25_212261_add_foreign_keys_to_orders.php`

**Fields Added:**
```php
// In orders table:
$table->unsignedBigInteger('bank_account_id')->nullable()->comment('Bank account for bank transfer payments');

// Foreign key added later (after provider_bank_accounts table exists):
$table->foreign('bank_account_id')->references('id')->on('provider_bank_accounts')->onDelete('set null');
```

## Migrations Kept (Data Seeding)

### **1. Loyalty Points Settings**
**File**: `2025_05_28_130000_add_loyalty_points_settings.php`
**Reason**: Seeds site_settings table with loyalty points configuration data

### **2. Fee Settings**
**File**: `2025_05_29_000000_add_fee_settings.php`
**Reason**: Seeds site_settings table with fee configuration data

## Updated Table Structures

### **Users Table (Final Structure)**
```php
Schema::create('users', function (Blueprint $table) {
    $table->id();
    $table->string('name', 50);
    $table->string('country_code', 5)->default('965');
    $table->string('phone', 15);
    $table->string('email', 50)->nullable();
    $table->string('password', 100);
    $table->enum('gender', ['male', 'female'])->nullable();
    $table->enum('type', ['client', 'provider'])->default('client');
    $table->enum('status', ['pending','active', 'blocked', 'rejected', 'deleted'])->default('pending');
    $table->boolean('is_notify')->default(true);
    $table->string('code', 10)->nullable();
    $table->timestamp('code_expire')->nullable();
    $table->foreignId('region_id')->nullable()->constrained()->cascadeOnDelete();
    $table->foreignId('city_id')->nullable()->constrained()->cascadeOnDelete();
    $table->decimal('wallet_balance', 10, 2)->default(0)->comment('User wallet balance');
    $table->integer('loyalty_points')->default(0)->comment('User loyalty points');
    $table->softDeletes();
    $table->timestamp('created_at')->useCurrent();
    $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();
});
```

### **Course Enrollments Table (Final Structure)**
```php
Schema::create('course_enrollments', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->foreignId('course_id')->constrained()->onDelete('cascade');
    $table->timestamp('enrolled_at');
    $table->enum('status', ['pending_payment', 'active', 'suspended', 'completed', 'cancelled'])->default('pending_payment');
    $table->decimal('progress_percentage', 5, 2)->default(0)->comment('Course completion percentage');
    $table->integer('total_time_spent')->default(0)->comment('Total time spent in seconds');
    $table->timestamp('last_accessed_at')->nullable()->comment('Last time student accessed the course');
    $table->integer('completed_stages_count')->default(0)->comment('Number of completed stages');
    $table->timestamp('completed_at')->nullable();

    // Payment information
    $table->enum('payment_method', ['wallet', 'bank_transfer', 'credit_card', 'mada', 'apple_pay']);
    $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
    $table->decimal('amount_paid', 10, 2);
    $table->string('payment_reference')->nullable();
    $table->unsignedBigInteger('bank_account_id')->nullable();
    $table->timestamp('payment_completed_at')->nullable();

    $table->timestamps();

    // Ensure user can only enroll once per course
    $table->unique(['user_id', 'course_id']);

    // Add indexes for better performance
    $table->index(['user_id', 'status']);
    $table->index(['course_id', 'status']);
    $table->index(['payment_status']);
    $table->index(['progress_percentage']);
    $table->index(['last_accessed_at']);
    $table->index(['completed_stages_count']);
});
```

### **Carts Table (Final Structure)**
```php
Schema::create('carts', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
    $table->string('session_id')->nullable();
    $table->enum('type', ['service', 'product', 'mixed'])->default('product');
    $table->unsignedBigInteger('provider_id')->nullable();
    $table->decimal('subtotal', 12, 2)->default(0);
    $table->decimal('discount_amount', 12, 2)->default(0);
    $table->string('coupon_code')->nullable();
    $table->decimal('booking_fee', 12, 2)->default(0);
    $table->decimal('home_service_fee', 12, 2)->default(0)->comment('Fee for home service');
    $table->decimal('delivery_fee', 12, 2)->default(0);
    $table->decimal('total', 12, 2)->default(0);
    $table->integer('loyalty_points_used')->default(0);
    $table->timestamps();
    
    $table->foreign('provider_id')->references('id')->on('providers');
});
```

### **Orders Table (Final Structure)**
```php
Schema::create('orders', function (Blueprint $table) {
    $table->id();
    $table->string('order_number')->unique();
    $table->foreignId('address_id')->constrained()->onDelete('cascade');
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->unsignedBigInteger('provider_id')->nullable();
    $table->enum('status', ['pending_payment', 'processing', 'in_progress', 'completed', 'cancelled', 'refunded'])->default('pending_payment');
    $table->enum('payment_method', ['credit_card', 'wallet', 'bank_transfer', 'apple_pay', 'mada']);
    $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
    $table->decimal('subtotal', 12, 2);
    $table->decimal('discount_amount', 12, 2)->default(0);
    $table->string('coupon_code')->nullable();
    $table->decimal('booking_fee', 12, 2)->default(0);
    $table->decimal('home_service_fee', 12, 2)->default(0)->comment('Fee for home service');
    $table->decimal('delivery_fee', 12, 2)->default(0);
    $table->decimal('platform_commission', 12, 2)->default(0);
    $table->decimal('provider_share', 12, 2)->default(0);
    $table->decimal('total', 12, 2);
    $table->integer('loyalty_points_earned')->default(0);
    $table->integer('loyalty_points_used')->default(0);
    $table->text('cancellation_reason')->nullable();
    $table->timestamp('scheduled_at')->nullable();
    $table->string('invoice_number')->nullable();
    $table->string('payment_reference')->nullable();
    $table->enum('booking_type', ['home', 'salon'])->nullable()->comment('Service booking type');
    $table->enum('delivery_type', ['normal', 'express'])->nullable()->comment('Product delivery type');
    $table->unsignedBigInteger('bank_account_id')->nullable()->comment('Bank account for bank transfer payments');
    $table->timestamps();
    $table->softDeletes();
    
    $table->foreign('provider_id')->references('id')->on('providers');
});
```

## Benefits

### **1. Cleaner Schema Organization**
- ✅ **Single Source**: All table columns defined in one place
- ✅ **Complete Picture**: Easy to see full table structure at a glance
- ✅ **Reduced Complexity**: Fewer migration files to manage

### **2. Better Dependency Management**
- ✅ **No Fragmentation**: Related columns created together
- ✅ **Proper Ordering**: Foreign keys added after referenced tables exist
- ✅ **Clear Dependencies**: Easier to understand table relationships

### **3. Improved Development Workflow**
- ✅ **Fresh Migrations**: Clean database setup for new environments
- ✅ **Easier Testing**: Simpler database reset for tests
- ✅ **Better Documentation**: Complete table structure visible in one file

### **4. Maintenance Benefits**
- ✅ **Fewer Files**: Reduced number of migration files to track
- ✅ **Logical Grouping**: Related changes kept together
- ✅ **Easier Rollbacks**: Simpler to understand what gets rolled back

## Migration Execution Order (After Consolidation)

```bash
1. ✅ Core tables (users, courses, etc.)
2. ✅ Dependent tables (course_enrollments, course_stage_completions)
3. ✅ Provider tables (providers, provider_bank_accounts)
4. ✅ Foreign key additions (after referenced tables exist)
5. ✅ Data seeding (settings, configurations)
```

The migration consolidation provides a cleaner, more maintainable database schema with better dependency management! 🗄️📋✨
