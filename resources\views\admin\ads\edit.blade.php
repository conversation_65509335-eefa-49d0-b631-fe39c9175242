@extends('admin.layout.master')

@section('css')
<link rel="stylesheet" href="{{ asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css') }}">
<link rel="stylesheet" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">
@endsection

@section('content')
<form method="POST" action="{{ route('admin.ads.update', $ad->id) }}" enctype="multipart/form-data" class="form-horizontal" novalidate>
    @csrf
    @method('PUT')

    <section id="multiple-column-form">
        <div class="row">

            {{-- الصورة الرئيسية --}}
            <div class="col-md-3">
                <div class="card card-body">
                    <label>{{ __('admin.main_image') }}</label>
                    <div class="mb-1">
                        <img src="{{ $ad->getFirstMediaUrl('main_image') }}" alt="{{ $ad->name }}" class="img-thumbnail" style="max-height: 200px;">
                    </div>
                    <input type="file" name="main_image" class="form-control">
                </div>
            </div>

            <div class="col-md-9">
                <div class="card">
                    <div class="card-body">
                        <div class="form-body">
                            <div class="row">

                                <div class="col-12">
                                    <ul class="nav nav-tabs mb-3">
                                        @foreach (languages() as $lang)
                                        <li class="nav-item">
                                            <a class="nav-link @if($loop->first) active @endif" data-toggle="tab" href="#tab_{{ $lang }}">{{ __('admin.data') }} {{ $lang }}</a>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>

                                <div class="col-12">
                                    <div class="tab-content">
                                        @foreach (languages() as $lang)
                                        <div class="tab-pane fade @if($loop->first) show active @endif" id="tab_{{ $lang }}">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label>{{ __('admin.name') }} ({{ $lang }})</label>
                                                    <input type="text" name="name[{{ $lang }}]" class="form-control" value="{{ $ad->getTranslation('name', $lang) }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>{{ __('admin.description') }} ({{ $lang }})</label>
                                                    <textarea name="description[{{ $lang }}]" class="form-control">{{ $ad->getTranslation('description', $lang) }}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.user') }}</label>
                                    <select name="user_id" class="form-control select2">
                                        @foreach($users as $user)
                                        <option value="{{ $user->id }}" @selected($ad->user_id == $user->id)>{{ $user->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.price') }}</label>
                                    <input type="number" name="price" class="form-control" value="{{ $ad->price }}">
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.main_category') }}</label>
                                    <select name="main_category_id" class="form-control select2">
                                        @foreach($Categories as $category)
                                        <option value="{{ $category->id }}" @selected($ad->main_category_id == $category->id)>{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.sub_category') }}</label>
                                    <select name="sub_category_id" class="form-control select2">
                                        @foreach($Categories as $category)
                                        <option value="{{ $category->id }}" @selected($ad->sub_category_id == $category->id)>{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.city') }}</label>
                                    <select name="city_id" class="form-control select2">
                                        @foreach($cities as $city)
                                        <option value="{{ $city->id }}" @selected($ad->city_id == $city->id)>{{ $city->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.region') }}</label>
                                    <select name="region_id" class="form-control select2">
                                        @foreach($regions as $region)
                                        <option value="{{ $region->id }}" @selected($ad->region_id == $region->id)>{{ $region->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.whatsapp_contact') }}</label>
                                    <input type="text" name="whatsapp_contact" class="form-control" value="{{ $ad->whatsapp_contact }}">
                                </div>

                                <div class="col-md-6">
                                    <label>{{ __('admin.gender') }}</label>
                                    <select name="gender" class="form-control select2">
                                        <option value="">{{ __('admin.choose_gender') }}</option>
                                        <option value="male" @selected($ad->gender == 'male')>{{ __('admin.male') }}</option>
                                        <option value="female" @selected($ad->gender == 'female')>{{ __('admin.female') }}</option>
                                        <option value="both" @selected($ad->gender == 'both')>{{ __('admin.both') }}</option>
                                    </select>
                                </div>

                        @php
    $statuses = [
        'under_review' => __('admin.under_review'),
        'active' => __('admin.active'),
        'rejected' => __('admin.rejected'),
        'hidden' => __('admin.hidden'),
        'cancelled' => __('admin.cancelled'),
        'awaiting_sale_confirm' => __('admin.awaiting_sale_confirm'),
        'awaiting_purchase_confirm' => __('admin.awaiting_purchase_confirm'),
        'sold' => __('admin.sold'),
    ];

    $selectedStatus = old('status', $ad->status ?? 'under_review');
@endphp

<div class="col-md-6">
    <label>{{ __('admin.status') }}</label>
    <select name="status" class="form-control select2">
        @foreach ($statuses as $key => $label)
            <option value="{{ $key }}" @selected($selectedStatus == $key)>{{ $label }}</option>
        @endforeach
    </select>
</div>


<style>
    .image-wrapper {
        position: relative;
        display: inline-block;
    }

    .image-wrapper img {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 6px;
    }

    .delete-icon {
        position: absolute;
        top: 5px;
        left: 5px;
        background-color: rgba(255, 0, 0, 0.8);
        color: white;
        padding: 5px 7px;
        border-radius: 50%;
        cursor: pointer;
        display: none;
        z-index: 10;
    }

    .image-wrapper:hover .delete-icon {
        display: block;
    }
</style>

<div class="col-md-12 mt-3">
    <label>{{ __('admin.additional_images') }}</label>
    <div class="row" id="existingImages">
        @foreach($ad->getMedia('ads') as $media)
            <div class="col-md-3 mb-2">
                <div class="image-wrapper" data-media-id="{{ $media->id }}">
                    <span class="delete-icon" onclick="removeImage(this)">🗑</span>
                    <img src="{{ $media->getUrl() }}" onclick="toggleDelete(this)">
                    <input type="hidden" name="existing_images[]" value="{{ $media->id }}">
                </div>
            </div>
        @endforeach
    </div>

    <input type="file" name="images[]" class="form-control mt-2" multiple>
</div>


                                <div class="col-12 text-center mt-3">
                                    <button type="submit" class="btn btn-primary">{{ __('admin.save') }}</button>
                                    <a href="{{ url()->previous() }}" class="btn btn-outline-secondary">{{ __('admin.back') }}</a>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</form>
@endsection

@section('js')
<script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
<script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
<script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
<script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>
<script>
    function toggleDelete(imgElement) {
        const wrapper = imgElement.closest('.image-wrapper');
        const deleteIcon = wrapper.querySelector('.delete-icon');

        // إظهار أو إخفاء علامة السلة
        deleteIcon.style.display = deleteIcon.style.display === 'block' ? 'none' : 'block';
    }

    function removeImage(icon) {
        const wrapper = icon.closest('.image-wrapper');
        wrapper.remove();
    }
</script>



@endsection
