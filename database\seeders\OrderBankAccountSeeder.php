<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderBankAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Bank account details for bank transfer orders

        DB::table('order_bank_accounts')->insert([
            [
                'order_id' => 4, // Order with bank transfer payment
                'sender_bank_name' => 'National Bank of Kuwait',
                'sender_account_holder_name' => '<PERSON>',
                'sender_account_number' => '****************',
                'sender_iban' => '******************************',
                'transfer_amount' => 25.50,
                'transfer_reference' => 'TRF_' . \Illuminate\Support\Str::random(10),
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(12),
            ],
            [
                'order_id' => 5, // Another bank transfer order (cancelled)
                'sender_bank_name' => 'Kuwait Finance House',
                'sender_account_holder_name' => '<PERSON>',
                'sender_account_number' => '****************',
                'sender_iban' => '******************************',
                'transfer_amount' => 62.50,
                'transfer_reference' => 'TRF_' . \Illuminate\Support\Str::random(10),
                'created_at' => now()->subHours(8),
                'updated_at' => now()->subHours(2),
            ],
        ]);

        $this->command->info('Order bank accounts seeded successfully!');
    }
}
