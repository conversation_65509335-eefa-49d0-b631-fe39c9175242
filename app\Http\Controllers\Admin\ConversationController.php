<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Notifications\NotifyUser;

class ConversationController extends Controller
{
public function index(Request $request)
{
    $conversations = Conversation::with(['messages', 'advertisement'])->latest()->paginate(10);

    if ($request->ajax()) {
        return view('admin.conversations.table', compact('conversations'))->render();
    }

    return view('admin.conversations.index', compact('conversations'));
}



  public function show(Conversation $conversation)
{
    $messages = $conversation->messages()->latest()->with('sender')->get();

    return view('admin.conversations.show', compact('conversation', 'messages'));
}


    public function reply(Request $request, $id)
    {
        $request->validate([
            'message' => 'nullable|string|max:200',
            'file' => 'nullable|file|max:10240',
        ]);

        if (!$request->filled('message') && !$request->hasFile('file')) {
            return response()->json(['error' => 'يرجى إدخال رسالة أو إرفاق ملف.'], 422);
        }

        $admin = Auth::guard('admin')->user();
        $conversation = Conversation::findOrFail($id);

        $type = $request->hasFile('file') && $request->filled('message') ? 'both' :
                ($request->hasFile('file') ? 'file' : 'text');

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $admin->id,
            'receiver_id' => $request->receiver_id,
            'message' => $request->message,
            'type' => $type,
            'is_read' => false,
        ]);

        if ($request->hasFile('file')) {
            $message->addMediaFromRequest('file')->toMediaCollection('chat-attachments');
        }

        $user = User::find($request->receiver_id);
        if ($user) {
            $user->notify(new NotifyUser([
                'title' => 'رسالة من الإدارة',
                'body' => $request->message ?: 'تم إرسال مرفق',
            ]));
        }

        return response()->json([
            'message' => [
                'id' => $message->id,
                'message' => $message->message,
                'created_at' => $message->created_at,
                'file_url' => optional($message->getFirstMedia('chat-attachments'))->getUrl(),
                'file_type' => optional($message->getFirstMedia('chat-attachments'))->mime_type,
            ]
        ]);
    }
}
