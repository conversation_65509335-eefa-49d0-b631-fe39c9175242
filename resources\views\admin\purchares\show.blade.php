@extends('admin.layout.master')

@section('content')
<section class="mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-md-12">
            <div class="card border-0 shadow rounded">

                {{-- Card Header --}}
                <div class="card-header bg-white border-bottom">
                    <h4 class="mb-0 text-dark">{{ __('admin.purchase_request_details') }}</h4>
                </div>

                {{-- Card Body --}}
                <div class="card-body px-0 py-4" style="background-color: #f9f9f9;">
                    {{-- Purchase Info Table --}}
                    <div class="table-responsive px-4">
                        <table class="table table-bordered mb-0 table-striped">
                            <tbody>
                                <tr>
                                    <th>{{ __('admin.advertisement') }}</th>
                                    <td>{{ optional($purchase->advertisement)->name ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.buyer') }}</th>
                                    <td>{{ optional($purchase->buyer)->name ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.seller') }}</th>
                                    <td>{{ optional($purchase->seller)->name ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.status') }}</th>
                                    <td>
                                        <span class="badge 
                                            @switch($purchase->status)
                                                @case('under_review') badge-warning @break
                                                @case('waiting_buyer_confirmation') badge-info @break
                                                @case('completed') badge-success @break
                                                @case('problem') badge-danger @break
                                                @case('cancelled') badge-secondary @break
                                                @default badge-light
                                            @endswitch">
                                            {{ __('admin.' . $purchase->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.amount_paid') }}</th>
                                    <td>{{ number_format($purchase->amount_paid, 2) }} {{ __('admin.currency') }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.wallet_credit_used') }}</th>
                                    <td>{{ number_format($purchase->wallet_credit_used, 2) }} {{ __('admin.currency') }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.payment_method') }}</th>
                                    <td>{{ $purchase->payment_method ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.payment_status') }}</th>
                                    <td>
                                        <span class="badge {{ $purchase->payment_status ? 'badge-success' : 'badge-danger' }}">
                                            {{ $purchase->payment_status ? __('admin.paid') : __('admin.unpaid') }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ __('admin.created_at') }}</th>
                                    <td>{{ $purchase->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    {{-- Ratings Section --}}
                    <section class="mt-5 px-4">
                        <h5 class="text-dark font-weight-bold mb-3">{{ __('admin.ratings') }}</h5>

                        @php
                            $ratings = $purchase->ratings ?? collect();
                            $average = $ratings->avg('stars');
                            $count = $ratings->count();
                        @endphp

                        {{-- Average Rating --}}
                        @if($count > 0)
                            <p>
                                <strong>{{ __('admin.average_rating') }}:</strong>
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= round($average)) ⭐ @else ☆ @endif
                                @endfor
                                ({{ number_format($average, 1) }} / 5)
                            </p>
                            <p>
                                <strong>{{ __('admin.rating') }}:</strong> {{ $count }} {{ __('admin.ratings') }}
                            </p>
                        @else
                            <p class="text-muted">{{ __('admin.no_ratings_yet') }}</p>
                        @endif

                        {{-- Ratings List --}}
                        <div class="mt-4">
                            @forelse($ratings as $rating)
                                <div class="border p-3 rounded mb-3 bg-white shadow-sm">
                                    <div class="d-flex justify-content-between">
                                        <strong>{{ $rating->rater?->name ?? __('admin.anonymous') }}</strong>
                                        <small class="text-muted">{{ $rating->created_at->format('Y-m-d H:i') }}</small>
                                    </div>

                                    <div class="my-1">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= $rating->stars) ⭐ @else ☆ @endif
                                        @endfor
                                    </div>

                                    <p class="mb-2">{{ $rating->comment }}</p>

                                    {{-- حالة التقييم وزر التغيير --}}
                                    <div class="mt-2 d-flex align-items-center justify-content-between">
                                        <span>
                                            <strong>{{ __('admin.status') }}:</strong>
                                            <span class="badge 
                                                @if($rating->status == 'pending') badge-warning 
                                                @elseif($rating->status == 'approved') badge-success 
                                                @else badge-danger 
                                                @endif">
                                                {{ __('admin.' . $rating->status) }}
                                            </span>
                                        </span>

                                        @if($rating->status == 'pending')
                                            <div>
                                                {{-- زر الموافقة --}}
                                                <form action="{{ route('admin.ratings.updateStatus', $rating->id) }}" method="POST" class="d-inline-block">
                                                    @csrf
                                                    @method('PUT')
                                                    <input type="hidden" name="status" value="approved">
                                                   <button class="btn btn-sm btn-success swal-confirm"
        data-id="{{ $rating->id }}"
        data-status="approved"
        data-message="هل أنت متأكد من قبول هذا التقييم؟">
    ✔ {{ __('admin.approve') }}
</button>
                                                </form>

                                                {{-- زر الرفض --}}
                                                <form action="{{ route('admin.ratings.updateStatus', $rating->id) }}" method="POST" class="d-inline-block ml-1">
                                                    @csrf
                                                    @method('PUT')
                                                    <input type="hidden" name="status" value="rejected">
                                                   <button class="btn btn-sm btn-danger swal-confirm ml-1"
        data-id="{{ $rating->id }}"
        data-status="rejected"
        data-message="هل أنت متأكد من رفض هذا التقييم؟">
    ✖ {{ __('admin.reject') }}
</button>
                                                </form>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @empty
                                {{-- <p class="text-muted">{{ __('admin.no_ratings_yet') }}</p> --}}
                            @endforelse
                        </div>
                    </section>

                    {{-- Back Button --}}
                    <div class="text-center mt-4">
                        <a href="{{ route('admin.purchares.index') }}" class="btn btn-dark btn-lg px-5">
                            <i class="feather icon-arrow-left"></i> {{ __('admin.back') }}
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('js')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.swal-confirm').forEach(function (button) {
            button.addEventListener('click', function (e) {
                e.preventDefault();
                let ratingId = this.dataset.id;
                let status = this.dataset.status;
                let message = this.dataset.message;

                Swal.fire({
                    title: 'تأكيد العملية',
                    text: message,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، تأكيد',
                    cancelButtonText: 'إلغاء',
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#d33',
                }).then((result) => {
                    if (result.isConfirmed) {
                        let form = document.createElement('form');
                        form.method = 'POST';
                        form.action = `/admin/ratings/${ratingId}/status`;

                        // CSRF
                        let csrf = document.createElement('input');
                        csrf.type = 'hidden';
                        csrf.name = '_token';
                        csrf.value = '{{ csrf_token() }}';
                        form.appendChild(csrf);

                        // Method spoof
                        let method = document.createElement('input');
                        method.type = 'hidden';
                        method.name = '_method';
                        method.value = 'PUT';
                        form.appendChild(method);

                        // Status field
                        let input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'status';
                        input.value = status;
                        form.appendChild(input);

                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            });
        });
    });
</script>
@endsection


