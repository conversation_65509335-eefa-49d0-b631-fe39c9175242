@extends('admin.layout.master')

{{-- Extra CSS --}}
@section('css')
<link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css') }}">
<link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">
@endsection

{{-- Content --}}
@section('content')
<form method="POST" action="{{ route('admin.trainers.store') }}" class="store form-horizontal" novalidate enctype="multipart/form-data">
    @csrf
    <input type="hidden" name="type" value="client">

    <section id="multiple-column-form">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-content">
                        <div class="card-body">
                            <div class="form-body">
                                <div class="row">

                                    {{-- Language Tabs --}}
                                    <div class="col-12">
                                        <ul class="nav nav-tabs mb-3">
                                            @foreach (languages() as $lang)
                                            <li class="nav-item">
                                                <a class="nav-link @if($loop->first) active @endif" data-toggle="pill" href="#first_{{ $lang }}" aria-expanded="true">
                                                    {{ __('admin.data') }} {{ $lang }}
                                                </a>
                                            </li>
                                            @endforeach
                                        </ul>
                                    </div>



                                    {{-- Language Tabs Content --}}
                                    {{-- Language Tabs Content --}}
                                    <div class="tab-content col-12">
                                        @foreach (languages() as $lang)
                                        <div role="tabpanel" class="tab-pane fade @if($loop->first) show active @endif" id="first_{{ $lang }}">
                                            {{-- Name --}}
                                            <div class="form-group">
                                                <label>{{ __('admin.name') }} {{ strtoupper($lang) }}</label>
                                                <input type="text" name="name[{{ $lang }}]" class="form-control" placeholder="{{ __('admin.write') . ' ' . __('admin.name') }} {{ $lang }}" @if($loop->first) required data-validation-required-message="{{ __('admin.this_field_is_required') }}" @endif>
                                            </div>

                                            {{-- Bio --}}
                                            <div class="form-group">
                                                <label>{{ __('admin.bio') }} {{ strtoupper($lang) }}</label>
                                                <textarea name="bio[{{ $lang }}]" class="form-control" placeholder="{{ __('admin.write') . ' ' . __('admin.bio') }} {{ $lang }}" @if($loop->first) required data-validation-required-message="{{ __('admin.this_field_is_required') }}" @endif></textarea>
                                            </div>

                                            {{-- Experience --}}
                                            <div class="form-group">
                                                <label>{{ __('admin.experience') }} {{ strtoupper($lang) }}</label>
                                                <textarea name="experience[{{ $lang }}]" class="form-control" placeholder="{{ __('admin.write') . ' ' . __('admin.experience') }} {{ $lang }}" @if($loop->first) required data-validation-required-message="{{ __('admin.this_field_is_required') }}" @endif></textarea>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>



                                    {{-- Main Image --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.personal_image') }}</label>
                                            <input type="file" name="personal_image" class="form-control" accept="image/*">
                                        </div>

                                    </div>

                                    <div class="col-md-6 col-12">

                                        <div class="form-group">
                                            <label>{{ __('admin.certificate_image') }}</label>
                                            <input type="file" name="certificate_image" class="form-control" accept="image/*">
                                        </div>

                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.works') }}</label>
                                            <input type="file" name="works[]" class="form-control" multiple accept="image/*">
                                        </div>

                                    </div>







                                    {{-- Additional Images --}}
                                    {{-- <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.additional_images') }}</label>
                                    <div class="custom-file">
                                        <input type="file" name="images[]" class="custom-file-input" id="images" accept="image/*" multiple>
                                        <label class="custom-file-label" for="images">{{ __('admin.choose_images') }}</label>
                                    </div>
                                    <div class="row mt-2" id="additionalImagesPreview"></div>
                                </div>
                            </div> --}}

                            {{-- User --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.user') }}</label>
                                    <select name="user_id" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                        <option value="">{{ __('admin.choose_user') }}</option>
                                        @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            {{-- Price --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.training_price') }}</label>
                                    <input type="number" step="0.01" name="training_price" class="form-control" placeholder="0.00" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                </div>
                            </div>




                            {{-- WhatsApp Contact --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.contact_whatsapp') }}</label>
                                    <input type="text" name="contact_whatsapp" class="form-control" placeholder="{{ __('admin.enter_whatsapp_number') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                </div>
                            </div>

                            {{-- Contact Phone --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.contact_phone') }}</label>
                                    <input type="text" name="contact_phone" class="form-control" placeholder="{{ __('admin.enter_phone_number') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                </div>
                            </div>

                            {{-- Contact Email --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.contact_email') }}</label>
                                    <input type="email" name="contact_email" class="form-control" placeholder="{{ __('admin.enter_email') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                </div>
                            </div>


                            {{-- City --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.city') }}</label>
                                    <select name="city_id" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                        <option value="">{{ __('admin.select_city') }}</option>
                                        @foreach($cities as $city)
                                        <option value="{{ $city->id }}">{{ $city->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            {{-- Region --}}
                            <div class="col-md-6 col-12">
                                <div class="form-group">
                                    <label>{{ __('admin.region') }}</label>
                                    <select name="region_id" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                        <option value="">{{ __('admin.select_region') }}</option>
                                        @foreach($regions as $region)
                                        <option value="{{ $region->id }}">{{ $region->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>



                            {{-- Buttons --}}
                            <div class="col-12 d-flex justify-content-center mt-3">
                                <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{ __('admin.add') }}</button>
                                <a href="{{ url()->previous() }}" class="btn btn-outline-warning mr-1 mb-1">{{ __('admin.back') }}</a>
                            </div>

                        </div> {{-- End row --}}
                    </div> {{-- End form-body --}}
                </div> {{-- End card-body --}}
            </div>
        </div>
        </div>
        </div>
    </section>
</form>
@endsection

{{-- JS Scripts --}}
@section('js')
<script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
<script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
<script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
<script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>

{{-- Image Preview Scripts --}}
@include('admin.shared.addImage')

{{-- Submit AJAX Form --}}
@include('admin.shared.submitAddForm')

{{-- Region Cities Dynamic Dropdown --}}
@include('admin.shared.regionCityDropdown')
@endsection
