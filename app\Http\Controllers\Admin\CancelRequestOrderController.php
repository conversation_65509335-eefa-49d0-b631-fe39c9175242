<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\CancelReason;
use App\Models\SiteSetting;
use App\Services\WalletService;
use App\Traits\Report;
use App\Services\TransactionService;
use App\Enums\OrderStatus as OrderStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CancelRequestOrderController extends Controller
{
    protected $transactionService;
    protected $walletService;

    public function __construct(TransactionService $transactionService , WalletService $walletService)
    {
        $this->transactionService = $transactionService;
        $this->walletService = $walletService;
    }

    /**
     * Display a listing of cancel request orders.
     */
    public function index($id = null)
    {
        if (request()->ajax()) {
            $orders = Order::with(['user', 'provider', 'address', 'cancelReason', 'paymentMethod'])
                ->where('current_status', 'request_cancel')
                ->search(request()->searchArray)
                ->paginate(30);
            $html = view('admin.cancel_request_orders.table', compact('orders'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.cancel_request_orders.index');
    }

    /**
     * Display the specified cancel request order.
     */
    public function show($id)
    {
        $order = Order::with([
            'user',
            'items.item',
            'coupon',
            'cancelReason',
            'bankTransfer',
            'providerSubOrders' => function($query) {
                $query->with([
                    'provider.user',
                    'statusChanges' => function($statusQuery) {
                        $statusQuery->with('statusable')->orderBy('created_at', 'desc');
                    },
                    'orderItems' => function($itemsQuery) {
                        $itemsQuery->with(['item', 'service', 'product']);
                    }
                ]);
            }
        ])->where('current_status', 'request_cancel')->findOrFail($id);

        // Get payment method details
        $paymentMethod = \App\Models\PaymentMethod::find($order->payment_method_id);

        // Get cancellation fee settings
        $cancellationFeeAmount = SiteSetting::where('key', 'cancellation_fee_amount')->value('value') ?? 5.00;
        $cancellationFeePercentage = SiteSetting::where('key', 'cancellation_fee_percentage')->value('value') ?? 0;

        return view('admin.cancel_request_orders.show', compact('order', 'paymentMethod', 'cancellationFeeAmount', 'cancellationFeePercentage'));
    }

    /**
     * Accept cancel request
     */
    public function acceptCancelRequest(Request $request, $id)
    {
        $request->validate([
            'cancel_fees' => 'required|numeric|min:0'
        ]);

        try {
            return DB::transaction(function () use ($request, $id) {
                $order = Order::with(['user', 'providerSubOrders'])->findOrFail($id);

                if ($order->current_status !== 'request_cancel') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Order is not in request cancel status'
                    ], 400);
                }

                $cancelFees = (float) $request->cancel_fees;
                $admin = auth()->guard('admin')->user();

                // Calculate refund amount (total - cancel fees)
                $refundAmount = max(0, $order->total - $cancelFees);

                // Update main order status
                $order->update([
                    'cancel_fees' => $cancelFees,
                    'current_status' => OrderStatusEnum::CANCELLED->value,
                    'cancellation_reason' => "Cancel request accepted. Fees: {$cancelFees}",
                ]);

                // Cancel all provider sub-orders and create status records
                foreach ($order->providerSubOrders as $subOrder) {
                    $subOrder->update([
                        'status' => OrderStatusEnum::CANCELLED->value,
                        'cancellation_reason' => "Cancel request accepted by admin",
                    ]);

                    // Create status record for sub-order
                    OrderStatus::create([
                        'provider_sub_order_id' => $subOrder->id,
                        'status' => OrderStatusEnum::CANCELLED->value,
                        'statusable_type' => 'App\Models\Admin',
                        'statusable_id' => $admin->id,
                        'map_desc' => "Cancel request accepted. Refund: {$refundAmount}, Fees: {$cancelFees}",
                    ]);
                }

                // Create main order status record
                OrderStatus::create([
                    'order_id' => $order->id,
                    'status' => OrderStatusEnum::CANCELLED->value,
                    'statusable_type' => 'App\Models\Admin',
                    'statusable_id' => $admin->id,
                    'map_desc' => "Cancel request accepted by admin. Refund: {$refundAmount}, Fees: {$cancelFees}",
                ]);

                // Process refund if amount > 0
                if ($refundAmount > 0) {
                    $this->processRefund($order, $refundAmount);
                }

                // Restore product quantities
                $this->restoreProductQuantities($order);
                            $this->walletService->createTransaction($order->user_id, $refundAmount, 'refund', 'success');

                // Log the action
                Report::addToLog("قبول طلب إلغاء الطلب رقم {$order->order_number}. المبلغ المسترد: {$refundAmount}, رسوم الإلغاء: {$cancelFees}");

                // Send notification to the order user
                $order->user->notify(new \App\Notifications\NotifyUser([
                    'title' => [
                        'ar' => 'قبول طلب الإلغاء',
                        'en' => 'Cancel Request Accepted'
                    ],
                    'body' => [
                        'ar' => 'تم قبول طلب الإلغاء للطلب رقم ' . $order->order_number . '. سيتم إعادة المبلغ المسترد إلى محفظتك.',
                        'en' => 'Your cancel request for order #' . $order->order_number . ' has been accepted. The refund will be returned to your wallet.'
                    ],
                    'type' => 'order_cancel_accepted',
                    'order_id' => $order->id
                ]));

                return response()->json([
                    'success' => true,
                    'message' => 'Cancel request accepted successfully',
                    'data' => [
                        'refund_amount' => $refundAmount,
                        'cancel_fees' => $cancelFees
                    ]
                ]);
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to accept cancel request: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject cancel request
     */
    public function rejectCancelRequest(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            return DB::transaction(function () use ($request, $id) {
                $order = Order::findOrFail($id);

                if ($order->current_status !== 'request_cancel') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Order is not in request cancel status'
                    ], 400);
                }

                $reason = $request->reason;
                $admin = auth()->guard('admin')->user();

                // Update order status back to previous status (usually processing)
                $order->update([
                    'current_status' => OrderStatusEnum::PROCESSING->value,
                ]);

                // Create main order status record
                OrderStatus::create([
                    'order_id' => $order->id,
                    'status' => OrderStatusEnum::PROCESSING->value,
                    'statusable_type' => 'App\Models\Admin',
                    'statusable_id' => $admin->id,
                    'map_desc' => "Cancel request rejected by admin: {$reason}",
                ]);

                // Log the action
                Report::addToLog("رفض طلب إلغاء الطلب رقم {$order->order_number}: {$reason}");

                // Send notification to the order user
                $order->user->notify(new \App\Notifications\NotifyUser([
                    'title' => [
                        'ar' => 'رفض طلب الإلغاء',
                        'en' => 'Cancel Request Rejected'
                    ],
                    'body' => [
                        'ar' => 'تم رفض طلب الإلغاء للطلب رقم ' . $order->order_number . '. السبب: ' . $reason,
                        'en' => 'Your cancel request for order #' . $order->order_number . ' was rejected. Reason: ' . $reason
                    ],
                    'type' => 'order_cancel_rejected',
                    'order_id' => $order->id
                ]));

                return response()->json([
                    'success' => true,
                    'message' => 'Cancel request rejected successfully'
                ]);
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject cancel request: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process refund based on payment method
     */
    private function processRefund(Order $order, float $refundAmount)
    {
        switch ($order->payment_method_id) {
            case 1: // Wallet
                if ($order->payment_status === 'paid') {
                    $this->transactionService->addToWallet($order->user, $refundAmount, 'OrderCancellationRefund');
                }
                break;

            case 5: // Bank Transfer
                // For bank transfers, admin needs to manually process refund
                Log::info('Bank transfer refund required', [
                    'order_id' => $order->id,
                    'amount' => $refundAmount,
                    'user_id' => $order->user_id
                ]);
                break;

            default: // Electronic payments
                // For electronic payments, initiate refund through payment gateway
                Log::info('Electronic payment refund initiated', [
                    'order_id' => $order->id,
                    'amount' => $refundAmount,
                    'payment_reference' => $order->payment_reference
                ]);
                break;
        }
    }

    /**
     * Restore product quantities when order is cancelled
     */
    private function restoreProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->item_type === 'App\Models\Product') {
                $product = \App\Models\Product::find($orderItem->item_id);
                if ($product) {
                    $product->increment('quantity', $orderItem->quantity);
                }
            }
        }
    }
}
