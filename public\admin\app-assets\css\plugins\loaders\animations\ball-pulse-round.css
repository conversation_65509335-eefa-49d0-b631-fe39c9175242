/*========================================================
        DARK LAYOUT
=========================================================*/
@-webkit-keyframes ball-pulse-round {
  0%, 80%, 100% {
            transform : scale(0);
    -webkit-transform : scale(0);
  }
  40% {
            transform : scale(1);
    -webkit-transform : scale(1);
  }
}
@keyframes ball-pulse-round {
  0%, 80%, 100% {
            transform : scale(0);
    -webkit-transform : scale(0);
  }
  40% {
            transform : scale(1);
    -webkit-transform : scale(1);
  }
}

.ball-pulse-round > div {
  -webkit-animation-fill-mode : both;
          animation-fill-mode : both;
  width : 10px;
  height : 10px;
  -webkit-animation : ball-pulse-round 1.2s infinite ease-in-out;
          animation : ball-pulse-round 1.2s infinite ease-in-out;
}