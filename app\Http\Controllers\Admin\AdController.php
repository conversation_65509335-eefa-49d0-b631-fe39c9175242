<?php
namespace App\Http\Controllers\Admin;

use App\Exports\CategoryExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ads\Store;
use App\Http\Requests\Admin\ads\Update;
use App\Models\Advertisement;
use App\Models\Category;
use Illuminate\Http\UploadedFile;
use App\Models\City;
use App\Models\Region;
use App\Models\User;
use App\Traits\Report;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AdController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {

            $ads = Advertisement::search(request()->searchArray)->paginate(30);

            $html = view('admin.ads.table', compact('ads'))->render();
            return response()->json(['html' => $html]);
        }

        $ads = Advertisement::latest()->get();
        $regions = Region::all();
        return view('admin.ads.index', compact('ads','regions', 'id'));
    }

    public function export()
    {
        return Excel::download(new CategoryExport, 'users.xlsx');
    }

    public function create($id = null)
    {

        $Categories = Category::all();
        $cities = City::all();
        $regions = Region::all();
        $users= User::all();

        return view('admin.ads.create',compact('Categories','cities','regions','users'));
    }

public function store(Store $request)
{
    $validated = $request->validated();

    $ad = Advertisement::create($validated);

    if ($request->hasFile('main_image')) {
        $ad->addMediaFromRequest('main_image')->toMediaCollection('main_image');
    }

    if ($request->hasFile('images')) {
        foreach ($request->file('images') as $image) {
            if ($image instanceof \Illuminate\Http\UploadedFile) {
                $ad->addMedia($image)->toMediaCollection('ads');
            }
        }
    }

    Report::addToLog('إضافة إعلان');

    return response()->json(['url' => route('admin.ads.index')]);
}

public function edit($id)
{
    $ad = Advertisement::findOrFail($id);
    $users = User::all();
    $cities = City::all();
    $regions = Region::all();
    $Categories = Category::all();

    return view('admin.ads.edit', compact('ad', 'users', 'cities', 'regions', 'Categories'));
}

public function update(Request $request, $id)
{
    $ad = Advertisement::findOrFail($id);
    $ad->update($request->except('existing_images', 'images', 'main_image'));

    if ($request->hasFile('main_image')) {
        $ad->clearMediaCollection('main_image');
        $ad->addMediaFromRequest('main_image')->toMediaCollection('main_image');
    }

    $existingIds = $request->input('existing_images', []);
    $ad->getMedia('ads')->each(function ($media) use ($existingIds) {
        if (!in_array($media->id, $existingIds)) {
            $media->delete();
        }
    });

    if ($request->hasFile('images')) {
        foreach ($request->file('images') as $image) {
            $ad->addMedia($image)->toMediaCollection('ads');
        }
    }

    return redirect()->route('admin.ads.index')->with('success', 'تم التعديل بنجاح');
}


    public function show($id)
    {
        $ad = Advertisement::with(['user', 'city', 'region', 'mainCategory', 'subCategory'])->findOrFail($id);
        return view('admin.ads.show', compact('ad'));
    }

    // public function show($id)
    // {
    //     $category   = Category::findOrFail($id);
    //     $ads = Advertisement::all();
    //     return view('admin.ads.show', ['category' => $category, 'ads' => $ads]);
    // }

    public function destroy($id)
    {
        $ad = Advertisement::findOrFail($id)->delete();
        Report::addToLog('  حذف قسم');
        return response()->json(['id' => $id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (Advertisement::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من الاقسام');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }
}
