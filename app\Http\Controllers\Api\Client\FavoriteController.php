<?php
namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\FavoriteAdResource;
use App\Services\FavoriteService;
use Illuminate\Http\Request;

class FavoriteController extends Controller
{
    public function __construct(protected FavoriteService $favoriteService) {}

  public function toggle(Request $request)
{
    $advertisementId = $request->input('advertisement_id'); // الإعلان اللي هيتعمله تoggle
    $user = $request->user(); // المستخدم اللي معاه التوكن

    $result = $this->favoriteService->toggleFavorite(
        $user, // نمرر الـ user مباشرة
        $advertisementId
    );

    // return response()->json([
    //     'success' => true,
    //     'message' => $result['message'],
    //     'data' => isset($result['data']) ? new FavoriteAdResource($result['data']) : null
    // ]);

    return response()->json([
        'success' => true,
        'message' => $result['message'],
        'data' => isset($result['data']) ? new FavoriteAdResource($result['data']) : null
    ]);
}


    public function index(Request $request)
    {
        $favorites = $this->favoriteService->getUserFavorites($request->user()->id);
        return Responder::success(
            $favorites ? FavoriteAdResource::collection($favorites) : null
        );
    }
}
