<?php

namespace App\Repositories;

use App\Models\Advertisement;
use Illuminate\Database\Eloquent\Collection;

class AdRepository
{
    protected Advertisement $model;

    public function __construct(Advertisement $model)
    {
        $this->model = $model;
    }

    public function getAdsByUser(int $userId): Collection
    {
        return $this->model
            ->with(['user', 'mainCategory', 'subCategory', 'media', 'city', 'region']) 
            ->where('user_id', $userId)
            ->latest()
            ->get();
    }

    public function findById($id): ?Advertisement
    {
        return $this->model
            ->with(['user', 'mainCategory', 'subCategory', 'media', 'city', 'region'])
            ->find($id);
    }

    public function create(array $data): Advertisement
    {
        return $this->model->create($data);
    }

    public function update(Advertisement $ad, array $data): Advertisement
    {
        $ad->update($data);
        return $ad;
    }

    public function delete(Advertisement $ad)
    {
        return $ad->delete();
    }

public function getUserAds($userId, $filters = [])
{
    return Advertisement::query()
        ->where('user_id', $userId)

        ->when(isset($filters['status']), function ($q) use ($filters) {
            if (is_array($filters['status'])) {
                $q->whereIn('status', $filters['status']);
            } else {
                $q->where('status', $filters['status']);
            }
        })


         ->when(isset($filters['name']), function ($q) use ($filters) {
            $q->where('name', 'like', '%' . $filters['name'] . '%');
            // لو `name` translatable:
            // $q->where('name->' . app()->getLocale(), 'like', '%' . $filters['name'] . '%');
        })


        ->when(isset($filters['main_category_id']), fn($q) =>
            $q->where('main_category_id', $filters['main_category_id']))
        ->when(isset($filters['sub_category_id']), fn($q) =>
            $q->where('sub_category_id', $filters['sub_category_id']))
        ->when(isset($filters['min_price']), fn($q) =>
            $q->where('price', '>=', $filters['min_price']))
        ->when(isset($filters['max_price']), fn($q) =>
            $q->where('price', '<=', $filters['max_price']))
        ->when(isset($filters['gender_target']), fn($q) =>
            $q->where('gender_target', $filters['gender_target']))
        ->when(isset($filters['city_id']), fn($q) =>
            $q->where('city_id', $filters['city_id']))
        ->when(isset($filters['sort_by']), function ($q) use ($filters) {
            switch ($filters['sort_by']) {
                case 'oldest':
                    $q->orderBy('created_at', 'asc');
                    break;
                case 'lowest_price':
                    $q->orderBy('price', 'asc');
                    break;
                case 'highest_price':
                    $q->orderBy('price', 'desc');
                    break;
                default:
                    $q->orderBy('created_at', 'desc');
            }
        }, fn($q) => $q->orderBy('created_at', 'desc'))

        ->withCount('ratings')
        ->withAvg('ratings', 'stars')
        ->get();
}






}
