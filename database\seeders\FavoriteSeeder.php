<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FavoriteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // User favorites for services and providers
        
        DB::table('favorites')->insert([
            [
                'user_id' => 1,
                'favoritable_type' => 'App\\Models\\Service',
                'favoritable_id' => 1,
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ],
            [
                'user_id' => 1,
                'favoritable_type' => 'App\\Models\\Service',
                'favoritable_id' => 3,
                'created_at' => now()->subDays(8),
                'updated_at' => now()->subDays(8),
            ],
            [
                'user_id' => 1,
                'favoritable_type' => 'App\\Models\\Provider',
                'favoritable_id' => 1,
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(7),
            ],
            [
                'user_id' => 2,
                'favoritable_type' => 'App\\Models\\Service',
                'favoritable_id' => 2,
                'created_at' => now()->subDays(6),
                'updated_at' => now()->subDays(6),
            ],
            [
                'user_id' => 2,
                'favoritable_type' => 'App\\Models\\Provider',
                'favoritable_id' => 2,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'user_id' => 3,
                'favoritable_type' => 'App\\Models\\Service',
                'favoritable_id' => 1,
                'created_at' => now()->subDays(4),
                'updated_at' => now()->subDays(4),
            ],
            [
                'user_id' => 3,
                'favoritable_type' => 'App\\Models\\Service',
                'favoritable_id' => 4,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 3,
                'favoritable_type' => 'App\\Models\\Provider',
                'favoritable_id' => 3,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'user_id' => 1,
                'favoritable_type' => 'App\\Models\\Service',
                'favoritable_id' => 5,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => 2,
                'favoritable_type' => 'App\\Models\\Provider',
                'favoritable_id' => 1,
                'created_at' => now()->subHours(12),
                'updated_at' => now()->subHours(12),
            ],
        ]);

        $this->command->info('Favorites seeded successfully!');
    }
}
