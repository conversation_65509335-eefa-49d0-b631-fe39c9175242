<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ComplaintReplaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Complaint replies from admin/support team
        
        DB::table('complaint_replays')->insert([
            [
                'complaint_id' => 1,
                'admin_id' => 1,
                'reply' => 'Thank you for bringing this to our attention. We have investigated the issue and will take appropriate action. We apologize for any inconvenience caused.',
                'is_public' => true,
                'created_at' => now()->subDays(8),
                'updated_at' => now()->subDays(8),
            ],
            [
                'complaint_id' => 1,
                'admin_id' => 1,
                'reply' => 'We have contacted the provider and they will reach out to you directly to resolve this matter.',
                'is_public' => false,
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(7),
            ],
            [
                'complaint_id' => 2,
                'admin_id' => 1,
                'reply' => 'We understand your frustration regarding the delayed appointment. We have implemented new scheduling measures to prevent this from happening again.',
                'is_public' => true,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'complaint_id' => 2,
                'admin_id' => 1,
                'reply' => 'As compensation for the inconvenience, we have added a 20% discount coupon to your account.',
                'is_public' => false,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'complaint_id' => 3,
                'admin_id' => 1,
                'reply' => 'We take service quality very seriously. We have reviewed the provider\'s performance and provided additional training.',
                'is_public' => true,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'complaint_id' => 4,
                'admin_id' => 1,
                'reply' => 'Thank you for reporting this payment issue. Our technical team has identified and fixed the bug. Please try the payment process again.',
                'is_public' => true,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'complaint_id' => 4,
                'admin_id' => 1,
                'reply' => 'If you continue to experience payment issues, please contact our support team <NAME_EMAIL>',
                'is_public' => false,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'complaint_id' => 5,
                'admin_id' => 1,
                'reply' => 'We apologize for the damaged product. A replacement has been shipped to your address and should arrive within 2-3 business days.',
                'is_public' => true,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'complaint_id' => 6,
                'admin_id' => 1,
                'reply' => 'We are currently investigating this matter and will provide an update within 24 hours.',
                'is_public' => true,
                'created_at' => now()->subHours(12),
                'updated_at' => now()->subHours(12),
            ],
            [
                'complaint_id' => 7,
                'admin_id' => 1,
                'reply' => 'Thank you for your feedback. We are working on improving our app performance and will release an update soon.',
                'is_public' => true,
                'created_at' => now()->subHours(6),
                'updated_at' => now()->subHours(6),
            ],
        ]);

        $this->command->info('Complaint replies seeded successfully!');
    }
}
