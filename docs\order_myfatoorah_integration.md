# Order MyFatoorah Integration

## Overview
Complete MyFatoorah payment gateway integration for the order system, following the same robust pattern implemented for course enrollments.

## What Was Implemented

### 1. **OrderPaymentService**
- ✅ **Dedicated Service**: `app/Services/Myfatoorah/OrderPaymentService.php`
- ✅ **Invoice Creation**: Creates detailed invoices with order items breakdown
- ✅ **Payment Verification**: Verifies payment status with MyFatoorah API
- ✅ **Webhook Processing**: Handles payment status changes automatically
- ✅ **Gateway Management**: Lists available payment gateways

### 2. **Enhanced OrderService**
- ✅ **MyFatoorah Integration**: Updated `processElectronicPayment()` method
- ✅ **Error Handling**: Comprehensive error handling with rollback
- ✅ **Logging**: Detailed logging for debugging and monitoring

### 3. **Order Payment Controller**
- ✅ **Callback Handling**: `app/Http/Controllers/Api/Order/PaymentController.php`
- ✅ **Success/Error Callbacks**: Handles MyFatoorah redirects
- ✅ **Webhook Processing**: Processes payment status changes
- ✅ **Gateway Listing**: Provides available payment gateways

### 4. **Enhanced Order Controller**
- ✅ **Payment Gateways**: Added `paymentGateways()` method
- ✅ **Cart Integration**: Gets gateways based on cart total

### 5. **Updated Request Validation**
- ✅ **Gateway Support**: Added `gateway` parameter to `CreateOrderRequest`
- ✅ **Validation Rules**: Validates gateway selection

### 6. **Configuration & Routes**
- ✅ **Order Payment URLs**: Added to `config/myfatoorah.php`
- ✅ **Client Routes**: Added order payment routes to `routes/client.php`

## API Endpoints

### **Order Management**
```
GET    /api/client/orders                           - List user orders
GET    /api/client/orders/{id}                      - Get order details
POST   /api/client/orders/create                    - Create order from cart
POST   /api/client/orders/cancel                    - Cancel order
GET    /api/client/orders/payment-gateways          - Get available payment gateways
```

### **MyFatoorah Order Callbacks (Public)**
```
GET    /api/client/orders/payment/success           - Payment success callback
GET    /api/client/orders/payment/error             - Payment error callback
POST   /api/client/orders/payment/webhook           - Payment webhook
GET    /api/client/orders/payment/gateways          - Get available gateways
```

## Order Payment Flow

### **1. Cart to Order Creation**
```
User has items in cart → Calls create order API → Order created with pending_payment status
```

### **2. MyFatoorah Payment Processing**
```
Order created → MyFatoorah invoice generated → User redirected to payment gateway
```

### **3. Payment Completion**
```
Payment completed → MyFatoorah redirects to success/error URL → Order status updated
```

### **4. Webhook Processing**
```
MyFatoorah sends webhook → Order status updated automatically → Loyalty points processed
```

## Order Invoice Structure

### **Invoice Items Breakdown**
```json
{
  "InvoiceItems": [
    {
      "ItemName": "Product Name",
      "Quantity": 2,
      "UnitPrice": 150.00
    },
    {
      "ItemName": "Delivery Fee",
      "Quantity": 1,
      "UnitPrice": 25.00
    },
    {
      "ItemName": "Booking Fee",
      "Quantity": 1,
      "UnitPrice": 10.00
    },
    {
      "ItemName": "Home Service Fee",
      "Quantity": 1,
      "UnitPrice": 50.00
    }
  ]
}
```

## Usage Examples

### **1. Create Order with MyFatoorah**
```json
POST /api/client/orders/create
{
    "address_id": 1,
    "payment_method": "credit_card",
    "gateway": "myfatoorah",
    "booking_type": "home",
    "delivery_type": "express"
}

Response:
{
    "status": true,
    "data": {
        "success": true,
        "message": "Redirect to MyFatoorah payment gateway",
        "requires_payment_gateway": true,
        "payment_url": "https://portal.myfatoorah.com/...",
        "invoice_id": "12345"
    }
}
```

### **2. Get Available Payment Gateways**
```json
GET /api/client/orders/payment-gateways

Response:
{
    "status": true,
    "data": {
        "cart_total": "485.00",
        "gateways": [
            {
                "PaymentMethodId": 1,
                "PaymentMethodCode": "myfatoorah",
                "PaymentMethodEn": "MyFatoorah",
                "PaymentMethodAr": "ماي فاتورة"
            },
            {
                "PaymentMethodId": 2,
                "PaymentMethodCode": "knet",
                "PaymentMethodEn": "KNET",
                "PaymentMethodAr": "كي نت"
            }
        ]
    }
}
```

### **3. Payment Success Callback**
```json
GET /api/client/orders/payment/success?paymentId=12345

Response:
{
    "status": true,
    "data": {
        "message": "Order payment completed successfully!",
        "order": {
            "id": 25,
            "order_number": "ORD-20250601-ABC123",
            "status": "processing",
            "payment_status": "paid",
            "total": "485.00"
        }
    }
}
```

## Order Status Updates

### **Successful Payment**
```sql
UPDATE orders SET
    payment_status = 'paid',
    status = 'processing',
    payment_reference = 'MF_12345'
WHERE id = 25;
```

### **Failed Payment**
```sql
UPDATE orders SET
    payment_status = 'failed',
    status = 'cancelled'
WHERE id = 25;
```

## Environment Configuration

Add to `.env`:
```env
# Order Payment URLs
MYFATOORAH_ORDER_SUCCESS_URL=/api/client/orders/payment/success
MYFATOORAH_ORDER_ERROR_URL=/api/client/orders/payment/error
MYFATOORAH_ORDER_WEBHOOK_URL=/api/client/orders/payment/webhook
```

## Key Features

### **1. Comprehensive Invoice Items**
- ✅ **Product Items**: Individual products with quantities and prices
- ✅ **Delivery Fees**: Normal/Express delivery charges
- ✅ **Booking Fees**: Service booking charges
- ✅ **Home Service Fees**: Additional fees for home services

### **2. Robust Error Handling**
- ✅ **Transaction Safety**: All operations wrapped in database transactions
- ✅ **Product Quantity Rollback**: Restores quantities on payment failure
- ✅ **Comprehensive Logging**: Detailed logs for debugging

### **3. Webhook Processing**
- ✅ **Automatic Status Updates**: Order status updated via webhooks
- ✅ **Multiple Event Types**: Handles paid, failed, cancelled events
- ✅ **Signature Validation**: Optional webhook signature verification

### **4. Integration Benefits**
- ✅ **Consistent Pattern**: Same pattern as course enrollment system
- ✅ **Reusable Components**: Shared MyFatoorah configuration and utilities
- ✅ **Scalable Architecture**: Easy to extend for additional payment methods

## Testing

### **1. Test Order Creation**
```bash
# Create order with MyFatoorah
POST /api/client/orders/create
{
    "address_id": 1,
    "payment_method": "mada",
    "gateway": "myfatoorah"
}
```

### **2. Test Payment Gateways**
```bash
# Get available gateways
GET /api/client/orders/payment-gateways
```

### **3. Test Webhook**
```bash
# Simulate webhook
POST /api/client/orders/payment/webhook
{
    "EventType": "InvoiceStatusChanged",
    "Data": {
        "InvoiceId": "12345",
        "CustomerReference": "25"
    }
}
```

## Security Features

- ✅ **Payment Verification**: All payments verified with MyFatoorah API
- ✅ **Order ID Validation**: Ensures order belongs to payment
- ✅ **Webhook Security**: Optional signature validation
- ✅ **Transaction Safety**: Database rollback on failures

## Benefits

1. **✅ Complete Integration**: Full MyFatoorah support for orders
2. **✅ Detailed Invoicing**: Comprehensive invoice item breakdown
3. **✅ Automatic Processing**: Webhook-based status updates
4. **✅ Robust Error Handling**: Comprehensive error recovery
5. **✅ Consistent Architecture**: Follows course enrollment pattern
6. **✅ Production Ready**: Full logging, validation, and security

The order system now has complete MyFatoorah integration with the same robust features as the course enrollment system! 🚀
