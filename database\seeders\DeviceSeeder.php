<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Device<PERSON>eeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // User devices for push notifications
        
        DB::table('devices')->insert([
            [
                'user_id' => 1,
                'device_token' => 'fcm_token_' . \Illuminate\Support\Str::random(40),
                'device_type' => 'android',
                'device_model' => 'Samsung Galaxy S21',
                'app_version' => '1.0.0',
                'os_version' => 'Android 12',
                'is_active' => true,
                'last_used_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'user_id' => 1,
                'device_token' => 'apns_token_' . \Illuminate\Support\Str::random(40),
                'device_type' => 'ios',
                'device_model' => 'iPhone 13 Pro',
                'app_version' => '1.0.0',
                'os_version' => 'iOS 15.5',
                'is_active' => false,
                'last_used_at' => now()->subDays(7),
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(7),
            ],
            [
                'user_id' => 2,
                'device_token' => 'fcm_token_' . \Illuminate\Support\Str::random(40),
                'device_type' => 'android',
                'device_model' => 'Google Pixel 6',
                'app_version' => '1.1.0',
                'os_version' => 'Android 13',
                'is_active' => true,
                'last_used_at' => now()->subHours(2),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subHours(2),
            ],
            [
                'user_id' => 3,
                'device_token' => 'apns_token_' . \Illuminate\Support\Str::random(40),
                'device_type' => 'ios',
                'device_model' => 'iPhone 12',
                'app_version' => '1.0.5',
                'os_version' => 'iOS 15.0',
                'is_active' => true,
                'last_used_at' => now()->subMinutes(30),
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subMinutes(30),
            ],
            [
                'user_id' => 2,
                'device_token' => 'web_token_' . \Illuminate\Support\Str::random(40),
                'device_type' => 'web',
                'device_model' => 'Chrome Browser',
                'app_version' => '1.0.0',
                'os_version' => 'Windows 11',
                'is_active' => true,
                'last_used_at' => now()->subHours(1),
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(1),
            ],
        ]);

        $this->command->info('Devices seeded successfully!');
    }
}
