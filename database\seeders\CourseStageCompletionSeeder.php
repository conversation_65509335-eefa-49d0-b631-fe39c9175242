<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CourseStageCompletionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Course stage completions tracking user progress

        DB::table('course_stage_completions')->insert([
            // User 1, Course 1 (60% progress - 2 stages completed)
            [
                'enrollment_id' => 1,
                'stage_id' => 1,
                'completed_at' => now()->subDays(14),
                'time_spent' => 930, // 15.5 minutes in seconds
                'last_watch_time' => '00:15:30',
                'notes' => 'Completed introduction stage successfully',
                'created_at' => now()->subDays(14),
                'updated_at' => now()->subDays(14),
            ],
            [
                'enrollment_id' => 1,
                'stage_id' => 2,
                'completed_at' => now()->subDays(10),
                'time_spent' => 1200, // 20 minutes in seconds
                'last_watch_time' => '00:20:00',
                'notes' => 'Completed basic cutting techniques',
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ],

            // User 2, Course 1 (100% progress - completed all 3 stages)
            [
                'enrollment_id' => 2,
                'stage_id' => 1,
                'completed_at' => now()->subDays(11),
                'time_spent' => 900, // 15 minutes in seconds
                'last_watch_time' => '00:15:00',
                'notes' => 'Great introduction to hair types',
                'created_at' => now()->subDays(11),
                'updated_at' => now()->subDays(11),
            ],
            [
                'enrollment_id' => 2,
                'stage_id' => 2,
                'completed_at' => now()->subDays(8),
                'time_spent' => 1545, // 25.75 minutes in seconds
                'last_watch_time' => '00:25:45',
                'notes' => 'Mastered cutting techniques',
                'created_at' => now()->subDays(8),
                'updated_at' => now()->subDays(8),
            ],
            [
                'enrollment_id' => 2,
                'stage_id' => 3,
                'completed_at' => now()->subDays(2),
                'time_spent' => 1100, // 18.33 minutes in seconds
                'last_watch_time' => '00:18:20',
                'notes' => 'Learned about all styling tools',
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(2),
            ],

            // User 3, Course 2 (33% progress - 1 stage completed)
            [
                'enrollment_id' => 3,
                'stage_id' => 4,
                'completed_at' => now()->subDays(9),
                'time_spent' => 735, // 12.25 minutes in seconds
                'last_watch_time' => '00:12:15',
                'notes' => 'Nail preparation techniques mastered',
                'created_at' => now()->subDays(9),
                'updated_at' => now()->subDays(9),
            ],

            // User 1, Course 3 (67% progress - 2 stages completed)
            [
                'enrollment_id' => 4,
                'stage_id' => 7,
                'completed_at' => now()->subDays(7),
                'time_spent' => 1200, // 20 minutes in seconds
                'last_watch_time' => '00:20:00',
                'notes' => 'Understanding skin types completed',
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(7),
            ],
            [
                'enrollment_id' => 4,
                'stage_id' => 8,
                'completed_at' => now()->subDays(3),
                'time_spent' => 990, // 16.5 minutes in seconds
                'last_watch_time' => '00:16:30',
                'notes' => 'Cleansing techniques learned',
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],

            // User 5, Course 3 (100% progress - completed all 3 stages)
            [
                'enrollment_id' => 6,
                'stage_id' => 7,
                'completed_at' => now()->subDays(4),
                'time_spent' => 1200, // 20 minutes in seconds
                'last_watch_time' => '00:20:00',
                'notes' => 'Excellent understanding of skin types',
                'created_at' => now()->subDays(4),
                'updated_at' => now()->subDays(4),
            ],
            [
                'enrollment_id' => 6,
                'stage_id' => 8,
                'completed_at' => now()->subDays(2),
                'time_spent' => 990, // 16.5 minutes in seconds
                'last_watch_time' => '00:16:30',
                'notes' => 'Mastered cleansing techniques',
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'enrollment_id' => 6,
                'stage_id' => 9,
                'completed_at' => now()->subHours(12),
                'time_spent' => 1695, // 28.25 minutes in seconds
                'last_watch_time' => '00:28:15',
                'notes' => 'Facial massage techniques perfected',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(12),
            ],

            // User 2, Course 4 (25% progress - 1 stage completed)
            [
                'enrollment_id' => 7,
                'stage_id' => 10,
                'completed_at' => now()->subDays(6),
                'time_spent' => 1800, // 30 minutes in seconds
                'last_watch_time' => '00:30:00',
                'notes' => 'Basic makeup fundamentals learned',
                'created_at' => now()->subDays(6),
                'updated_at' => now()->subDays(6),
            ],
        ]);

        $this->command->info('Course stage completions seeded successfully!');
    }
}
