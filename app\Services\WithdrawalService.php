<?php

namespace App\Services;

use App\Models\WithdrawalRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class WithdrawalService
{
public function create(array $data)
{
    $user = auth()->user();
    $balance = $user->wallet?->balance;

    if ($data['amount'] < 10) {
        throw ValidationException::withMessages([
            'amount' => ['أقل قيمة للسحب هي 10 ريال'],
        ]);
    }

    if ($data['amount'] > $balance) {
        throw ValidationException::withMessages([
            'amount' => ['الرصيد غير كافي لعملية السحب.'],
        ]);
    }



    return WithdrawalRequest::create([
        'user_id'         => $user->id,
        'amount'          => $data['amount'],
        'bank_name'       => $data['bank_name'],
        'account_holder'  => $data['account_holder'],
        'account_number'  => $data['account_number'],
        'iban'            => $data['iban'],
        'status'          => 'pending',
    ]);
}


    public function getAllForUser()
    {
        return WithdrawalRequest::where('user_id', auth()->id())
            ->orderByDesc('created_at')
            ->get();
    }

public function cancel($id)
{
    $withdrawal = WithdrawalRequest::where('user_id', auth()->id())
        ->where('status', 'pending')
        ->findOrFail($id);

    $user = $withdrawal->user;

    // التحقق من وجود المحفظة
    $wallet = $user->wallet;
    if (!$wallet) {
        $wallet = $user->wallet()->create([
            'balance' => 0,
            'frozen_balance' => 0,
        ]);
    }

    // تعديل الرصيد
    $wallet->balance += $withdrawal->amount;
    $wallet->frozen_balance -= $withdrawal->amount;
    $wallet->save();

    // حذف طلب السحب
    $withdrawal->delete();

    return true;
}

}
