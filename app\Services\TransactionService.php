<?php

namespace App\Services;

use App\Enum\OrderPaymentMethodEnum;
use App\Models\Driver;
use App\Models\User;
use App\Repositories\SettingRepository;
use App\Repositories\TransactionRepository;
use Illuminate\Support\Facades\Log;

class TransactionService
{
    public function __construct(protected TransactionRepository $transactionRepository) {}

    public function list($type, $request)
    {
        return $this->transactionRepository->list($type, $request);
    }

    public function getBalance()
    {
        return $this->transactionRepository->getBalance();
    }

    public function walletReport($type)
    {
        return $this->transactionRepository->walletReport($type);
    }

    public function withdrawOrderTransaction($order, $userId, $total)
    {
        $data = [
            'notes' => [
                'en' => "Withdraw Commission Value For Order Number $order->id",
                'ar' => "سحب قيمه عموله الحجز رقم $order->id",
            ],
            'order_id' => $order->id,
            'user_id' => $userId,
            'amount' => $total * -1,
        ];
        $this->transactionRepository->create($data);
    }

    public function depositOrderTransaction($order, $userId, $total)
    {
        $data = [
            'notes' => [
                'en' => "Deposit Order Value Number $order->id",
                'ar' => "إيداع قيمه الحجز رقم $order->id",
            ],
            'order_id' => $order->id,
            'user_id' => $userId,
            'amount' => $total,
        ];
        $this->transactionRepository->create($data);
    }

   
}



