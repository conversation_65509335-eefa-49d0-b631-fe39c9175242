<?php

namespace App\Http\Controllers\Admin;

use App\Models\Blog;
use App\Traits\Report;
use App\Models\BlogComment;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\blogs\Store;
use App\Http\Requests\Admin\blogs\Update;


class BlogController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {
            $blogs = Blog::with('category')->search(request()->searchArray)->paginate(30);
            $html = view('admin.blogs.table', compact('blogs'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.blogs.index');
    }

    public function create()
    {
        $categories = BlogCategory::where('is_active', 1)->get();
        return view('admin.blogs.create', compact('categories'));
    }

    public function store(Store $request)
    {
        $data = $request->validated();
        $data['is_active'] = (int) $request->input('is_active', 0);

        $blog = Blog::create($data);

        // Handle image upload using Spatie Media Library
        if ($request->hasFile('image')) {
            $blog->addMediaFromRequest('image')
                ->usingName('blog_image')
                ->usingFileName(time() . '_blog_' . $blog->id . '.' . $request->file('image')->getClientOriginalExtension())
                ->toMediaCollection('blogs');
        }

        Report::addToLog('اضافه المدونه');
        return response()->json(['url' => route('admin.blogs.index')]);
    }

    public function edit($id)
    {
        $blog = Blog::findOrFail($id);
        $categories = BlogCategory::where('is_active', 1)->get();
        return view('admin.blogs.edit', compact('blog', 'categories'));
    }

    public function update(Update $request, $id)
    {
        $blog = Blog::findOrFail($id);
        $data = $request->validated();
        $data['is_active'] = (int) $request->input('is_active', 0);

        $blog->update($data);

        // Handle image upload using Spatie Media Library
        if ($request->hasFile('image')) {
            // Clear existing media
            $blog->clearMediaCollection('blogs');

            // Add new media
            $blog->addMediaFromRequest('image')
                ->usingName('blog_image')
                ->usingFileName(time() . '_blog_' . $blog->id . '.' . $request->file('image')->getClientOriginalExtension())
                ->toMediaCollection('blogs');
        }

        Report::addToLog('تعديل المدونه');
        return response()->json(['url' => route('admin.blogs.index')]);
    }

    public function show($id)
    {
        $blog = Blog::with(['category', 'reactions'])->findOrFail($id);

        // Get first page of comments (5 per page)
        $comments = $blog->comments()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(5);

        return view('admin.blogs.show', compact('blog', 'comments'));
    }

    public function destroy($id)
    {
        $blog = Blog::findOrFail($id);

        // Clear media before deleting
        $blog->clearMediaCollection('blogs');

        $blog->delete();
        Report::addToLog('حذف المدونه');
        return response()->json(['id' => $id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $blog = Blog::findOrFail($id);
            // Clear media before deleting
            $blog->clearMediaCollection('blogs');
            $blog->delete();
        }

        Report::addToLog('حذف مجموعة من المدونات');
        return response()->json(['message' => 'تم حذف البيانات بنجاح']);
    }

    /**
     * Load more comments for a blog
     */
    public function loadMoreComments(Request $request, $blogId)
    {
        try {
            $blog = Blog::findOrFail($blogId);
            $page = $request->get('page', 1);

            $comments = $blog->comments()
                ->with('user')
                ->orderBy('created_at', 'desc')
                ->paginate(5, ['*'], 'page', $page);

            if ($comments->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => __('admin.no_more_comments'),
                    'html' => ''
                ]);
            }

            $html = view('admin.blogs.partials.comments', compact('comments'))->render();

            return response()->json([
                'success' => true,
                'html' => $html,
                'has_more' => $comments->hasMorePages(),
                'next_page' => $comments->currentPage() + 1
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to load comments'], 500);
        }
    }

    /**
     * Toggle comment approval status
     */
    public function toggleCommentApproval($commentId)
    {
        try {
            $comment = BlogComment::findOrFail($commentId);
            $comment->is_approved = !$comment->is_approved;
            $comment->save();

            $status = $comment->is_approved ? 'approved' : 'unapproved';
            Report::addToLog("تغيير حالة موافقة التعليق إلى {$status}");

            return response()->json([
                'success' => true,
                'is_approved' => $comment->is_approved,
                'message' => $comment->is_approved ? 'Comment approved' : 'Comment unapproved'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to toggle comment approval: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update comment'], 500);
        }
    }
}
