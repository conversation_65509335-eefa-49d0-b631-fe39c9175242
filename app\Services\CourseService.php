<?php

namespace App\Services;

use App\Facades\Responder;
use App\Models\Course;
use App\Models\User;
use App\Models\CourseEnrollment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CourseService
{

    public function __construct(
        protected Course $model
    ){

    }

    public function index()
    {
        return $this->model->where('is_active' , true)->orderBy('created_at','desc')->latest()->get();
    }

    public function getById($id)
    {
        $course =  $this->model->find($id);
        if(!$course)
        {
            return Responder::error('not found');
        }
        return $course;
    }

    /**
     * Enroll user in course by purchasing it
     *
     * @param User $user
     * @param int $courseId
     * @param array $paymentData
     * @return array
     */
    public function enrollInCourse(User $user, int $courseId, array $paymentData)
    {
        try {
            return DB::transaction(function () use ($user, $courseId, $paymentData) {
            // Get the course
            $course = Course::find($courseId);
            if (!$course || !$course->is_active) {
                return ['success' => false, 'message' => 'Course not found or not available'];
            }

            // Check for existing enrollment
            $existingEnrollment = $this->getExistingEnrollment($user->id, $courseId);
            if ($existingEnrollment) {
                return $this->handleExistingEnrollment($existingEnrollment, $paymentData);
            }

            // Create enrollment record with payment info
            $enrollment = CourseEnrollment::create([
                'user_id' => $user->id,
                'course_id' => $courseId,
                'enrolled_at' => now(),
                'status' => 'pending_payment',
                'payment_method' => $paymentData['payment_method'],
                'payment_status' => 'pending',
                'amount_paid' => $course->price,
                'bank_account_id' => $paymentData['bank_account_id'] ?? null,
            ]);

            // Create stage completion records for all course stages
            $enrollment->createStageCompletionRecords();

            // Process payment
            $paymentResult = $this->processCoursePayment($enrollment, $user, $paymentData);

            // Check if payment result has the expected structure
            if (!is_array($paymentResult) || !isset($paymentResult['success'])) {
                // Log unexpected payment result structure
                Log::error('Unexpected payment result structure', [
                    'payment_result' => $paymentResult,
                    'enrollment_id' => $enrollment->id,
                    'payment_method' => $paymentData['payment_method']
                ]);

                $enrollment->delete();
                return [
                    'success' => false,
                    'message' => 'Payment processing error. Please try again.'
                ];
            }

            if ($paymentResult['success']) {
                return [
                    'success' => true,
                    'message' => 'Successfully enrolled in course',
                    'enrollment' => $enrollment->fresh(),
                    'payment_result' => $paymentResult
                ];
            } else {
                // Payment failed, delete the enrollment
                $enrollment->delete();
                return [
                    'success' => false,
                    'message' => $paymentResult['message'] ?? 'Payment failed'
                ];
            }
            });
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log the error
            Log::error('Course enrollment service error', [
                'user_id' => $user->id,
                'course_id' => $courseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred during enrollment. Please try again.'
            ];
        }
    }

    /**
     * Process payment for course enrollment
     */
    private function processCoursePayment(CourseEnrollment $enrollment, User $user, array $paymentData)
    {
        try {
            $result = null;

            switch ($paymentData['payment_method']) {
                case 'wallet':
                    $result = $this->processWalletPayment($enrollment, $user);
                    break;



                case 'credit_card':
                case 'mada':
                case 'apple_pay':
                    $result = $this->processElectronicPayment($enrollment, $paymentData);
                    break;

                default:
                    $result = ['success' => false, 'message' => 'Invalid payment method'];
                    break;
            }

            // Ensure result has the expected structure
            if (!is_array($result) || !isset($result['success'])) {
                Log::error('Payment method returned invalid structure', [
                    'payment_method' => $paymentData['payment_method'],
                    'result' => $result,
                    'enrollment_id' => $enrollment->id
                ]);

                return ['success' => false, 'message' => 'Payment processing error'];
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Payment processing exception', [
                'payment_method' => $paymentData['payment_method'],
                'enrollment_id' => $enrollment->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => 'Payment processing failed'];
        }
    }

    /**
     * Process wallet payment for course
     */
    private function processWalletPayment(CourseEnrollment $enrollment, User $user)
    {
        if ($user->wallet_balance >= $enrollment->amount_paid) {
            // Deduct from wallet
            $user->decrement('wallet_balance', $enrollment->amount_paid);

            // Mark payment as completed
            $enrollment->markPaymentAsCompleted('WALLET-' . time());

            // Process loyalty points
            $this->processLoyaltyPoints($user, $enrollment->amount_paid);

            return ['success' => true, 'message' => 'Payment successful'];
        }

        return ['success' => false, 'message' => 'Insufficient wallet balance'];
    }

    /**
     * Process bank transfer payment for course
     */
    private function processBankTransferPayment(CourseEnrollment $enrollment, array $paymentData)
    {
        // For bank transfer, enrollment stays pending until admin confirms
        $enrollment->update([
            'payment_reference' => 'BANK-COURSE-' . $enrollment->id,
        ]);

        // Get bank account details
        $bankDetails = $this->getBankTransferDetails($enrollment);

        return [
            'success' => true,
            'message' => 'Enrollment created successfully. Please complete bank transfer.',
            'requires_bank_transfer' => true,
            'enrollment_id' => $enrollment->id,
            'payment_reference' => $enrollment->payment_reference,
            'bank_name' => $bankDetails['bank_name'],
            'beneficiary_name' => $bankDetails['beneficiary_name'],
            'account_number' => $bankDetails['account_number'],
            'iban' => $bankDetails['iban'],
            'instructions' => $bankDetails['instructions'],
        ];
    }

    /**
     * Process electronic payment for course using MyFatoorah
     */
    private function processElectronicPayment(CourseEnrollment $enrollment, array $paymentData)
    {
        try {
            $myfatoorahService = app(\App\Services\Myfatoorah\CoursePaymentService::class);

            $user = $enrollment->user;
            $course = $enrollment->course;

            // Create payment invoice with MyFatoorah
            $result = $myfatoorahService->createCoursePaymentInvoice(
                $enrollment,
                $user,
                $course,
                [
                    'gateway' => $paymentData['gateway'] ?? 'myfatoorah'
                ]
            );

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Redirect to MyFatoorah payment gateway',
                    'requires_payment_gateway' => true,
                    'payment_url' => $result['invoice_url'],
                    'invoice_id' => $result['invoice_id'],
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['message']
                ];
            }

        } catch (\Exception $e) {
            Log::error('Electronic payment processing failed', [
                'enrollment_id' => $enrollment->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Payment gateway error. Please try again.'
            ];
        }
    }

    /**
     * Get existing enrollment for user and course
     */
    private function getExistingEnrollment(int $userId, int $courseId): ?CourseEnrollment
    {
        return CourseEnrollment::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->first();
    }

    /**
     * Handle existing enrollment based on its status
     */
    private function handleExistingEnrollment(CourseEnrollment $existingEnrollment, array $paymentData)
    {
        switch ($existingEnrollment->status) {
            case 'active':
            case 'completed':
                return [
                    'success' => false,
                    'message' => 'User is already enrolled in this course',
                    'enrollment' => $existingEnrollment
                ];

            case 'pending_payment':
                // Check if payment method is the same, if so, return existing enrollment
                if ($existingEnrollment->payment_method === $paymentData['payment_method']) {
                    // Try to process payment again for existing enrollment
                    $paymentResult = $this->processCoursePayment($existingEnrollment, $existingEnrollment->user, $paymentData);

                    if ($paymentResult['success']) {
                        return [
                            'success' => true,
                            'message' => 'Payment processed for existing enrollment',
                            'enrollment' => $existingEnrollment->fresh(),
                            'payment_result' => $paymentResult
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => $paymentResult['message'] ?? 'Payment failed for existing enrollment'
                        ];
                    }
                } else {
                    // Different payment method, update the enrollment
                    $existingEnrollment->update([
                        'payment_method' => $paymentData['payment_method'],
                        'bank_account_id' => $paymentData['bank_account_id'] ?? null,
                        'enrolled_at' => now(),
                    ]);

                    $paymentResult = $this->processCoursePayment($existingEnrollment, $existingEnrollment->user, $paymentData);

                    if ($paymentResult['success']) {
                        return [
                            'success' => true,
                            'message' => 'Enrollment updated and payment processed',
                            'enrollment' => $existingEnrollment->fresh(),
                            'payment_result' => $paymentResult
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => $paymentResult['message'] ?? 'Payment failed'
                        ];
                    }
                }

            case 'cancelled':
            case 'failed':
                // Reactivate the enrollment with new payment method
                $existingEnrollment->update([
                    'status' => 'pending_payment',
                    'payment_status' => 'pending',
                    'payment_method' => $paymentData['payment_method'],
                    'bank_account_id' => $paymentData['bank_account_id'] ?? null,
                    'enrolled_at' => now(),
                    'payment_reference' => null,
                    'payment_completed_at' => null,
                ]);

                $paymentResult = $this->processCoursePayment($existingEnrollment, $existingEnrollment->user, $paymentData);

                if ($paymentResult['success']) {
                    return [
                        'success' => true,
                        'message' => 'Enrollment reactivated and payment processed',
                        'enrollment' => $existingEnrollment->fresh(),
                        'payment_result' => $paymentResult
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $paymentResult['message'] ?? 'Payment failed'
                    ];
                }

            default:
                return [
                    'success' => false,
                    'message' => 'Invalid enrollment status'
                ];
        }
    }

    /**
     * Check if user is enrolled in course
     */
    public function isUserEnrolled(int $userId, int $courseId): bool
    {
        return CourseEnrollment::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->whereIn('status', ['active', 'completed'])
            ->exists();
    }

    /**
     * Get user's enrolled courses
     */
    public function getUserEnrolledCourses(int $userId)
    {
        return CourseEnrollment::with(['course.stages.userCompletion'])
            ->where('user_id', $userId)
            ->whereIn('status', ['active', 'completed'])
            ->get()
            ->pluck('course');
    }

    /**
     * Get course with enrollment status for user
     */
    public function getCourseWithEnrollmentStatus(int $courseId, ?int $userId = null)
    {
        $course = $this->getById($courseId);

        if ($userId) {
            $course->is_enrolled = $this->isUserEnrolled($userId, $courseId);
            $enrollment = CourseEnrollment::where('user_id', $userId)
                ->where('course_id', $courseId)
                ->first();
            $course->enrollment = $enrollment;
        } else {
            $course->is_enrolled = false;
            $course->enrollment = null;
        }

        return $course;
    }

    /**
     * Process loyalty points for course purchase
     */
    private function processLoyaltyPoints(User $user, float $amountPaid)
    {
        $pointsEarned = $user->calculateLoyaltyPointsEarned($amountPaid);
        if ($pointsEarned > 0) {
            $user->addLoyaltyPoints($pointsEarned);
        }
    }

    /**
     * Get bank transfer details for course payment
     */
    private function getBankTransferDetails(CourseEnrollment $enrollment)
    {
        // If enrollment has a specific bank account, use it
        if ($enrollment->bank_account_id && $enrollment->bankAccount) {
            return [
                'bank_name' => $enrollment->bankAccount->bank_name,
                'beneficiary_name' => $enrollment->bankAccount->holder_name,
                'account_number' => $enrollment->bankAccount->account_number,
                'iban' => $enrollment->bankAccount->iban,
                'enrollment_id' => $enrollment->id,
                'payment_reference' => $enrollment->payment_reference,
                'instructions' => 'Please include your enrollment ID in the transfer reference',
            ];
        }

        // Fallback to default bank details
        return [
            'bank_name' => 'البنك الأهلي السعودي',
            'beneficiary_name' => 'شركة سوريسو للتقنية',
            'account_number' => '**********',
            'iban' => 'SA********************12',
            'enrollment_id' => $enrollment->id,
            'payment_reference' => $enrollment->payment_reference,
            'instructions' => 'Please include your enrollment ID in the transfer reference',
        ];
    }



    /**
     * Confirm payment for course enrollment (called by payment gateway webhook or admin)
     */
    public function confirmCoursePayment(CourseEnrollment $enrollment, ?string $paymentReference = null)
    {
        try {
            return DB::transaction(function () use ($enrollment, $paymentReference) {
                $enrollment->markPaymentAsCompleted($paymentReference);

                // Process loyalty points for electronic payments
                if (in_array($enrollment->payment_method, ['credit_card', 'mada', 'apple_pay'])) {
                    $this->processLoyaltyPoints($enrollment->user, $enrollment->amount_paid);
                }

                return $enrollment;
            });
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            // Log the error
            Log::error('Course payment confirmation error', [
                'enrollment_id' => $enrollment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \Exception('Failed to confirm course payment. Please try again.');
        }
    }

    /**
     * Update enrollment status directly (for webhook processing)
     */
    public function updateEnrollmentStatus(CourseEnrollment $enrollment, string $status, string $paymentStatus, ?string $paymentReference = null)
    {
        try {
            return DB::transaction(function () use ($enrollment, $status, $paymentStatus, $paymentReference) {
                $updateData = [
                    'status' => $status,
                    'payment_status' => $paymentStatus,
                ];

                if ($paymentReference) {
                    $updateData['payment_reference'] = $paymentReference;
                }

                if ($paymentStatus === 'paid') {
                    $updateData['payment_completed_at'] = now();

                    // Process loyalty points for successful payments
                    $this->processLoyaltyPoints($enrollment->user, $enrollment->amount_paid);
                }

                $enrollment->update($updateData);

                Log::info('Enrollment status updated', [
                    'enrollment_id' => $enrollment->id,
                    'status' => $status,
                    'payment_status' => $paymentStatus
                ]);

                return $enrollment->fresh();
            });
        } catch (\Exception $e) {
            Log::error('Failed to update enrollment status', [
                'enrollment_id' => $enrollment->id,
                'status' => $status,
                'payment_status' => $paymentStatus,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('Failed to update enrollment status: ' . $e->getMessage());
        }
    }
}