<?php

namespace App\Http\Requests\Admin\Provider;

use App\Rules\ProviderPhoneUnique;
use Illuminate\Foundation\Http\FormRequest;

class Update extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $userId = $this->route('id');
        return [
            // User basic fields
            'name'                      => 'required|max:50',
            'country_code'              => 'required|numeric|digits_between:2,5',
            'phone'                     => [
                'required',
                'phone:SA',
                new ProviderPhoneUnique($userId)
            ],
            'email'                     => "required|email|max:50|unique:users,email,{$userId},id,deleted_at,NULL",
            'password'                  => 'nullable|confirmed|min:6|max:100',
            'city_id'                   => 'required|exists:cities,id',
            'region_id'                   => 'required|exists:regions,id',

            'gender'                    => 'required|in:male,female',
             'image'                     => 'nullable|image|max:2048',

            // Provider specific fields
            'commercial_name'           => 'required|array',
            'commercial_name.ar'        => 'required|string|max:100',
            'commercial_name.en'        => 'nullable|string|max:100',
            'salon_type'                => 'required|string|in:salon,beauty_center',
            'residence_type'            => 'required_if:nationality,other|nullable|string|in:individual,professional',
            'nationality'               => 'required|string|in:saudi,other',
            
            'commercial_register_no'    => 'required|string|max:50',
            'sponsor_name'              => 'required_if:nationality,other|nullable|string|max:100',
            'sponsor_phone'             => 'required_if:nationality,other|nullable|string|max:20',
            'institution_name'          => 'required|string|max:100',
            'in_home'                   => 'nullable|boolean',
            'in_salon'                  => 'nullable|boolean',
            'home_fees'                 => 'nullable|numeric|min:0|max:999999.99',

            // Document uploads
            'logo'                      => 'nullable|image|max:2048',
            'commercial_register_image' => 'nullable|image|max:2048',
            'residence_image'           => 'required_if:nationality,other|nullable|image|max:2048',
        ];
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'                      => __('admin.this_field_is_required'),
            'name.max'                          => __('admin.max_length_191'),
            'country_code.required'             => __('admin.this_field_is_required'),
            'country_code.numeric'              => __('admin.must_be_numeric'),
            'country_code.digits_between'       => __('admin.country_code_digits_between_2_5'),
            'phone.required'                    => __('admin.this_field_is_required'),
            'phone.min'                         => __('admin.phone_min_8_digits'),
            'phone.unique'                      => __('admin.phone_already_exists'),
            'email.required'                    => __('admin.this_field_is_required'),
            'email.email'                       => __('admin.email_formula_is_incorrect'),
            'email.max'                         => __('admin.max_length_191'),
            'email.unique'                      => __('admin.email_already_exists'),
            'password.min'                      => __('admin.password_min_6_characters'),
            'city_id.required'                  => __('admin.this_field_is_required'),
            'city_id.exists'                    => __('admin.city_not_found'),
            'status.required'                   => __('admin.this_field_is_required'),
            'status.in'                         => __('admin.invalid_status'),
            'image.image'                       => __('admin.must_be_image'),
            'image.max'                         => __('admin.image_max_size_2mb'),
            'commercial_name.max'               => __('admin.max_length_191'),
            'commercial_register_no.max'        => __('admin.max_length_50'),
            'institution_name.max'              => __('admin.max_length_191'),
            'sponsor_name.max'                  => __('admin.max_length_191'),
            'sponsor_phone.max'                 => __('admin.max_length_20'),
            'nationality.max'                   => __('admin.max_length_100'),
            'residence_type.in'                 => __('admin.invalid_residence_type'),
            'salon_type.in'                     => __('admin.invalid_salon_type'),
            'description.max'                   => __('admin.max_length_1000'),
            'home_fees.numeric'                 => __('admin.must_be_numeric'),
            'home_fees.min'                     => __('admin.must_be_positive'),
            'home_fees.max'                     => __('admin.max_amount_999999'),
            'mobile_service_fee.numeric'        => __('admin.must_be_numeric'),
            'mobile_service_fee.min'            => __('admin.must_be_positive'),
            'mobile_service_fee.max'            => __('admin.max_amount_999999'),
            'logo.image'                        => __('admin.must_be_image'),
            'logo.max'                          => __('admin.image_max_size_2mb'),
            'commercial_register_image.image'   => __('admin.must_be_image'),
            'commercial_register_image.max'     => __('admin.image_max_size_2mb'),
            'residence_image.image'             => __('admin.must_be_image'),
            'residence_image.max'               => __('admin.image_max_size_2mb'),
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Convert checkbox values to boolean
        $this->merge([
            'in_home' => $this->has('in_home') ? true : false,
            'in_salon' => $this->has('in_salon') ? true : false,
            'is_mobile' => $this->has('is_mobile') ? true : false,
        ]);

        // Fix phone number format if needed
        if ($this->has('phone')) {
            $this->merge([
                'phone' => ltrim($this->phone, '0'),
            ]);
        }

        // Remove password if empty (to keep current password)
        if (empty($this->password)) {
            $this->request->remove('password');
        }
    }
}
