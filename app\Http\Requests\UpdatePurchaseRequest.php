<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePurchaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'advertisement_id' => 'sometimes|exists:advertisements,id',
            'wallet_credit_used' => 'sometimes|numeric|min:0',
           'payment_method' => 'sometimes|string|in:online,wallet', 

        ];
    }
}
