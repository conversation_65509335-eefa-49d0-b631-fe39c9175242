<?php

namespace App\Traits;

trait menu {
  public function home() {

    $menu = [
     
      [
        'name'  => __('admin.clients'),
        'count' => \App\Models\User::count(),
        'icon'  => 'icon-users',
        'url'   => url('admin/clients'),
      ],
            [
        'name'  => __('admin.conversations'),
        'count' => \App\Models\Conversation::count(),
        'icon'  => 'icon-list',
        'url'   => url('admin/conversations'),
      ],
                  [
        'name'  => __('admin.advertisements'),
        'count' => \App\Models\Advertisement::count(),
        'icon'  => 'icon-list',
        'url'   => url('admin/advertisements'),
      ],


      //trainers
      [
        'name'  => __('admin.trainers'),
        'count' => \App\Models\TrainerApplication::count(),
        'icon'  => 'icon-users',
        'url'   => url('admin/trainers'),
      ],
      // purchares
      [
        'name'  => __('admin.purchases'),
        'count' => \App\Models\PurchaseRequest::count(),
        'icon'  => 'icon-list',
        'url'   => url('admin/purchases'),
      ],
      // regions
      [
        'name'  => __('admin.regions'),
        'count' => \App\Models\Region::count(),
        'icon'  => 'icon-map',
        'url'   => url('admin/regions'),
      ],
      //countries
      [
        'name'  => __('admin.countries'),
        'count' => \App\Models\Country::count(),
        'icon'  => 'icon-globe',
        'url'   => url('admin/countries'),
      ],
      //cities
      [
        'name'  => __('admin.cities'),
        'count' => \App\Models\City::count(),
        'icon'  => 'icon-list',
        'url'   => url('admin/cities'),
      ],
      [
        'name'  => __('admin.complaints'),
        'count' => \App\Models\Complaint::count(),
        'icon'  => 'icon-list',
        'url'   => url('admin/complaints'),
      ],
      //reports
      

      
      
      




      //             [
      //   'name'  => __('admin.conversations'),
      //   'count' => \App\Models\Conversation::count(),
      //   'icon'  => 'icon-list',
      //   'url'   => url('admin/conversations'),
      // ],
      //             [
      //   'name'  => __('admin.conversations'),
      //   'count' => \App\Models\Conversation::count(),
      //   'icon'  => 'icon-list',
      //   'url'   => url('admin/conversations'),
      // ],
      // [
      //   'name'  => __('admin.services'),
      //   'count' => \App\Models\Service::count(),
      //   'icon'  => 'icon-users',
      //   'url'   => url('admin/services'),
      // ],

      // [
      //   'name'  => __('admin.product-categories'),
      //   'count' => \App\Models\ProductCategory::count(),
      //   'icon'  => 'icon-users',
      //   'url'   => url('admin/products'),
      // ],
      
      // [
      //   'name'  => __('admin.products'),
      //   'count' => \App\Models\Product::count(),
      //   'icon'  => 'icon-users',
      //   'url'   => url('admin/products'),
      // ],

      // [
      //   'name'  => __('admin.orders_only_services'),
      //   'count' => \App\Models\Order::whereHas('items', function($q) {
      //       $q->where('item_type', 'App\\Models\\Service');
      //   })->whereDoesntHave('items', function($q) {
      //       $q->where('item_type', 'App\\Models\\Product');
      //   })->sum('total'),
      //   'icon'  => 'icon-list',
      // ],
      // [
      //   'name'  => __('admin.orders_only_products'),
      //   'count' => \App\Models\Order::whereHas('items', function($q) {
      //       $q->where('item_type', 'App\\Models\\Product');
      //   })->sum('total'),
      //   'icon'  => 'icon-list',
      // ],
      // [
      //   'name'  => __('admin.total_orders'),
      //   'count' => \App\Models\Order::sum('total'),
      //   'icon'  => 'icon-list',
      // ],

      // [
      //   'name'  => __('admin.platform_commission'),
      //   'count' => \App\Models\Order::sum('platform_commission'),
      //   'icon'  => 'icon-list',
      // ],

      // [
      //   'name'  => __('admin.delivery_fees'),
      //   'count' => \App\Models\Order::sum('delivery_fee'),
      //   'icon'  => 'icon-list',
      // ],

      // [
      //   'name'  => __('admin.cancel_fees'),
      //   'count' => \App\Models\Order::sum('cancel_fees'),
      //   'icon'  => 'icon-list',
      // ],

      // [
      //   'name'  => __('admin.total_discounts'),
      //   'count' => \App\Models\Order::sum('discount_amount'),
      //   'icon'  => 'icon-list',
      // ],

      // [
      //   'name'  => __('admin.total_revenue'),
      //   'count' =>\App\Models\Order::sum('platform_commission') + \App\Models\Order::sum('delivery_fee') +\App\Models\Order::sum('cancel_fees'),
      //   'icon'  => 'icon-list',
      // ],

      // 6. Total clients
      // [
      //   'name'  => __('admin.total_clients'),
      //   'count' => \App\Models\User::where('type', 'client')->count(),
      //   'icon'  => 'icon-users',
      // ],
      // // 17. Total client account deletion requests
      // [
      //   'name'  => __('admin.total_client_deletion_requests'),
      //   'count' => \App\Models\AccountDeletionRequest::whereHas('user', function($q) {
      //       $q->where('type', 'client');
      //   })->count(),
      //   'icon'  => 'icon-users',
      // ],
      // // 18. Total blocked clients
      // [
      //   'name'  => __('admin.total_blocked_clients'),
      //   'count' => \App\Models\User::where('type', 'client')->where('status', 'blocked')->count(),
      //   'icon'  => 'icon-users',
      // ],
      // 19. Total service orders
      // [
      //   'name'  => __('admin.total_service_orders'),
      //   'count' => \App\Models\OrderItem::where('item_type', 'App\\fModels\\Service')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // 20. Total provider registration requests
      // [
      //   'name'  => __('admin.total_provider_registration_requests'),
      //   'count' => \App\Models\Provider::count(),
      //   'icon'  => 'icon-users',
      // ],
      // // 21. Total provider account deletion requests
      // [
      //   'name'  => __('admin.total_provider_deletion_requests'),
      //   'count' => \App\Models\AccountDeletionRequest::whereHas('user', function($q) {
      //       $q->where('type', 'provider');
      //   })->count(),
      //   'icon'  => 'icon-users',
      // ],
      // // 22. Total blocked providers
      // [
      //   'name'  => __('admin.total_blocked_providers'),
      //   'count' => \App\Models\Provider::where('status', 'blocked')->count(),
      //   'icon'  => 'icon-users',
      // ],
      // // 23. Total active providers
      // [
      //   'name'  => __('admin.total_active_providers'),
      //   'count' => \App\Models\Provider::where('is_active', 1)->count(),
      //   'icon'  => 'icon-users',
      // ],
      // // 24. Total bookings
      // [
      //   'name'  => __('admin.total_bookings'),
      //   'count' => \App\Models\Order::count(),
      //   'icon'  => 'icon-list',
      // ],
      // // 25. Total bookings pending payment confirmation
      // [
      //   'name'  => __('admin.total_bookings_pending_payment_confirmation'),
      //   'count' => \App\Models\Order::where('current_status', 'pending_payment')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // // 26. Total bookings under processing
      // [
      //   'name'  => __('admin.total_bookings_processing'),
      //   'count' => \App\Models\Order::where('current_status', 'processing')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // // 27. Total ongoing bookings
      // [
      //   'name'  => __('admin.total_bookings_ongoing'),
      //   'count' => \App\Models\Order::where('current_status', 'ongoing')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // // 28. Total cancelled bookings
      // [
      //   'name'  => __('admin.total_bookings_cancelled'),
      //   'count' => \App\Models\Order::where('current_status', 'cancelled')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // // 29. Total completed bookings
      // [
      //   'name'  => __('admin.total_bookings_completed'),
      //   'count' => \App\Models\Order::where('current_status', 'completed')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // // 30. Total failed bookings
      // [
      //   'name'  => __('admin.total_bookings_failed'),
      //   'count' => \App\Models\Order::where('current_status', 'failed')->count(),
      //   'icon'  => 'icon-list',
      // ],
      // Uncomment the following if ConsultationRequest model exists
      // [
      //   'name'  => __('admin.total_consultation_requests'),
      //   'count' => \App\Models\ConsultationRequest::count(),
      //   'icon'  => 'icon-list',
      // ],
      // 32. Total unread contact us messages
      // [
      //   'name'  => __('admin.total_unread_contactus_messages'),
      //   'count' => \App\Models\ContactUs::where('is_read', 0)->count(),
      //   'icon'  => 'icon-list',
      // ],
      
    ];

    return $menu;
  }

  

}