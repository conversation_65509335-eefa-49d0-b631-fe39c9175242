<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_requests', function (Blueprint $table) {
            $table->id();
             $table->foreignId('advertisement_id')->constrained()->onDelete('cascade');
            $table->foreignId('buyer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('seller_id')->constrained('users')->onDelete('cascade');
            $table->enum('status', ['under_review', 'waiting_buyer_confirmation', 'completed', 'problem', 'cancelled'])->default('under_review');
            $table->decimal('amount_paid', 10, 2);
            $table->string('payment_method')->nullable(); 
            $table->decimal('wallet_credit_used', 10, 2)->default(0);
            $table->boolean('payment_status')->default(false); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_requests');
    }
};
