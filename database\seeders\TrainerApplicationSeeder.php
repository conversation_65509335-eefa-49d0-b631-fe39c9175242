<?php

namespace Database\Seeders;

use App\Models\TrainerApplication;
use App\Models\User;
use App\Models\City;
use App\Models\Region;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TrainerApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::inRandomOrder()->take(5)->get();
        $regions = Region::pluck('id')->toArray();
        $cities = City::pluck('id')->toArray();

        foreach ($users as $user) {
            TrainerApplication::create([
                'user_id' => $user->id,
                'name' => [
                    'ar' => 'مدرب ' . $user->id,
                    'en' => 'Trainer ' . $user->id,
                ],
                'bio' => [
                    'ar' => 'نبذة قصيرة عن المدرب ' . $user->id,
                    'en' => 'Short bio for trainer ' . $user->id,
                ],
                'experience' => [
                    'ar' => 'خبرات مميزة في التدريب ' . $user->id,
                    'en' => 'Extensive training experience ' . $user->id,
                ],
                'region_id' => fake()->randomElement($regions),
                'city_id' => fake()->randomElement($cities),
                'training_price' => fake()->numberBetween(500, 2000),
                'contact_phone' => '010' . fake()->unique()->numerify('########'),
                'contact_email' => fake()->unique()->safeEmail(),
                'contact_whatsapp' => '015' . fake()->unique()->numerify('########'),
                'status' => 'pending',
            ]);
        }
    }
}
