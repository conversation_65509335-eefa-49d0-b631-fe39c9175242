<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CourseStageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Course stages/lessons for each course

        DB::table('course_stages')->insert([
            // Course 1: Hair Styling Basics
            [
                'course_id' => 1,
                'title' => json_encode([
                    'en' => 'Introduction to Hair Types',
                    'ar' => 'مقدمة في أنواع الشعر'
                ]),
                'order' => 1,
                'created_at' => now()->subDays(20),
                'updated_at' => now()->subDays(20),
            ],
            [
                'course_id' => 1,
                'title' => json_encode([
                    'en' => 'Basic Cutting Techniques',
                    'ar' => 'تقنيات القص الأساسية'
                ]),
                'order' => 2,
                'created_at' => now()->subDays(20),
                'updated_at' => now()->subDays(20),
            ],
            [
                'course_id' => 1,
                'title' => json_encode([
                    'en' => 'Styling Tools and Products',
                    'ar' => 'أدوات ومنتجات التصفيف'
                ]),
                'order' => 3,
                'created_at' => now()->subDays(20),
                'updated_at' => now()->subDays(20),
            ],

            // Course 2: Advanced Nail Art
            [
                'course_id' => 2,
                'title' => json_encode([
                    'en' => 'Nail Preparation Techniques',
                    'ar' => 'تقنيات تحضير الأظافر'
                ]),
                'order' => 1,
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(15),
            ],
            [
                'course_id' => 2,
                'title' => json_encode([
                    'en' => 'Basic Nail Art Designs',
                    'ar' => 'تصاميم فن الأظافر الأساسية'
                ]),
                'order' => 2,
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(15),
            ],
            [
                'course_id' => 2,
                'title' => json_encode([
                    'en' => 'Advanced Techniques',
                    'ar' => 'التقنيات المتقدمة'
                ]),
                'order' => 3,
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(15),
            ],

            // Course 3: Skincare Fundamentals
            [
                'course_id' => 3,
                'title' => json_encode([
                    'en' => 'Understanding Skin Types',
                    'ar' => 'فهم أنواع البشرة'
                ]),
                'order' => 1,
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ],
            [
                'course_id' => 3,
                'title' => json_encode([
                    'en' => 'Cleansing Techniques',
                    'ar' => 'تقنيات التنظيف'
                ]),
                'order' => 2,
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ],
            [
                'course_id' => 3,
                'title' => json_encode([
                    'en' => 'Facial Massage Techniques',
                    'ar' => 'تقنيات تدليك الوجه'
                ]),
                'order' => 3,
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ],
        ]);

        $this->command->info('Course stages seeded successfully!');
    }
}
