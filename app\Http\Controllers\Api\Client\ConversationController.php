<?php
namespace App\Http\Controllers\Api\Client;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Client\ConversationDetailsResource;
use App\Http\Resources\Api\Client\ConversationResource;
use App\Http\Resources\Api\Client\MessageResource;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Services\Responder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConversationController extends Controller
{
    public function index()
    {
        $userId = Auth::id();

        $conversations = Conversation::whereHas('messages', function ($q) use ($userId) {
            $q->where('sender_id', $userId)->orWhere('receiver_id', $userId);
        })->with(['lastMessage', 'advertisement'])->get();

        return Responder::success(ConversationResource::collection($conversations));
    }

    public function store(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'advertisement_id' => 'required|exists:advertisements,id',
            'message' => 'nullable|string|max:200',
            'file' => 'nullable|file|max:10240',
        ]);

        $senderId = Auth::id();

        $conversation = Conversation::firstOrCreate([
            'advertisement_id' => $request->advertisement_id,
        ]);

        $type = match (true) {
            $request->filled('message') && $request->hasFile('file') => 'both',
            $request->filled('message') => 'text',
            $request->hasFile('file') => 'file',
            default => null,
        };

        if (!$type) {
            return Responder::error(__('apis.message_required'), 422);
        }

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $senderId,
            'receiver_id' => $request->receiver_id,
            'message' => $request->message,
            'message_type' => $type,
            'is_read' => false,
        ]);

        if ($request->hasFile('file')) {
            $message->addMediaFromRequest('file')->toMediaCollection('chat-attachments');
        }

        return Responder::success(new ConversationResource($conversation), ['message' => __('apis.message_sent')]);
    }

    public function getMessages($conversation_id)
{
    $userId = auth()->id();

    $conversation = \App\Models\Conversation::findOrFail($conversation_id);

    $hasAccess = Message::where('conversation_id', $conversation_id)
        ->where(function ($q) use ($userId) {
            $q->where('sender_id', $userId)->orWhere('receiver_id', $userId);
        })
        ->exists();

    if (!$hasAccess) {
        return \App\Services\Responder::error('Unauthorized access.', 403);
    }

    $messages = Message::with(['sender', 'receiver'])
        ->where('conversation_id', $conversation_id)
        ->orderBy('created_at', 'asc')
        ->get();

    return \App\Services\Responder::success([
        'messages' => MessageResource::collection($messages),
    ]);
}
}
