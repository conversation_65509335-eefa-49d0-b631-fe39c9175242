<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderBankAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Provider bank accounts for payments

        DB::table('provider_bank_accounts')->insert([
            [
                'provider_id' => 1,
                'bank_name' => 'National Bank of Kuwait',
                'holder_name' => '<PERSON>',
                'account_number' => '****************',
                'iban' => '******************************',
                'is_default' => true,
                'created_at' => now()->subDays(35),
                'updated_at' => now()->subDays(30),
            ],
            [
                'provider_id' => 2,
                'bank_name' => 'Kuwait Finance House',
                'holder_name' => 'Ahmad <PERSON>Mans<PERSON>',
                'account_number' => '****************',
                'iban' => '******************************',
                'is_default' => true,
                'created_at' => now()->subDays(28),
                'updated_at' => now()->subDays(25),
            ],
            [
                'provider_id' => 3,
                'bank_name' => 'Gulf Bank',
                'holder_name' => 'Fatima Al-Zahra',
                'account_number' => '****************',
                'iban' => '******************************',
                'is_default' => true,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'provider_id' => 1,
                'bank_name' => 'Commercial Bank of Kuwait',
                'holder_name' => 'Sarah Al-Ahmad',
                'account_number' => '****************',
                'iban' => '******************************',
                'is_default' => false,
                'created_at' => now()->subDays(65),
                'updated_at' => now()->subDays(10),
            ],
            [
                'provider_id' => 4,
                'bank_name' => 'Ahli United Bank',
                'holder_name' => 'Layla Al-Rashid',
                'account_number' => '****************',
                'iban' => '******************************',
                'is_default' => true,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
        ]);

        $this->command->info('Provider bank accounts seeded successfully!');
    }
}
