<?php

namespace App\Http\Requests\Admin\Client;

use App\Rules\ClientPhoneUnique;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class Update extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'     => 'nullable|max:191',
            'is_blocked'  => 'nullable',
            'country_code' => 'nullable',
            'city_id' => 'nullable|exists:cities,id',
            'region_id' => 'nullable|exists:regions,id',
            'phone'    => [
                'required',
                'phone:SA',
                 Rule::unique('users', 'phone')->whereNull('deleted_at'),
            ],
            'email'    => 'nullable|email|max:191|unique:users,email,'.$this->id.',id,deleted_at,NULL',
            'password' => ['nullable', 'min:6'],
            'image'   => ['nullable', 'image'],
            'gender' => 'nullable|in:female,male'
        ];
    }
}
