<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'advertisement_id',
        'buyer_id',
        'seller_id',
        'status',
        'amount_paid',
        'wallet_credit_used',
         'payment_method', 
         'payment_status',
    ];

    protected $casts = [
        'amount_paid' => 'decimal:2',
        'wallet_credit_used' => 'decimal:2',
    ];

    // Relationships
    public function advertisement()
    {
        return $this->belongsTo(Advertisement::class);
    }


    //ratings
    public function ratings()
    {
        return $this->hasMany(Rating::class ,'purchase_request_id');
    }

    public function buyer()
    {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function walletTransaction()
    {
        return $this->hasOne(WalletTransaction::class);
    }

// Removed duplicate ratings() method
    public function paymentMethods()
{
    return $this->belongsToMany(PaymentMethod::class); 
}

// App\Models\PurchaseRequest.php

public function scopeSearch($query, $searchArray = [])
{
    $searchArray = is_array($searchArray) ? $searchArray : [];

    $query->where(function ($q) use ($searchArray) {
        foreach ($searchArray as $key => $value) {
            if (is_null($value) || $value === '') continue;

            if ($key === 'buyer_name') {
                $q->whereHas('buyer', function ($subQ) use ($value) {
                    $subQ->where('name', 'like', "%{$value}%");
                });
            } elseif ($key === 'seller_name') {
                $q->whereHas('seller', function ($subQ) use ($value) {
                    $subQ->where('name', 'like', "%{$value}%");
                });
            } elseif ($key === 'advertisement_name') {
                $q->whereHas('advertisement', function ($subQ) use ($value) {
                    $subQ->where('name', 'like', "%{$value}%");
                });
            } elseif ($key === 'created_at_min') {
                $q->whereDate('created_at', '>=', $value);
            } elseif ($key === 'created_at_max') {
                $q->whereDate('created_at', '<=', $value);
            } elseif ($key !== 'order') {

                $q->where($key, 'like', "%{$value}%");
            }
        }
    });

    $orderDirection = isset($searchArray['order']) ? $searchArray['order'] : 'DESC';

    return $query->orderBy('created_at', $orderDirection);
}


}