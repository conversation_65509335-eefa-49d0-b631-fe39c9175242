<?php

namespace App\Http\Resources\Api\Client;

use App\Http\Resources\Api\RatingResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TrainerApplicationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => [
                'id' => $this->user_id,
                'name' => optional($this->user)->name,
            ],
            'name' => $this->getTranslation('name', app()->getLocale()),
                     'personal_image' => $this->getFirstMediaUrl('profile') ?: null,
            'bio' => $this->getTranslation('bio', app()->getLocale()),
            'experience' => $this->getTranslation('experience', app()->getLocale()),
            'region' => [
                'id' => $this->region_id,
                'name' => optional($this->region)->name,
            ],
            'city' => [
                'id' => $this->city_id,
                'name' => optional($this->city)->name,
            ],
            'training_price' => $this->training_price,
            'contact_phone' => $this->contact_phone,
            'contact_email' => $this->contact_email,
            'contact_whatsapp' => $this->contact_whatsapp,
            'status' => $this->status,
         'personal_image' => $this->getFirstMediaUrl('profile') ?: null,
'certificate_image' => $this->getFirstMediaUrl('certificate') ?: null,
'portfolio_images' => $this->getMedia('works')->map(function ($media) {
    return [
        'id' => $media->id,
        'url' => $media->getUrl(),
        // 'name' => $media->name,
        // 'size' => $media->size,
        'created_at' => $media->created_at->format('Y-m-d H:i:s'),
    ];
}),

    // when loaded RatingResource
       'rate' => RatingResource::collection(
    $this->whenLoaded('ratings', function () {
        return $this->ratings->where('status', 'approved');
    })
),
    'created_at' => $this->created_at->format('Y-m-d H:i:s'),
];
    }
}
