<?php
namespace App\Http\Resources\Api\Client;

use Illuminate\Http\Resources\Json\JsonResource;

class ConversationDetailsResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'advertisement_name' => $this->advertisement?->name,
            'sender_name' => $this->sender?->name,
            'message' => $this->message,
            'message_type' => $this->message_type,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
