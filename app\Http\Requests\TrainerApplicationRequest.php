<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TrainerApplicationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; 
    }

    public function rules(): array
    {
       return [
        'name' => ['required', 'array'],
        'name.ar' => ['required_without:name.en', 'string', 'min:5'],
        'name.en' => ['required_without:name.ar', 'string', 'min:5'],

        'bio' => ['required', 'array'],
        'bio.ar' => ['required_without:bio.en', 'string', 'min:10'],
        'bio.en' => ['required_without:bio.ar', 'string', 'min:10'],

        'experience' => ['required', 'array'],
        'experience.ar' => ['required_without:experience.en', 'string', 'min:10'],
        'experience.en' => ['required_without:experience.ar', 'string', 'min:10'],

        'region_id' => ['required', 'exists:regions,id'],
        'city_id' => ['required', 'exists:cities,id'],
        'training_price' => ['required', 'numeric', 'min:0'],
        // 'price_unit' => ['required', 'in:monthly'],

        'contact_phone' => ['required', 'string', 'phone:SA','unique:trainer_applications,contact_phone'],
        'contact_email' => ['nullable', 'email', 'unique:trainer_applications,contact_email'],
        'contact_whatsapp' => ['nullable', 'string','phone:SA','unique:trainer_applications,contact_whatsapp'],

    //    'personal_image' => ['required', 'file', 'image', 'max:5120'], // 5MB
'certificate_image' => ['required', 'file', 'image', 'max:5120'],
        //contact_email
        // 'works' => ['nullable', 'image', 'max:5120'], 
        // 'works' => ['required', 'array', 'max:10'],
        // 'works.*' => ['image', 'max:5120'],
    ];
    }

    

    public function messages(): array
    {
        return [
            'contact_phone' => __('validation.phone_format'),
            'contact_whatsapp' => __('validation.phone_format'),
 
            'name.ar.required' => 'الاسم بالعربية مطلوب',
            'name.en.required' => 'الاسم بالإنجليزية مطلوب',
            'name.ar.min'      => 'الاسم بالعربية يجب أن لا يقل عن 5 أحرف',
            'name.en.min'      => 'الاسم بالإنجليزية يجب أن لا يقل عن 5 أحرف',

            'bio.ar.required' => 'النبذة بالعربية مطلوبة',
            'bio.en.required' => 'النبذة بالإنجليزية مطلوبة',
            'bio.ar.min'      => 'النبذة بالعربية يجب أن لا تقل عن 10 أحرف',
            'bio.en.min'      => 'النبذة بالإنجليزية يجب أن لا تقل عن 10 أحرف',

            'experience.ar.required' => 'الخبرات بالعربية مطلوبة',
            'experience.en.required' => 'الخبرات بالإنجليزية مطلوبة',
            'experience.ar.min'      => 'الخبرات بالعربية يجب أن لا تقل عن 10 أحرف',
            'experience.en.min'      => 'الخبرات بالإنجليزية يجب أن لا تقل عن 10 أحرف',

            'region_id.required' => 'اختيار المنطقة مطلوب',
            'city_id.required'   => 'اختيار المدينة مطلوب',

            'training_price.required' => 'سعر التدريب مطلوب',

            'contact_phone.required' => 'رقم الجوال مطلوب',
            'contact_phone.unique'   => 'رقم الجوال مستخدم من قبل',

            'contact_email.email'  => 'صيغة البريد الإلكتروني غير صحيحة',
            'contact_email.unique' => 'البريد الإلكتروني مستخدم من قبل',

            'contact_whatsapp.unique' => 'رقم الواتساب مستخدم من قبل',

            'personal_image.required' => 'الصورة الشخصية مطلوبة',
            'personal_image.image'    => 'يجب أن تكون الصورة الشخصية صورة',
            'personal_image.max'      => 'أقصى حجم للصورة الشخصية هو 5 ميجا',

            'certificate_image.required' => 'صورة الشهادة مطلوبة',
            'certificate_image.image'    => 'يجب أن تكون الشهادة صورة',
            'certificate_image.max'      => 'أقصى حجم لصورة الشهادة هو 5 ميجا',

            'portfolio_images.array' => 'يجب إرسال صور الأعمال كمصفوفة',
            'portfolio_images.max'   => 'لا يمكن رفع أكثر من 10 صور أعمال',
            'portfolio_images.*.image' => 'كل صورة في الأعمال يجب أن تكون صورة صحيحة',
            'portfolio_images.*.max'   => 'أقصى حجم لكل صورة عمل هو 5 ميجا',
        ];
    }
}
