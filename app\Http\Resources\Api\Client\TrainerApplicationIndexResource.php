<?php

namespace App\Http\Resources\Api\Client;

use App\Http\Resources\Api\RatingResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TrainerApplicationIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->getTranslation('name', app()->getLocale()),
                     'personal_image' => $this->getFirstMediaUrl('profile') ?: null,
            'bio' => $this->getTranslation('bio', app()->getLocale()),
            'experience' => $this->getTranslation('experience', app()->getLocale()),
               'city' => [
                'id' => $this->city_id,
                'name' => optional($this->city)->name,
            ],
            'region' => [
                'id' => $this->region_id,
                'name' => optional($this->region)->name,
            ],
         
            'training_price' => $this->training_price,

            'rating_count' => $this->ratings_count ?? 0,
'rating_average' => round($this->average_rating, 1) ?? 0,


       'rate' => RatingResource::collection(
           $this->whenLoaded('ratings', function () {
               return $this->ratings->where('status', 'approved');
           })
       ),
       // 'created_at' => $media->created_at->format('Y-m-d H:i:s'), // Uncomment and fix if $media is defined
   ];
    }
}
