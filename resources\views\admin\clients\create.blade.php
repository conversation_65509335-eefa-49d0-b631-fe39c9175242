@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
<link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
<link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<form method="POST" action="{{route('admin.clients.store')}}" class="store form-horizontal" novalidate>
    <section id="multiple-column-form">
        <div class="row">
            <div class="col-md-3">
                <div class="col-12 card card-body">
                    <div class="imgMontg col-12 text-center">
                        <div class="dropBox">
                            <div class="textCenter">
                                <div class="imagesUploadBlock">
                                    <label class="uploadImg">
                                        <span><i class="feather icon-image"></i></span>
                                        <input type="file" accept="image/*" name="image" class="imageUploader">
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-9">
                <div class="card">
                    <div class="card-content">
                        <div class="card-body">
                            @csrf
                            <input type="hidden" name="type" value="client">
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.name')}}</label>
                                            <div class="controls">
                                                <input type="text" name="name" class="form-control" placeholder="{{__('admin.write_the_name')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.phone_number')}}</label>
                                            <div class="row">
                                                <div class="col-md-4 col-12">
                                                    <select name="country_code" class="form-control select2">
                                                        @foreach($countries as $country)
                                                        <option value="{{ $country->key }}" @if ($settings['default_country']==$country->id)
                                                            selected
                                                            @endif >
                                                            {{ '+'.$country->key }}{{ $country->flag}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-8 col-12">
                                                    <div class="controls">
                                                        <input type="number" name="phone" class="form-control" placeholder="{{__('admin.enter_phone_number')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" data-validation-number-message="{{__('admin.the_phone_number_ must_not_have_charachters_or_symbol')}}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{ __('admin.email')}}</label>
                                            <div class="controls">
                                                <input type="email" name="email" class="form-control" placeholder="{{__('admin.enter_the_email')}}" data-validation-email-message="{{__('admin.email_formula_is_incorrect')}}">
                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.password')}}</label>
                                            <div class="controls">
                                                <input type="password" name="password" class="form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.password_confirmation')}}</label>
                                            <div class="controls">
                                                <input type="password" name="password_confirmation" class="form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="region_id">{{__('admin.Region')}}</label>
                                            <div class="controls">
                                                <select name="region_id" id="region_id" class="form-control select2">
                                                    <option value="">{{__('admin.select_region')}}</option>
                                                    @foreach($regions as $region)
                                                    <option value="{{ $region->id }}">{{ $region->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="city_id">{{__('admin.City')}}</label>
                                            <div class="controls">
                                                <select name="city_id" id="city_id" class="form-control select2">
                                                    <option value="">{{__('admin.select_city')}}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    {{-- gender --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="">{{ __('admin.gender') }}</label>
                                            <div class="controls">
                                                <select name="gender" class="form-control select2">
                                                    <option value="">{{ __('admin.choose_gender') }}</option>
                                                    <option value="male">{{ __('admin.male') }}</option>
                                                    <option value="female">{{ __('admin.female') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-md-6 col-12">
    <div class="form-group">
        <label for="active">{{ __('admin.active') }}</label>
        <select name="active" class="select2 form-control">
            <option value="1">{{ __('admin.active') }}</option>
            <option value="0">{{ __('admin.inactive') }}</option>
        </select>
        @error('active')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>
</div>


                                        <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="first-name-column">{{__('admin.Validity')}}</label>
                                        <div class="controls">
                                            <select name="is_blocked" class="select2 form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                <option value>{{__('admin.Select_the_blocking_status')}}</option>
                                                <option value="1">{{__('admin.Prohibited')}}</option>
                                                <option value="0">{{__('admin.Unspoken')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>



                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{__('admin.add')}}</button>
                                        <a href="{{ url()->previous() }}" type="reset" class="btn btn-outline-warning mr-1 mb-1">{{__('admin.back')}}</a>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>
@endsection

@section('js')
<script src="{{asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')}}"></script>
<script src="{{asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')}}"></script>
<script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
<script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>

{{-- show selected image script --}}
@include('admin.shared.addImage')
{{-- show selected image script --}}

{{-- submit add form script --}}
@include('admin.shared.submitAddForm')
{{-- submit add form script --}}

{{-- Region Cities AJAX Script --}}
@include('admin.shared.regionCityDropdown')
{{-- Region Cities AJAX Script --}}
@endsection
