<?php

namespace App\Traits;

trait NotificationMessageTrait
{

    public function getTitle(array $notification_data, $local = 'ar')
    {
        // Handle simple string title (backward compatibility)
        if(isset($notification_data['title']) && is_string($notification_data['title'])){
            return $notification_data['title'];
        }

        // Handle localized title array
        if(isset($notification_data['title'][$local])){
            return $notification_data['title'][$local];
        }

        // Fallback to other language if current language not available
        if(isset($notification_data['title'])){
            $availableLanguages = array_keys($notification_data['title']);
            if(!empty($availableLanguages)){
                return $notification_data['title'][$availableLanguages[0]];
            }
        }

        // Final fallback to translation
        if(isset($notification_data['type'])){
            return trans('notification.title_' . $notification_data['type'], [], $local);
        }

        return 'Notification';
    }

    public function getBody(array $notification_data, $local = 'ar')
    {
        // Handle simple string body (backward compatibility)
        if(isset($notification_data['body']) && is_string($notification_data['body'])){
            return $notification_data['body'];
        }

        if (isset($notification_data['type']) && 'admin_notify' == $notification_data['type']) {
            // Handle localized body array
            if(isset($notification_data['body'][$local])){
                return $notification_data['body'][$local];
            }

            // Fallback to other language if current language not available
            if(isset($notification_data['body']) && is_array($notification_data['body'])){
                $availableLanguages = array_keys($notification_data['body']);
                if(!empty($availableLanguages)){
                    return $notification_data['body'][$availableLanguages[0]];
                }
            }

            return 'Notification message';
        } else {
            return $this->transTypeToBody($notification_data, $local);
        }
    }

 private function transTypeToBody($notification_data, $local)
{
    $transData = [];

    if (isset($notification_data['order_num'])) {
        $transData['order_num'] = $notification_data['order_num'];
    }

    if (isset($notification_data['amount'])) {
        $transData['amount'] = $notification_data['amount'];
    }

    if (isset($notification_data['date'])) {
        $transData['date'] = $notification_data['date'];
    }

    if (isset($notification_data['status'])) {
        $transData['status'] = trans('order.' . $notification_data['status'], [], $local);
    }

    if (!isset($notification_data['type'])) {
        return __('apis.notification_type_not_defined', [], $local);
    }

    return trans('notification.body_' . $notification_data['type'], $transData, $local);
}


}
