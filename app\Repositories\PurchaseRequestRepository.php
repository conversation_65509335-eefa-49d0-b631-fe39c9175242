<?php
namespace App\Repositories;

use App\Models\PurchaseRequest;

class PurchaseRequestRepository
{
 public function getAsBuyer($userId, $filters = [])
{
    return PurchaseRequest::where('buyer_id', $userId)
        ->when(isset($filters['sort_by']), function ($q) use ($filters) {
            switch ($filters['sort_by']) {
                case 'oldest':
                    $q->orderBy('created_at', 'asc');
                    break;

                case 'latest':
                default:
                    $q->orderBy('created_at', 'desc');
                    break;
            }
        })
        ->get();
}


    public function getAsSeller($userId)
    {
        return PurchaseRequest::where('seller_id', $userId)->get();
    }

public function getById($id, $userId)
{
    return PurchaseRequest::with('ratings') 
        ->where(function ($q) use ($userId) {
            $q->where('buyer_id', $userId)
              ->orWhere('seller_id', $userId);
        })
        ->findOrFail($id);
}


    public function create(array $data)
    {
        $data['buyer_id'] = auth()->id();
        return PurchaseRequest::create($data);
    }

    public function update($id, array $data)
    {
        $request = $this->getById($id, auth()->id());
        $request->update($data);
        return $request;
    }

    public function delete($id)
    {
        $request = $this->getById($id, auth()->id());
        return $request->delete();
    }

    public function adminCancelRequest($id)
{
    $request = PurchaseRequest::with('advertisement', 'buyer.wallet')->findOrFail($id);

    if ($request->status !== 'problem') {
        abort(400, 'لا يمكن الإلغاء إلا بعد الإبلاغ عن مشكلة.');
    }

    if ($request->wallet_credit_used > 0 && $request->buyer->wallet) {
        $wallet = $request->buyer->wallet;
        $wallet->balance += $request->wallet_credit_used;
        $wallet->save();
    }

    if ($request->advertisement) {
        $request->advertisement->update(['status' => 'cancelled']);
    }

    $request->update(['status' => 'cancelled']);

    return true;
}

}
