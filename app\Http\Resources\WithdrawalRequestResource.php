<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class WithdrawalRequestResource extends JsonResource
{
 public function toArray($request)
{
    return [
        'id' => $this->id,
        'amount' => $this->amount,
        'bank_name' => $this->bank_name,
        'account_holder' => $this->account_holder,
        'account_number' => $this->account_number,
        'iban' => $this->iban,
        'status' => $this->status,
        'created_at' => $this->created_at->format('Y-m-d H:i:s'),
    ];
}

}
