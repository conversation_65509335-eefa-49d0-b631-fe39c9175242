<div class="position-relative">
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>{{ __('admin.advertisement') }}</th>
                <th>{{ __('admin.client_name') }}</th>
                <th>{{ __('admin.phone') }}</th>
                <th>{{ __('admin.last_message') }}</th>
                <th>{{ __('admin.last_message_date') }}</th>
                <th>{{ __('admin.control') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse($conversations as $conversation)
                @php
                    // آخر رسالة في المحادثة
                    $lastMessage = $conversation->messages()->latest()->first();

                    // استنتاج العميل (الطرف غير الأدمن)
                    $adminId = auth('admin')->id();

                    if ($lastMessage?->sender_id != $adminId) {
                        $client = $lastMessage->sender;
                    } elseif ($lastMessage?->receiver_id != $adminId) {
                        $client = $lastMessage->receiver;
                    } else {
                        $client = null;
                    }
                @endphp

                <tr>
                    <td>{{ $conversation->id }}</td>
                    <td>{{ $conversation->advertisement->name ?? '-' }}</td>
                    <td>{{ $client?->name ?? '-' }}</td>
                    <td>{{ $client?->phone ?? '-' }}</td>
                    <td>
                        @if($lastMessage)
                            {{ \Illuminate\Support\Str::limit($lastMessage->message, 50) }}
                        @else
                            <span class="text-muted">{{ __('admin.no_messages') }}</span>
                        @endif
                    </td>
                    <td>
                        @if($lastMessage)
                            {{ $lastMessage->created_at->format('Y-m-d H:i') }}
                        @else
                            <span class="text-muted">-</span>
                        @endif
                    </td>
                    {{-- <td>
                        <a href="{{ route('admin.conversations.show', $conversation->id) }}" class="btn btn-primary btn-sm">
                            {{ __('admin.show') }}
                        </a>
                    </td> --}}
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        {{ __('admin.there_are_no_matches_matching') }}
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    {{-- pagination --}}
    @if ($conversations->count() > 0 && $conversations instanceof \Illuminate\Pagination\AbstractPaginator)
        <div class="d-flex justify-content-center mt-3">
            {{ $conversations->links() }}
        </div>
    @endif
</div>
