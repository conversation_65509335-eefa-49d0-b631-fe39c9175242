<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TrainerApplicationUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $id = $this->route('id'); // لو هتستخدمه في unique

        return [
      'name' => ['nullable', 'array'],
'name.ar' => ['nullable', 'string', 'min:5'],
'name.en' => ['nullable', 'string', 'min:5'],

           'bio' => ['nullable', 'array'],
'bio.ar' => ['nullable', 'string', 'min:10'],
'bio.en' => ['nullable', 'string', 'min:10'],

'experience' => ['nullable', 'array'],
'experience.ar' => ['nullable', 'string', 'min:10'],
'experience.en' => ['nullable', 'string', 'min:10'],


            'region_id' => ['nullable', 'exists:regions,id'],
            'city_id' => ['nullable', 'exists:cities,id'],

            'training_price' => ['nullable', 'numeric', 'min:0'],

            'contact_phone' => [
                'nullable',
                'string',
                Rule::unique('trainer_applications', 'contact_phone')->ignore($id),
            ],
            'contact_email' => [
                'nullable',
                'email',
                Rule::unique('trainer_applications', 'contact_email')->ignore($id),
            ],
            'contact_whatsapp' => [
                'nullable',
                'string',
                Rule::unique('trainer_applications', 'contact_whatsapp')->ignore($id),
            ],

            'personal_image' => ['nullable', 'file', 'image', 'max:5120'], // 5MB
            'certificate_image' => ['nullable', 'file', 'image', 'max:5120'],

            'works' => ['nullable', 'array', 'max:10'],
            'works.*' => ['image', 'max:5120'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.ar.required_without' => 'الاسم بالعربية مطلوب إذا لم يتم إدخال الاسم بالإنجليزية',
            'name.en.required_without' => 'الاسم بالإنجليزية مطلوب إذا لم يتم إدخال الاسم بالعربية',

            'bio.ar.required_without' => 'النبذة بالعربية مطلوبة إذا لم يتم إدخال النبذة بالإنجليزية',
            'bio.en.required_without' => 'النبذة بالإنجليزية مطلوبة إذا لم يتم إدخال النبذة بالعربية',

            'experience.ar.required_without' => 'الخبرات بالعربية مطلوبة إذا لم يتم إدخال الخبرات بالإنجليزية',
            'experience.en.required_without' => 'الخبرات بالإنجليزية مطلوبة إذا لم يتم إدخال الخبرات بالعربية',

            'personal_image.max' => 'الحد الأقصى لصورة الملف الشخصي هو 5 ميجا.',
            'certificate_image.max' => 'الحد الأقصى لصورة الشهادة هو 5 ميجا.',
            'works.max' => 'يمكنك رفع حتى 10 صور فقط من الأعمال.',
            'works.*.max' => 'كل صورة يجب أن لا تتجاوز 5 ميجا.',
        ];
    }
}
