<?php

namespace App\Http\Requests\Api\Provider;

use App\Http\Requests\Api\BaseApiRequest;
use Illuminate\Foundation\Http\FormRequest;

class UpdateProudctRequest extends BaseApiRequest
{
    public function rules(): array
    {
        return [
            'provider_id' => 'sometimes|exists:providers,id',
            'product_category_id' => 'sometimes|exists:product_categories,id',
            'name'                    => 'sometimes|array',
            'name.ar'                 => 'sometimes|string|max:100',
            'name.en'                 => 'nullable|string|max:100',
            'price' => 'sometimes|numeric|min:0',
            'quantity' => 'required|integer|min:0',
            'description' => 'nullable|string',
            'is_active' => 'sometimes|boolean',
            'images'              => 'sometimes|nullable|array|max:5',
            'images.*'            => 'image',
        ];
    }
}
