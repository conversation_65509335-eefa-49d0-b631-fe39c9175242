<?php

namespace App\Services\Payment;

use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Exception;

class NbkPaymentService
{
    protected $config;

    public function __construct()
    {
        $this->config = config('payment.nbk');
    }

    /**
     * Process NBK payment
     *
     * @param Order $order
     * @param array $data
     * @return array
     */
    public function processPayment(Order $order, array $data)
    {
        try {
            // Mark order as deleted temporarily (legacy behavior)
            $order->deleted_at = '2020-08-17 04:42:47';
            $order->save();

            // Build payment parameters
            $params = $this->buildPaymentParams($order, $data);

            // Generate signature
            $params['signature'] = $this->generateSignature($params);

            // Create payment URL
            $paymentUrl = url('/nbk/' . $order->print);

            Log::info('NBK payment initiated successfully', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_url' => $paymentUrl
            ]);

            return [
                'success' => true,
                'requires_payment_gateway' => true,
                'payment_url' => $paymentUrl,
                'order_id' => $order->id,
                'message' => 'Redirect to NBK payment gateway'
            ];

        } catch (Exception $e) {
            Log::error('NBK payment processing failed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);

            // Restore order if payment failed
            $order->deleted_at = null;
            $order->save();

            return [
                'success' => false,
                'message' => 'NBK payment processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build payment parameters for NBK
     *
     * @param Order $order
     * @param array $data
     * @return array
     */
    private function buildPaymentParams(Order $order, array $data)
    {
        $params = [];
        $iloop = 0;
        $amount = 0;

        // Add order items
        foreach ($order->items as $item) {
            $params['item_' . $iloop . '_name'] = $item->name;
            $params['item_' . $iloop . '_quantity'] = $item->quantity;
            $params['item_' . $iloop . '_sku'] = $item->item_id;
            $params['item_' . $iloop . '_unitPrice'] = $item->price;
            $iloop++;
            $amount += $item->total;
        }

        // Add delivery fee if applicable
        if ($order->delivery_fee > 0) {
            $params['item_' . $iloop . '_name'] = 'Delivery Fee';
            $params['item_' . $iloop . '_quantity'] = 1;
            $params['item_' . $iloop . '_sku'] = 0;
            $params['item_' . $iloop . '_unitPrice'] = $order->delivery_fee;
            $iloop++;
            $amount += $order->delivery_fee;
        }

        // Add booking fee if applicable
        if ($order->booking_fee > 0) {
            $params['item_' . $iloop . '_name'] = 'Booking Fee';
            $params['item_' . $iloop . '_quantity'] = 1;
            $params['item_' . $iloop . '_sku'] = 0;
            $params['item_' . $iloop . '_unitPrice'] = $order->booking_fee;
            $iloop++;
            $amount += $order->booking_fee;
        }

        // Add home service fee if applicable
        if ($order->home_service_fee > 0) {
            $params['item_' . $iloop . '_name'] = 'Home Service Fee';
            $params['item_' . $iloop . '_quantity'] = 1;
            $params['item_' . $iloop . '_sku'] = 0;
            $params['item_' . $iloop . '_unitPrice'] = $order->home_service_fee;
            $iloop++;
            $amount += $order->home_service_fee;
        }

        // Add discount if applicable
        if ($order->discount_amount > 0) {
            $params['item_' . $iloop . '_name'] = 'Discount';
            $params['item_' . $iloop . '_quantity'] = 1;
            $params['item_' . $iloop . '_sku'] = 0;
            $params['item_' . $iloop . '_unitPrice'] = -$order->discount_amount;
            $iloop++;
            $amount -= $order->discount_amount;
        }

        // Add NBK specific parameters
        $params['SECRET_KEY'] = $this->config['secret_key'];
        $params['access_key'] = $this->config['access_key'];
        $params['profile_id'] = $this->config['profile_id'];
        $params['merchant_id'] = $this->config['merchant_id'];
        $params['transaction_uuid'] = uniqid();
        $params['signed_field_names'] = 'access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency';
        $params['unsigned_field_names'] = '';
        $params['reference_number'] = time() * 1000;
        $params['signed_date_time'] = gmdate("Y-m-d\TH:i:s\Z");
        $params['locale'] = app()->getLocale();
        $params['transaction_type'] = 'sale';
        $params['currency'] = 'kwd';
        $params['submit'] = 'submit';
        $params['amount'] = $amount;

        // Customer information
        $phone = $order->user->phone ?? '12345678';
        $params['bill_to_forename'] = $order->user->first_name ?? 'Customer';
        $params['bill_to_surname'] = $order->user->last_name ?? '';
        $params['bill_to_phone'] = $phone;
        $params['bill_to_email'] = $order->user->email ?? '<EMAIL>';
        $params['ship_to_forename'] = $order->user->first_name ?? 'Customer';
        $params['ship_to_surname'] = $order->user->last_name ?? '';
        $params['ship_to_phone'] = $phone;
        $params['ship_to_email'] = $order->user->email ?? '<EMAIL>';

        return $params;
    }

    /**
     * Generate signature for NBK payment
     *
     * @param array $params
     * @return string
     */
    private function generateSignature(array $params)
    {
        $signedFieldNames = explode(",", $params["signed_field_names"]);
        $dataToSign = [];
        
        foreach ($signedFieldNames as $field) {
            $dataToSign[] = $field . "=" . $params[$field];
        }
        
        $dataString = implode(",", $dataToSign);
        return base64_encode(hash_hmac('sha256', $dataString, $params['SECRET_KEY'], true));
    }

    /**
     * Verify NBK payment
     *
     * @param string $paymentId
     * @param int $orderId
     * @return array
     */
    public function verifyPayment($paymentId, $orderId)
    {
        try {
            // For NBK, verification is typically done through the callback
            // This method can be extended if needed for additional verification
            
            Log::info('NBK payment verification', [
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ]);

            return [
                'success' => true,
                'verified' => true,
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ];

        } catch (Exception $e) {
            Log::error('NBK payment verification failed', [
                'payment_id' => $paymentId,
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed'
            ];
        }
    }

    /**
     * Process NBK callback
     *
     * @param array $request
     * @return array
     */
    public function processCallback(array $request)
    {
        try {
            if ($request['decision'] === 'ACCEPT') {
                $orderPrint = $request['req_transaction_uuid'];
                
                Log::info('NBK payment callback - success', [
                    'order_print' => $orderPrint,
                    'decision' => $request['decision']
                ]);

                return [
                    'success' => true,
                    'order_print' => $orderPrint,
                    'status' => 'accepted'
                ];
            } else {
                Log::warning('NBK payment callback - failed', [
                    'decision' => $request['decision'],
                    'request' => $request
                ]);

                return [
                    'success' => false,
                    'message' => 'Payment was not accepted'
                ];
            }

        } catch (Exception $e) {
            Log::error('NBK callback processing failed', [
                'error' => $e->getMessage(),
                'request' => $request
            ]);

            return [
                'success' => false,
                'message' => 'Callback processing failed'
            ];
        }
    }
}
