<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource {
  private $token               = '';

  public function setToken($value) {
    $this->token = $value;
    return $this;
  }

  public function toArray($request) {
    return [
      'id'                  => $this->id,
      'image'               => $this->image,
      'name'                => $this->name,
      'email'               => $this->email,
      // 'country_code'        => $this->country_code,
      // 'type'=> $this->type,
      'phone'               => $this->phone,
      'full_phone'          => $this->full_phone,
      'gender'              => $this->gender,
      // 'lang'                => $this->lang,
      // 'is_notify'           => $this->is_notify,
      'city' => [
        'id'=>$this?->city_id,
        'name' => $this->city?->name
        ],
          'region' => [
        'id'=>$this?->region_id,
        'name' => $this->region?->name
        ],

      'token'               => $this->token,
    ];
  }
}
