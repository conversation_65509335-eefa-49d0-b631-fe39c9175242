<?php

namespace App\Services\Payment;

use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Exception;

class KnetPaymentService
{
    protected $config;

    public function __construct()
    {
        $this->config = config('payment.knet');
    }

    /**
     * Process KNET payment
     *
     * @param Order $order
     * @param array $data
     * @return array
     */
    public function processPayment(Order $order, array $data)
    {
        try {
            // Mark order as deleted temporarily (legacy behavior)
            $order->deleted_at = '2020-08-17 04:42:47';
            $order->save();

            // Build invoice items
            $products = $this->buildInvoiceItems($order);

            // Prepare MyFatoorah request
            $postData = [
                "InvoiceValue" => $order->total,
                "CustomerName" => $order->user->name ?? 'Customer',
                "NotificationOption" => "LNK",
                "CustomerMobileCountryCode" => "966",
                "CustomerMobile" => $this->cleanPhoneNumber($order->user->phone ?? '12345678'),
                "CustomerEmail" => $order->user->email ?? '<EMAIL>',
                "CustomerReference" => (string) time(),
                "DisplayCurrencyIso" => "SAR",
                "CurrencyIso" => "SAR",
                "InvoiceItems" => $products,
                "CallBackUrl" => url('knetsuc') . '?order_id=' . $order->id,
                "ErrorUrl" => url('knetfail') . '?order_id=' . $order->id,
                "Language" => "ar",
                "ExpiryDate" => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day'))
            ];

            // Make API call to MyFatoorah
            $response = $this->makeApiCall($postData);

            if ($response && isset($response['Data']['InvoiceURL'])) {
                Log::info('KNET payment initiated successfully', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'invoice_url' => $response['Data']['InvoiceURL']
                ]);

                return [
                    'success' => true,
                    'requires_payment_gateway' => true,
                    'payment_url' => $response['Data']['InvoiceURL'],
                    'invoice_id' => $response['Data']['InvoiceId'] ?? null,
                    'order_id' => $order->id,
                    'message' => 'Redirect to KNET payment gateway'
                ];
            } else {
                throw new Exception('Invalid response from KNET payment gateway');
            }

        } catch (Exception $e) {
            Log::error('KNET payment processing failed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);

            // Restore order if payment failed
            $order->deleted_at = null;
            $order->save();

            return [
                'success' => false,
                'message' => 'KNET payment processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build invoice items from order
     *
     * @param Order $order
     * @return array
     */
    private function buildInvoiceItems(Order $order)
    {
        $products = [];

        // Add order items
        foreach ($order->items as $item) {
            $products[] = [
                "ItemName" => $item->name,
                "Quantity" => $item->quantity,
                "UnitPrice" => $item->price
            ];
        }

        // Add delivery fee if applicable
        if ($order->delivery_fee > 0) {
            $products[] = [
                "ItemName" => "Delivery Fee",
                "Quantity" => 1,
                "UnitPrice" => $order->delivery_fee
            ];
        }

        // Add booking fee if applicable
        if ($order->booking_fee > 0) {
            $products[] = [
                "ItemName" => "Booking Fee",
                "Quantity" => 1,
                "UnitPrice" => $order->booking_fee
            ];
        }

        // Add home service fee if applicable
        if ($order->home_service_fee > 0) {
            $products[] = [
                "ItemName" => "Home Service Fee",
                "Quantity" => 1,
                "UnitPrice" => $order->home_service_fee
            ];
        }

        // Add discount if applicable
        if ($order->discount_amount > 0) {
            $products[] = [
                "ItemName" => "Discount",
                "Quantity" => 1,
                "UnitPrice" => -$order->discount_amount
            ];
        }

        return $products;
    }

    /**
     * Make API call to MyFatoorah for KNET
     *
     * @param array $postData
     * @return array|null
     */
    private function makeApiCall(array $postData)
    {
        $accessToken = $this->config['access_token'];
        $apiUrl = $this->config['test_mode'] 
            ? "https://apitest.myfatoorah.com/v2/SendPayment"
            : "https://api-sa.myfatoorah.com/v2/SendPayment";

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $apiUrl);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($curl, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer $accessToken",
            "Content-Type: application/json"
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpCode !== 200) {
            throw new Exception("API call failed with HTTP code: $httpCode");
        }

        return json_decode($response, true);
    }

    /**
     * Clean phone number for API
     *
     * @param string $phone
     * @return string
     */
    private function cleanPhoneNumber($phone)
    {
        // Remove any non-numeric characters and leading zeros
        $cleaned = preg_replace('/[^0-9]/', '', $phone);
        return ltrim($cleaned, '0');
    }

    /**
     * Verify KNET payment
     *
     * @param string $paymentId
     * @param int $orderId
     * @return array
     */
    public function verifyPayment($paymentId, $orderId)
    {
        try {
            // For KNET, verification is typically done through the callback
            // This method can be extended if needed for additional verification
            
            Log::info('KNET payment verification', [
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ]);

            return [
                'success' => true,
                'verified' => true,
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ];

        } catch (Exception $e) {
            Log::error('KNET payment verification failed', [
                'payment_id' => $paymentId,
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed'
            ];
        }
    }
}
