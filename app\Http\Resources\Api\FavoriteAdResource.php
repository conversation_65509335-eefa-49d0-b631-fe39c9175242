<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class FavoriteAdResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id'               => $this->id,
            'user_id'          => $this->user_id,
            'name'             => $this->name,
            'description'      => $this->description,
            'price'            => $this->price,
            'main_category_id' => $this->main_category_id,
            'sub_category_id'  => $this->sub_category_id,
            'gender_target'    => $this->gender_target,
            'region_id'        => $this->region_id,
            'city_id'          => $this->city_id,
            'status'           => $this->status,
            'whatsapp_contact' => $this->whatsapp_contact,
            'is_main'          => $this->is_main,
            'created_at'       => $this->created_at,
            'updated_at'       => $this->updated_at,
        ];
    }
}
