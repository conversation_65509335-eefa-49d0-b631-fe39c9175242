@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
@endsection

@section('content')
<div class="content-wrapper">
    

    <div class="content-body">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="card-title">{{ __('admin.enrollment_details') }}</h4>
                        <div>
                            <a href="{{ route('admin.course_enrollments.pdf', $enrollment->id) }}" class="btn btn-success">
                                <i class="la la-download"></i> {{ __('admin.download_pdf') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{{ __('admin.enrollment_id') }}:</strong></td>
                                            <td>{{ $enrollment->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.enrollment_datetime') }}:</strong></td>
                                            <td>{{ $enrollment->enrolled_at ? $enrollment->enrolled_at->format('Y-m-d H:i:s') : '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.course_name') }}:</strong></td>
                                            <td>{{ $enrollment->course->name ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.course') }} ID:</strong></td>
                                            <td>{{ $enrollment->course_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.course_provider') }}:</strong></td>
                                            <td>{{ $enrollment->course->instructor_name ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.client_name') }}:</strong></td>
                                            <td>{{ $enrollment->user->name ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.mobile_number') }}:</strong></td>
                                            <td>{{ $enrollment->user->phone ?? '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>{{ __('admin.amount_paid') }}:</strong></td>
                                            <td>{{ number_format($enrollment->amount_paid, 2) }} {{ __('admin.riyal') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.payment_method') }}:</strong></td>
                                            <td>
                                                @switch($enrollment->payment_method)
                                                    @case('wallet')
                                                        <span class="badge badge-info">محفظة</span>
                                                        @break
                                                    @case('bank_transfer')
                                                        <span class="badge badge-warning">تحويل بنكي</span>
                                                        @break
                                                    @case('credit_card')
                                                        <span class="badge badge-primary">بطاقة ائتمان</span>
                                                        @break
                                                    @case('mada')
                                                        <span class="badge badge-success">مدى</span>
                                                        @break
                                                    @case('apple_pay')
                                                        <span class="badge badge-dark">Apple Pay</span>
                                                        @break
                                                    @default
                                                        <span class="badge badge-secondary">{{ $enrollment->payment_method }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ __('admin.payment_reference') }}:</strong></td>
                                            <td>{{ $enrollment->payment_reference ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>حالة الدفع:</strong></td>
                                            <td>
                                                @switch($enrollment->payment_status)
                                                    @case('pending')
                                                        <span class="badge badge-warning">في الانتظار</span>
                                                        @break
                                                    @case('paid')
                                                        <span class="badge badge-success">مدفوع</span>
                                                        @break
                                                    @case('failed')
                                                        <span class="badge badge-danger">فشل</span>
                                                        @break
                                                    @case('refunded')
                                                        <span class="badge badge-info">مسترد</span>
                                                        @break
                                                    @default
                                                        <span class="badge badge-secondary">{{ $enrollment->payment_status }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>حالة الاشتراك:</strong></td>
                                            <td>
                                                @switch($enrollment->status)
                                                    @case('pending_payment')
                                                        <span class="badge badge-warning">في انتظار الدفع</span>
                                                        @break
                                                    @case('active')
                                                        <span class="badge badge-success">نشط</span>
                                                        @break
                                                    @case('suspended')
                                                        <span class="badge badge-danger">معلق</span>
                                                        @break
                                                    @case('completed')
                                                        <span class="badge badge-info">مكتمل</span>
                                                        @break
                                                    @case('cancelled')
                                                        <span class="badge badge-dark">ملغي</span>
                                                        @break
                                                    @default
                                                        <span class="badge badge-secondary">{{ $enrollment->status }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>نسبة التقدم:</strong></td>
                                            <td>{{ number_format($enrollment->progress_percentage, 2) }}%</td>
                                        </tr>
                                        @if($enrollment->completed_at)
                                        <tr>
                                            <td><strong>تاريخ الإكمال:</strong></td>
                                            <td>{{ $enrollment->completed_at->format('Y-m-d H:i:s') }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="btn-group">
                                        <a href="{{ route('admin.course_enrollments.index') }}" class="btn btn-outline-secondary">
                                            <i class="la la-arrow-left"></i> {{ __('admin.back') }}
                                        </a>
                                        {{-- Future: Download Invoice Button --}}
                                        {{-- <a href="#" class="btn btn-outline-success">
                                            <i class="la la-download"></i> {{ __('admin.download_invoice') }}
                                        </a> --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
@endsection
