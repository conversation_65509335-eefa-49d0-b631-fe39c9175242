<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Order items for each order

        DB::table('order_items')->insert([
            // Order 1 items
            [
                'order_id' => 1,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 1,
                'name' => 'Hair Cut & Style',
                'quantity' => 1,
                'price' => 25.00,
                'total' => 25.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-15 10:00:00',
                    'location' => 'home',
                    'provider_id' => 1,
                    'duration' => 60
                ]),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'order_id' => 1,
                'item_type' => 'App\\Models\\Product',
                'item_id' => 1,
                'name' => 'Hair Styling Product',
                'quantity' => 2,
                'price' => 10.00,
                'total' => 20.00,
                'options' => json_encode([
                    'delivery_type' => 'normal',
                    'provider_id' => 1
                ]),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],

            // Order 2 items
            [
                'order_id' => 2,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 3,
                'name' => 'Manicure',
                'quantity' => 1,
                'price' => 15.00,
                'total' => 15.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-16 14:00:00',
                    'location' => 'salon',
                    'provider_id' => 2,
                    'duration' => 45
                ]),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'order_id' => 2,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 4,
                'name' => 'Pedicure',
                'quantity' => 1,
                'price' => 20.00,
                'total' => 20.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-16 15:00:00',
                    'location' => 'salon',
                    'provider_id' => 2,
                    'duration' => 60
                ]),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],

            // Order 3 items
            [
                'order_id' => 3,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 5,
                'name' => 'Facial Treatment',
                'quantity' => 1,
                'price' => 35.00,
                'total' => 35.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-17 11:00:00',
                    'location' => 'salon',
                    'provider_id' => 3,
                    'duration' => 90
                ]),
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],
            [
                'order_id' => 3,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 6,
                'name' => 'Relaxing Massage',
                'quantity' => 1,
                'price' => 50.00,
                'total' => 50.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-17 13:00:00',
                    'location' => 'salon',
                    'provider_id' => 3,
                    'duration' => 75
                ]),
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
            ],

            // Order 4 items
            [
                'order_id' => 4,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 1,
                'name' => 'Hair Cut & Style',
                'quantity' => 1,
                'price' => 25.00,
                'total' => 25.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-18 09:00:00',
                    'location' => 'salon',
                    'provider_id' => 1,
                    'duration' => 60
                ]),
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],

            // Order 5 items (cancelled)
            [
                'order_id' => 5,
                'item_type' => 'App\\Models\\Service',
                'item_id' => 7,
                'name' => 'Bridal Makeup',
                'quantity' => 1,
                'price' => 60.00,
                'total' => 60.00,
                'options' => json_encode([
                    'booking_time' => '2024-01-19 08:00:00',
                    'location' => 'salon',
                    'provider_id' => 4,
                    'duration' => 120
                ]),
                'created_at' => now()->subHours(8),
                'updated_at' => now()->subHours(8),
            ],
        ]);

        $this->command->info('Order items seeded successfully!');
    }
}
