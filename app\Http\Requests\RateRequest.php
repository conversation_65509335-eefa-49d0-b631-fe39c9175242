<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
        'trainer_id' => ['nullable', 'exists:users,id'],
        'purchase_request_id' => ['nullable', 'exists:purchase_requests,id'],
        'stars' => ['required', 'integer', 'between:1,5'],
        'status' => ['nullable', 'in:pending,approved,rejected'],
        'comment' => ['nullable', 'string', 'max:1000'],
        ];
    }
}
