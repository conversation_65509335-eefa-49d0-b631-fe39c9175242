@extends('admin.layout.master')

{{-- Extra CSS --}}
@section('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">
@endsection

{{-- Content --}}
@section('content')
<form method="POST" action="{{ route('admin.ads.store') }}" class="store form-horizontal" novalidate enctype="multipart/form-data">
    @csrf
    <input type="hidden" name="type" value="client">

    <section id="multiple-column-form">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-content">
                        <div class="card-body">
                            <div class="form-body">
                                <div class="row">

                                    {{-- Language Tabs --}}
                                    <div class="col-12">
                                        <ul class="nav nav-tabs mb-3">
                                            @foreach (languages() as $lang)
                                                <li class="nav-item">
                                                    <a class="nav-link @if($loop->first) active @endif" data-toggle="pill" href="#first_{{ $lang }}" aria-expanded="true">
                                                        {{ __('admin.data') }} {{ $lang }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>

                                    {{-- Language Tabs Content --}}
                                    <div class="tab-content col-12">
                                        @foreach (languages() as $lang)
                                            <div role="tabpanel" class="tab-pane fade @if($loop->first) show active @endif" id="first_{{ $lang }}">
                                                <div class="form-group">
                                                    <label>{{ __('admin.name') }} {{ $lang }}</label>
                                                    <input type="text" name="name[{{ $lang }}]" class="form-control"
                                                        placeholder="{{ __('admin.write') . __('admin.name') }} {{ $lang }}"
                                                        @if($loop->first) required data-validation-required-message="{{ __('admin.this_field_is_required') }}" @endif>
                                                </div>

                                                <div class="form-group">
                                                    <label>{{ __('admin.description') }} {{ $lang }}</label>
                                                    <textarea name="description[{{ $lang }}]" class="form-control"
                                                        placeholder="{{ __('admin.write') . ' ' . __('admin.description') }} {{ $lang }}"
                                                        @if($loop->first) required data-validation-required-message="{{ __('admin.this_field_is_required') }}" @endif></textarea>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>

                                    {{-- Main Image --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.main_image') }} <span class="text-danger">*</span></label>
                                            <div class="custom-file">
                                                <input type="file"
                                                    name="main_image"
                                                    class="custom-file-input @error('main_image') is-invalid @enderror"
                                                    id="mainImageInput"
                                                    accept="image/*">
                                                <label class="custom-file-label" for="mainImageInput">{{ __('admin.choose_image') }}</label>
                                            </div>
                                            @error('main_image')
                                                <span class="text-danger d-block mt-1">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>



                                    {{-- Additional Images --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.additional_images') }}</label>
                                            <div class="custom-file">
                                                <input type="file" name="images[]" class="custom-file-input" id="images" accept="image/*" multiple>
                                                <label class="custom-file-label" for="images">{{ __('admin.choose_images') }}</label>
                                            </div>
                                            <div class="row mt-2" id="additionalImagesPreview"></div>
                                        </div>
                                    </div>

                                    {{-- User --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.user') }}</label>
                                            <select name="user_id" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                <option value="">{{ __('admin.choose_user') }}</option>
                                                @foreach($users as $user)
                                                    <option value="{{ $user->id }}">{{ $user->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Price --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.price') }}</label>
                                            <input type="number" step="0.01" name="price" class="form-control" placeholder="0.00">
                                        </div>
                                    </div>

                                    {{-- Main Category --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.main_category') }}</label>
                                            <select name="main_category_id" class="form-control" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                <option value="">{{ __('admin.choose') }}</option>
                                                @foreach($Categories as $category)
                                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Sub Category --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.sub_category') }}</label>
                                            <select name="sub_category_id" class="form-control" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                <option value="">{{ __('admin.choose') }}</option>
                                                @foreach($Categories as $category)
                                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    {{-- WhatsApp Contact --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.whatsapp_contact') }}</label>
                                            <input type="text" name="whatsapp_contact" class="form-control" placeholder="{{ __('admin.enter_whatsapp_number') }}" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                        </div>
                                    </div>

                                    {{-- City --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.city') }}</label>
                                            <select name="city_id" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                <option value="">{{ __('admin.select_city') }}</option>
                                                @foreach($cities as $city)
                                                    <option value="{{ $city->id }}">{{ $city->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Region --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.region') }}</label>
                                            <select name="region_id" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                <option value="">{{ __('admin.select_region') }}</option>
                                                @foreach($regions as $region)
                                                    <option value="{{ $region->id }}">{{ $region->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Gender --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.gender') }}</label>
                                            <select name="gender" class="form-control select2">
                                                <option value="">{{ __('admin.choose_gender') }}</option>
                                                <option value="male">{{ __('admin.male') }}</option>
                                                <option value="female">{{ __('admin.female') }}</option>
                                                <option value="both">{{ __('admin.both') }}</option>
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Active --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.active') }}</label>
                                            <select name="active" class="form-control select2">
                                                <option value="1">{{ __('admin.active') }}</option>
                                                <option value="0">{{ __('admin.inactive') }}</option>
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Blocked --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label>{{ __('admin.Validity') }}</label>
                                            <select name="is_blocked" class="form-control select2" required data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                                <option value="">{{ __('admin.Select_the_blocking_status') }}</option>
                                                <option value="1">{{ __('admin.Prohibited') }}</option>
                                                <option value="0">{{ __('admin.Unspoken') }}</option>
                                            </select>
                                        </div>
                                    </div>

                                    {{-- Buttons --}}
                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{ __('admin.add') }}</button>
                                        <a href="{{ url()->previous() }}" class="btn btn-outline-warning mr-1 mb-1">{{ __('admin.back') }}</a>
                                    </div>

                                </div> {{-- End row --}}
                            </div> {{-- End form-body --}}
                        </div> {{-- End card-body --}}
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>
@endsection

{{-- JS Scripts --}}
@section('js')
    <script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>

    {{-- Image Preview Scripts --}}
    @include('admin.shared.addImage')

    {{-- Submit AJAX Form --}}
    @include('admin.shared.submitAddForm')

    {{-- Region Cities Dynamic Dropdown --}}
    @include('admin.shared.regionCityDropdown')
@endsection
