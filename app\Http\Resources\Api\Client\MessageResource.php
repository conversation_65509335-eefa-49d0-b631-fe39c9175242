<?php

namespace App\Http\Resources\Api\Client;

use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'message' => $this->message,
            'type' => $this->message_type,
            'is_read' => $this->is_read,
            'sender' => [
                'id' => $this->sender->id,
                'name' => $this->sender->name,
            ],
            'created_at' => $this->created_at->format('Y-m-d H:i'),
            // 'file_url' => $this->getFirstMediaUrl('chat-attachments'),
        ];
    }
}
