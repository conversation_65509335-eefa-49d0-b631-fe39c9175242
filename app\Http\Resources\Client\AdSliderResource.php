<?php

namespace App\Http\Resources\Client;

use App\Http\Resources\Api\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdSliderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id'               => $this->id,
            'name' => $this->name,
              'user' => $this->user ? [
            'id'       => $this->user->id,
            'name'     => $this->user->name,
        ] : null,
            'description' => $this->description,
            'price'            => $this->price,
            'gender_target'    => $this->gender_target,
             'city' => [
                'id'    => $this->city?->id,
                'name'  => $this->city?->name,
            ],
            'whatsapp_contact' => $this->whatsapp_contact,
            'status'           => $this->status,
            'is_main'          => $this->is_main,

             'main_category' => $this->mainCategory ? [
                'id'   => $this->mainCategory->id,
                'name' => $this->mainCategory->name,
                'image'=> $this->image_url,
            ] : null,

            'sub_category' => $this->subCategory ? [
                'id'   => $this->subCategory->id,
                'name' => $this->subCategory->name,
                'image'=> $this->subCategory->image_url,
            ] : null,
            

         'main_image' => MediaResource::make($this->getFirstMedia('ads_main')) ?: null,
                'images' => MediaResource::collection($this->getMedia('ads')),

'has_favorite' => auth()->check() && $this->favorites->contains('user_id', auth()->id()),
// 'has_contacted' => auth()->check() && $this->conversations->contains('user_id', auth()->id()),


            'created_at'      => $this->created_at?->format('Y-m-d H:i:s'),

        ];
    }
}
