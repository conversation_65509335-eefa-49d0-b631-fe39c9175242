<?php

namespace App\Http\Requests\Admin\products;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $default_lang = config('app.locale');

        return [
            'name'                  => 'required|array',
            "name.{$default_lang}"  => 'required|string|max:191',
            'name.*'                => 'nullable|string|max:191',
            'description'           => 'nullable|string',
            'price'                 => 'required|numeric|min:0',
            'quantity'              => 'required|integer|min:0',
            'image'                 => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'is_active'             => 'nullable|boolean',
            'product_category_id'   => 'required|exists:product_categories,id',
            'provider_id'           => 'required|exists:providers,id',
        ];
    }
}
