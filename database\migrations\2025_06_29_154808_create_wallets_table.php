<?php
use Illuminate\Database\Migrations\Migration;
return new class extends Migration
{
    public function up()
    {
        DB::statement($this->dropView());
        DB::statement($this->createView());
    }
    private function dropView(): string
    {
        return <<<'SQL'
DROP VIEW IF EXISTS `wallets`;
SQL;
    }
    // COALESCE used for condition if sum is null then set the value 0
    private function createView(): string
    {
        return <<<'SQL'
CREATE VIEW `wallets` AS
SELECT user_id ,COALESCE(SUM(amount),0) as balance FROM `transactions` GROUP BY user_id;
SQL;
    }
    public function down()
    {
        DB::statement($this->dropView());
    }
};