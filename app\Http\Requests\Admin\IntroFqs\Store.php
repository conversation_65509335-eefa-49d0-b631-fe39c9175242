<?php

namespace App\Http\Requests\Admin\IntroFqs;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $default_lang = config('app.locale');

        return [
            'title'                     => 'required|array',
            "title.{$default_lang}"     => 'required|string',
            'title.*'                   => 'nullable|string',
            'description'               => 'required|array',
            "description.{$default_lang}" => 'required|string',
            'description.*'             => 'nullable|string',
            'intro_fqs_category_id'     => 'required|exists:intro_fqs_categories,id',
        ];
    }
}
