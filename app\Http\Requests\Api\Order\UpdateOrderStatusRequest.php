<?php

namespace App\Http\Requests\Api\Order;

use App\Enums\OrderStatus;
use App\Http\Requests\Api\BaseApiRequest;

class UpdateOrderStatusRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => [
                'required',
                'string'
            ],
        ];
    }
}
