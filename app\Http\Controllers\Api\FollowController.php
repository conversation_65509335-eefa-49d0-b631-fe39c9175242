<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Client\AdSliderResource;
use App\Models\User;
use Illuminate\Http\Request;

class FollowController extends Controller
{
public function toggle(User $user)
{
    $authUser = auth()->user();

    if ($authUser->id === $user->id) {
        return response()->json(['status' => false, 'message' => __('apis.cannot_follow_self')], 403);
    }

    $isFollowing = $authUser->following()->where('followed_id', $user->id)->exists();

    if ($isFollowing) {
        $authUser->following()->detach($user->id);
        return response()->json(['status' => true, 'message' => __('apis.unfollowed_successfully')]);
    } else {
        $authUser->following()->attach($user->id);
        return response()->json(['status' => true, 'message' => __('apis.followed_successfully')]);
    }
}



public function profile(User $user)
{
    $authUser = auth()->user();

    return response()->json([
        'status' => true,
        'username' => $user->name,
        'is_following' => $authUser ? $authUser->following()->where('followed_id', $user->id)->exists() : false,
        'ads' => AdSliderResource::collection(
            $user->advertisements()->latest()->get()
        ),
    ]);
}


}
