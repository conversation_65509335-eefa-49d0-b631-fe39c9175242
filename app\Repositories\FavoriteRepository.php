<?php
namespace App\Repositories;

use App\Models\User;
use App\Models\Advertisement;

class FavoriteRepository
{
    public function toggleFavorite($userId, $advertisementId)
    {
        $user = User::findOrFail($userId);

        $isFavorited = $user->favorites()->where('advertisement_id', $advertisementId)->exists();

        if ($isFavorited) {
            $user->favorites()->detach($advertisementId);
            return [
                'message' => 'تمت الإزالة من المفضلة',
                'status' => false,
                'data' => null,
            ];
        } else {
            $user->favorites()->attach($advertisementId);
            $advertisement = Advertisement::find($advertisementId);
            return [
                'message' => 'تمت الإضافة إلى المفضلة',
                'status' => true,
                'data' => $advertisement,
            ];
        }
    }

    public function getUserFavorites($userId)
    {
        $user = User::findOrFail($userId);
        return $user->favorites()->latest()->get();
    }
}
