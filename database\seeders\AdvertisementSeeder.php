<?php

namespace Database\Seeders;

use App\Models\Advertisement;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Faker\Factory as FakerFactory;
use Faker\Provider\Color;

class AdvertisementSeeder extends Seeder
{
    public function run(): void
    {
        $faker = FakerFactory::create();
        $faker->addProvider(new Color($faker));

        $numberOfAds = 10;

        // Get all active categories
        $mainCategories = Category::whereNull('parent_id')->pluck('id')->toArray();
        $subCategories  = Category::whereNotNull('parent_id')->pluck('id')->toArray();

        for ($i = 0; $i < $numberOfAds; $i++) {
            Advertisement::create([
                'user_id' => 1,
                 'name' => $faker->sentence(3), 
                 'description' => $faker->paragraph,
                'price' => $faker->randomFloat(2, 100, 1000),
                'main_category_id' => $faker->randomElement($mainCategories),
                'sub_category_id' => $faker->randomElement($subCategories),
                'gender_target' => $faker->randomElement(['male', 'female', 'both']),
                'region_id' => 1,
                'city_id' => 2,
                'status' => $faker->randomElement(['under_review', 'active', 'rejected']),
                'whatsapp_contact' => '01' . $faker->randomNumber(9, true),
                'is_main' => $faker->boolean,
            ]);
        }
    }
}
