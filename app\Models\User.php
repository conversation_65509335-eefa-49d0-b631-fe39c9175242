<?php

namespace App\Models;

use App\Http\Resources\Api\UserResource;
use App\Traits\SmsTrait;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Jobs\SendEmailJob;
use App\Jobs\SendSms;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property mixed country_code
 * @property mixed phone
 */
class User extends Authenticatable implements HasMedia
{
    use Notifiable, HasApiTokens, SmsTrait, SoftDeletes, HasFactory, InteractsWithMedia;

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'is_notify'   => 'boolean',
    ];

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'password',
        'status',
        'is_notify',
        'code',
        'code_expire',
        'city_id',
        'region_id',
        'gender',
        'is_active'

    ];


 public function advertisements()
    {
        return $this->hasMany(Advertisement::class, 'user_id');
    }

    public function purchaseRequestsAsBuyer()
    {
        return $this->hasMany(PurchaseRequest::class, 'buyer_id');
    }

    public function purchaseRequestsAsSeller()
    {
        return $this->hasMany(PurchaseRequest::class, 'seller_id');
    }

    public function ratings()
{
    return $this->hasMany(Rating::class, 'client_id')->with('rater');
}


// من يتابعهم المستخدم
public function following()
{
    return $this->belongsToMany(User::class, 'follows', 'follower_id', 'followed_id');
}

// من يتابع المستخدم
public function followers()
{
    return $this->belongsToMany(User::class, 'follows', 'followed_id', 'follower_id');
}

// علاقة الإعلانات
// public function ads()
// {
//     return $this->hasMany(Adv::class);
// }

//region



    public function withdrawalRequests()
    {
        return $this->hasMany(WithdrawalRequest::class);
    }

    public function conversations()
    {
        return $this->belongsToMany(Conversation::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }


    public function trainerApplications()
    {
        return $this->hasMany(TrainerApplication::class);
    }

    public function ratingsGiven()
    {
        return $this->hasMany(Rating::class, 'rater_id');
    }

    public function ratingsReceived()
    {
        return $this->hasMany(Rating::class, 'ratee_id');
    }

    public function rooms()
    {
        return $this->morphMany(RoomMember::class, 'memberable');
    }

    public function ownRooms()
    {
        return $this->morphMany(Room::class, 'createable');
    }

    public function joinedRooms()
    {
        return $this->morphMany(RoomMember::class, 'memberable')
            ->with('room')
            ->get()
            ->sortByDesc('room.last_message_id')
            ->pluck('room');
    }

    // Define media collections
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('profile')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));

        $this->addMediaCollection('id_image')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));

        $this->addMediaCollection('license_image')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/default.png'))
            ->useFallbackPath(public_path('storage/images/default.png'));
    }

    // Define media conversions
    public function registerMediaConversions($media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(100)
            ->height(100)
            ->nonQueued(); // Process immediately
    }

     public function wallet()
    {
        return $this->hasOne(Wallet::class, 'user_id');
    }

//     public function favorites()
// {
//     return $this->belongsToMany(Advertisement::class, 'favorites')
//                 ->withTimestamps()
//                 ->withPivot('created_at');
// }

    public function scopeSearch($query, $searchArray = [])
    {
        $query->where(function ($query) use ($searchArray) {
            if ($searchArray) {
                foreach ($searchArray as $key => $value) {
                    if (str_contains($key, '_id')) {
                        if (null != $value) {
                            $query->Where($key, $value);
                        }
                    } elseif ('order' == $key) {
                    } elseif ('created_at_min' == $key) {
                        if (null != $value) {
                            $query->WhereDate('created_at', '>=', $value);
                        }
                    } elseif ('created_at_max' == $key) {
                        if (null != $value) {
                            $query->WhereDate('created_at', '<=', $value);
                        }
                    } else {
                        if (null != $value) {
                            $query->Where($key, 'like', '%' . $value . '%');
                        }
                    }
                }
            }
        });
        return $query->orderBy('created_at', request()->searchArray && request()->searchArray['order'] ? request()->searchArray['order'] : 'DESC');
    }

    public function setPhoneAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes['phone'] = fixPhone($value);
        }
    }

    public function setCountryCodeAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes['country_code'] = fixPhone($value);
        }
    }

    public function getFullPhoneAttribute()
    {
        return $this->attributes['country_code'] . $this->attributes['phone'];
    }

    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('profile') ?: asset('storage/images/default.png');
    }

    public function getImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('profile') ?: asset('storage/images/default.png');
    }

    public function getIdImageAttribute()
    {
        return $this->getFirstMediaUrl('id_image') ?: asset('storage/images/default.png');
    }

    public function getLicenseImageAttribute()
    {
        return $this->getFirstMediaUrl('license_image') ?: asset('storage/images/default.png');
    }

    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = bcrypt($value);
        }
    }

    public function replays()
    {
        return $this->morphMany(ComplaintReplay::class, 'replayer');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable')->orderBy('created_at', 'desc');
    }

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable')->latest();
    }

    /**
     * Get transactions where user is the payer (user_id)
     */
    public function payments()
    {
        return $this->hasMany(Transaction::class, 'user_id')->latest();
    }

    public function settlements()
    {
        return $this->morphMany(Settlement::class, 'transactionable')->latest();
    }

    public function markAsActive()
    {
        $this->update(['code' => null, 'code_expire' => null, 'is_active' => true]);
        return $this;
    }

    public function sendVerificationCode()
    {
        $this->update([
            'code'        => $this->activationCode(),
            'code_expire' => Carbon::now()->addMinute(),
        ]);

        $this->sendCodeAtSms($this->code);
        // $this->sendEmail($this->code);

        return ['user' => new UserResource($this->refresh())];
    }

    private function activationCode()
    {
        return 12345;
        // return mt_rand(1111, 9999);
    }

    public function sendCodeAtSms($code, $full_phone = null){
        $msg = trans('apis.activeCode');
        dispatch(new SendSms($full_phone ?? $this->full_phone, $msg . $code));
    }

    public function sendEmail($code, $full_phone = null){
        $msg = __('apis.activeCode');
        $data = ['title' => __('admin.reset_password'), 'message' => $msg.$code];
        dispatch(new SendEmailJob($this->email, $data));
    }

    public function logout()
    {
        $this->tokens()->delete();
        if (request()->device_id) {
            $this->devices()->where(['device_id' => request()->device_id])->delete();
        }
        return true;
    }

    public function devices()
    {
        return $this->morphMany(Device::class, 'morph');
    }

    public function login()
    {
        $this->updateUserDevice();
        $this->updateUserLang();
        $token = $this->createToken('bearer-token')->plainTextToken;
        return UserResource::make($this)->setToken($token);
    }

    public function updateUserLang()
    {
        if (request()->header('Lang') != null
            && in_array(request()->header('Lang'), languages())) {
            $this->update(['lang' => request()->header('Lang')]);
        }
    }

    public function updateUserDevice()
    {
        if (request()->device_id) {
            $this->devices()->updateOrCreate([
                'device_id' => request()->device_id,
            ], [
                'device_type' => request()->device_type,
            ]);
        }
    }

    public function city()
    {
        return $this->belongsTo(City::class, 'city_id', 'id');
    }

    public function region()
    {
        return $this->belongsTo(Region::class, 'region_id', 'id');
    }

    public function cart()
    {
        return $this->hasOne(Cart::class);
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    // public function provider()
    // {
    //     return $this->hasOne(Provider::class );
    // }

    /**
     * Get user's course enrollments
     */
    public function courseEnrollments()
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get user's enrolled courses
     */
    public function enrolledCourses()
    {
        return $this->belongsToMany(Course::class, 'course_enrollments')
            ->withPivot(['enrolled_at', 'status', 'progress_percentage', 'completed_at'])
            ->withTimestamps();
    }

    public function userUpdates()
    {
        return $this->hasMany(UserUpdate::class);
    }

    public function phoneUpdates()
    {
        return $this->hasMany(UserUpdate::class)->where('type', 'phone');
    }

    /**
     * Get loyalty points balance in SAR
     */
    public function getLoyaltyPointsValueAttribute()
    {
        $appInfo = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        $settings = \App\Services\SettingService::appInformations($appInfo);
        $redeemRate = $settings['loyalty_points_redeem_rate'] ?? 1;
        return ($this->loyalty_points ?? 0) * $redeemRate;
    }

    /**
     * Use loyalty points
     */
    public function useLoyaltyPoints($points)
    {
        if ($this->loyalty_points >= $points) {
            $this->decrement('loyalty_points', $points);
            return true;
        }
        return false;
    }

    /**
     * Add loyalty points
     */
    public function addLoyaltyPoints($points)
    {
        $this->increment('loyalty_points', $points);
        return $this;
    }

    /**
     * Calculate loyalty points earned from amount paid
     * Points are only earned on actual payment (not on points used)
     */
    public function calculateLoyaltyPointsEarned($amountPaid)
    {
        $appInfo = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        $settings = \App\Services\SettingService::appInformations($appInfo);

        // Check if loyalty points are enabled
        if (!$settings['loyalty_points_enabled']) {
            return 0;
        }

        $earnRate = $settings['loyalty_points_earn_rate'] ?? 1;
        return floor($amountPaid * $earnRate);
    }

    /**
     * Get maximum points that can be redeemed for a given cart total
     */
    public function getMaxRedeemablePoints($cartTotal)
    {
        $appInfo = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        $settings = \App\Services\SettingService::appInformations($appInfo);

        if (!$settings['loyalty_points_enabled']) {
            return 0;
        }

        $maxPercentage = $settings['loyalty_points_max_redeem_percentage'] ?? 50;
        $redeemRate = $settings['loyalty_points_redeem_rate'] ?? 1;
        $minRedeem = $settings['loyalty_points_min_redeem'] ?? 10;

        $maxValue = ($cartTotal * $maxPercentage) / 100;
        $maxPoints = floor($maxValue / $redeemRate);

        // User can't redeem more than they have
        $maxPoints = min($maxPoints, $this->loyalty_points);

        // Must meet minimum requirement
        return $maxPoints >= $minRedeem ? $maxPoints : 0;
    }

    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }
    // User.php
public function favorites()
{
    return $this->belongsToMany(Advertisement::class, 'favorites', 'user_id', 'advertisement_id');
}



    /**
     * Get all favorites for this user
     */
    // public function favorites()
    // {
    //     return $this->hasMany(Favorite::class);
    // }
}
