<?php

namespace App\Http\Requests\Admin\ads;

use Illuminate\Foundation\Http\FormRequest;

class Update extends FormRequest
{
    public function authorize(): bool
    {
        return true; 
    }

    public function rules(): array
    {
        return [
            'name'         => 'nullable|array',
            'name.*'       => 'nullable|string|max:255',
            'description'  => 'nullable|array',
            'description.*'=> 'nullable|string',

            'price'             => 'nullable|numeric|min:0',
            'main_category_id'  => 'nullable|exists:categories,id',
            'sub_category_id'   => 'nullable|exists:categories,id',
            'city_id'           => 'nullable|exists:cities,id',
            'region_id'         => 'nullable|exists:regions,id',
            'user_id'           => 'nullable|exists:users,id',
            'status'            => 'nullable|in:under_review,active,rejected,hidden,cancelled',

'whatsapp_contact' => 'nullable|digits_between:5,20|regex:/^[0-9]+$/',
            'gender'            => 'nullable|in:male,female,both',
            'active'            => 'nullable|boolean',
            'is_blocked'        => 'nullable|boolean',

            'main_image'        => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120', 
            'images'            => 'nullable|array|max:10',
            'images.*'          => 'image|mimes:jpg,jpeg,png,webp|max:5120',
        ];
    }

    public function messages(): array
    {
        return [
            'name.*.required' => 'حقل الاسم مطلوب بكل اللغات',
            'main_category_id.required' => 'يجب اختيار القسم الرئيسي',
            'sub_category_id.required'  => 'يجب اختيار القسم الفرعي',
            'city_id.required'          => 'يجب اختيار المدينة',
            'region_id.required'        => 'يجب اختيار المنطقة',
            'user_id.required'          => 'يجب اختيار المستخدم',
            'main_image.image'          => 'يجب أن تكون الصورة الرئيسية من نوع صورة',
            'images.*.image'            => 'كل صورة إضافية يجب أن تكون صورة صالحة',
        ];
    }
}
