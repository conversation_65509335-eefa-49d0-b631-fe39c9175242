<?php

namespace App\Http\Requests\Admin\ads;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [

        
            'user_id' => 'required|exists:users,id',
            'price' => 'required|numeric|min:0',
            'main_category_id' => 'required|exists:categories,id',
            'sub_category_id' => 'required|exists:categories,id',
            'gender' => 'required|in:male,female,both',
            'region_id' => 'required|exists:regions,id',
            'city_id' => 'required|exists:cities,id',
            'status' => 'in:under_review,active,rejected,hidden,cancelled',
            'whatsapp_contact' => 'nullable|digits_between:5,20|regex:/^[0-9]+$/',
            'is_main' => 'boolean',
            

            // 
        'main_image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
           'images' => 'nullable|array|max:10',
'images.*' => 'image|max:5120',

        ];

        foreach (languages() as $lang) {
            $rules["name.$lang"] = 'required|string|min:6|max:255';
            $rules["description.$lang"] = 'required|string';
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'main_image.required' => __('admin.this_field_is_required'),
        ];
    }
}
