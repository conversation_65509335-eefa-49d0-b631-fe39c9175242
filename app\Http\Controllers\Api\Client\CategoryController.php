<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Settings\CategoryResource;
use App\Http\Resources\Api\Settings\CategoryWithChildrenResource;
use App\Services\CategoryService;

class CategoryController extends Controller
{
    public function __construct(protected CategoryService $categoryService) {}

    /**
     * Get all parent categories (only parents)
     */
    public function index()
    {
        $categories = $this->categoryService->getAllParentCategories();
        return Responder::success(
            CategoryResource::collection($categories)
        );
    }

    /**
     * Get subcategories for specific parent category
     */
   public function show($id)
{
    $category = $this->categoryService->getCategoryWithChildren($id);

    if (!$category) {
        return Responder::error(__('apis.category_not_found'), [], 404);
    }

    // تحويل الأطفال إلى مصفوفة بسيطة
    $childrenList = $category->children->map(function ($child) {
        return [
            'id' => $child->id,
            'name' => $child->name,
            'image' => $child->getFirstMediaUrl('categories') ?: asset('default/category.png'),
        ];
    })->all(); 

    return response()->json([
        'status' => true,
        'data' => $childrenList
    ]);
}
}