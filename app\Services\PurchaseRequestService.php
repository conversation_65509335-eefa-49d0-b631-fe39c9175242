<?php

namespace App\Services;

use App\Models\Advertisement;
use App\Models\PurchaseRequestProblem;
use App\Notifications\PurchaseRequestStatusChanged;
use App\Repositories\AdRepository;
use Illuminate\Validation\ValidationException;
use App\Repositories\PurchaseRequestRepository;
use App\Facades\Responder;

class PurchaseRequestService
{
    protected $repository;
    protected $advertisement;
    protected $transactionService;

    public function __construct(PurchaseRequestRepository $repository, AdRepository $advertisement, TransactionService $transactionService)
    {
        $this->repository = $repository;
        $this->advertisement = $advertisement;
        $this->transactionService = $transactionService;
    }

    public function getAsBuyer()
    {
        return $this->repository->getAsBuyer(auth()->id());
    }

    public function getAsSeller()
    {
        return $this->repository->getAsSeller(auth()->id());
    }

    public function getById($id)
    {
        $request = $this->repository->getById($id, auth()->id());

        if ($request && $request->status === 'problem') {
            $problem = PurchaseRequestProblem::where('purchase_request_id', $id)
                ->where('user_id', auth()->id())
                ->latest()
                ->first();

            if ($problem) {
                $request->problem_details = [
                    'reason'     => $problem->reason,
                    'created_at' => $problem->created_at,
                ];
            }
        }

        return $request;
    }

    public function create(array $data)
    {
        $advertisement = $this->advertisement->findById($data['advertisement_id']);

        if ($advertisement->user_id == auth()->id()) {
            throw ValidationException::withMessages([
                'advertisement_id' => [__('apis.cannot_purchase_own_ad')],
            ]);
        }

        $price = $advertisement->price;
        $walletUsed = $data['wallet_credit_used'] ?? 0;
        $amountDue = max($price - $walletUsed, 0);

        $wallet = auth()->user()->wallet;
        $balance = $wallet?->balance ?? 0;

        if ($walletUsed > $balance) {
            throw ValidationException::withMessages([
                'wallet_credit_used' => [__('apis.insufficient_wallet_balance')],
            ]);
        }

        $settings = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        $productCommission = isset($settings['product_commission']) ? (float)$settings['product_commission'] : 0;
        $platformCommission = $productCommission;
        $amountToSeller = $price - $platformCommission;

        $purchaseData = [
            'advertisement_id'   => $advertisement->id,
            'buyer_id'           => auth()->id(),
            'seller_id'          => $advertisement->user_id,
            'status'             => 'under_review',
            'amount_paid'        => $price,
            'wallet_credit_used' => $walletUsed,
            'payment_method'     => $data['payment_method'] ?? null,
        ];

        $record = $this->repository->create($purchaseData);

        // تحديث حالة الإعلان
        $advertisement->update(['status' => 'awaiting_sale_confirm']);

        if ($amountDue > 1 && empty($data['payment_method'])) {
            return $record;
        }

        $this->transactionService->withdrawOrderTransaction($record, auth()->id(), $walletUsed);
        $this->transactionService->depositOrderTransaction($record, $advertisement->user_id, $walletUsed);

        return $record;
    }

    public function reportProblem($id, $reason)
    {
        $request = $this->getById($id);

        if ($request->buyer_id !== auth()->id()) {
            return Responder::error(__('apis.unauthorized'), 403);
        }

        $alreadyReported = PurchaseRequestProblem::where('purchase_request_id', $id)
            ->where('user_id', auth()->id())
            ->exists();

        if ($alreadyReported) {
            return Responder::error(__('apis.already_reported'), 422);
        }

        PurchaseRequestProblem::create([
            'purchase_request_id' => $id,
            'user_id'             => auth()->id(),
            'reason'              => $reason,
        ]);

        $oldStatus = $request->status;
        $request->update(['status' => 'problem']);
        $request->advertisement?->update(['status' => 'cancelled']);

        $request->seller?->notify(new PurchaseRequestStatusChanged($oldStatus, 'problem', $request->id));

        return Responder::success(null, [
            'message' => __('apis.reported_successfully'),
        ]);
    }

    public function confirmBySeller($id)
    {
        $request = $this->getById($id);

        if ($request->seller_id !== auth()->id()) {
            throw ValidationException::withMessages([
                'id' => [__('apis.unauthorized')],
            ]);
        }

        $oldStatus = $request->status;
        $request->update(['status' => 'waiting_buyer_confirmation']);
        $request->advertisement?->update(['status' => 'awaiting_purchase_confirm']);

        $request->buyer?->notify(new PurchaseRequestStatusChanged($oldStatus, 'waiting_buyer_confirmation', $request->id));
    }

    public function confirmByBuyer($id)
    {
        $request = $this->getById($id);

        if ($request->buyer_id !== auth()->id()) {
            throw ValidationException::withMessages([
                'id' => [__('apis.unauthorized')],
            ]);
        }

        $oldStatus = $request->status;
        $request->update(['status' => 'completed']);
        $request->advertisement?->update(['status' => 'sold']);

        $request->seller?->notify(new PurchaseRequestStatusChanged($oldStatus, 'completed', $request->id));
    }

public function update($id, array $data)
{
$purchase = $this->repository->getById($id, auth()->id());

    // لو المستخدم غير الإعلان
    if (isset($data['advertisement_id'])) {
        $advertisement = $this->advertisement->findById($data['advertisement_id']);

        // تحقق إضافي: منع المستخدم من شراء إعلانه الجديد
        if ($advertisement->user_id == auth()->id()) {
            throw ValidationException::withMessages([
                'advertisement_id' => [__('apis.cannot_purchase_own_ad')],
            ]);
        }

        // تحديث بيانات الإعلان الجديد
        $purchase->advertisement_id = $advertisement->id;
        $purchase->seller_id = $advertisement->user_id;

        // تحديث السعر بناءً على الإعلان الجديد
        $price = $advertisement->price;
        $walletUsed = $data['wallet_credit_used'] ?? 0;
        $amountDue = max($price - $walletUsed, 0);

        $purchase->amount_paid = $price;
        $purchase->wallet_credit_used = $walletUsed;
        $purchase->payment_method = $data['payment_method'] ?? $purchase->payment_method;
    }

    // تحديث الحالة أو أي بيانات أخرى إن وُجدت
    if (isset($data['status'])) {
        $purchase->status = $data['status'];
    }

    $purchase->save();

    return $purchase;
}


    public function delete($id)
    {
        return $this->repository->delete($id);
    }
}
