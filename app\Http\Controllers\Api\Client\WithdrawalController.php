<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreWithdrawalRequest;
use App\Http\Resources\WithdrawalRequestResource;
use App\Services\WithdrawalService;
use Illuminate\Http\JsonResponse;

class WithdrawalController extends Controller
{
    protected $service;

    public function __construct(WithdrawalService $service)
    {
        $this->service = $service;
    }

    public function index(): JsonResponse
    {
        $data = $this->service->getAllForUser();
        return Responder::success(WithdrawalRequestResource::collection($data));
    }

    public function store(StoreWithdrawalRequest $request): JsonResponse
    {
        $withdrawal = $this->service->create($request->validated());
        return Responder::success(
            new WithdrawalRequestResource($withdrawal),
            ['message' => 'تم إرسال طلب السحب بنجاح.']
        );
    }

    public function cancel($id): JsonResponse
    {
        $this->service->cancel($id);
        return Responder::success(null, ['message' => 'تم إلغاء طلب السحب بنجاح.']);
    }
}
