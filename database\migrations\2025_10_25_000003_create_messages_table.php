<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMessagesTable extends Migration {

  public function up() {
    Schema::create('messages', function (Blueprint $table) {
    $table->id();
   $table->foreignId('conversation_id')->constrained()->onDelete('cascade');
    $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
    $table->foreignId('receiver_id')->constrained('users')->onDelete('cascade');
    $table->text('message')->nullable();
    $table->enum('message_type', ['text', 'file', 'both'])->nullable();
    $table->boolean('is_read')->default(false);
    $table->timestamps();
});
  }

  public function down() {
    Schema::dropIfExists('messages');
  }
}
