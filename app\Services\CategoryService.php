<?php

namespace App\Services;

use App\Models\Category;
use Illuminate\Database\Eloquent\Collection;

class CategoryService
{
    public function __construct(protected Category $model) {}

    /**
     * Get all parent categories (no children)
     */
    public function getAllParentCategories(): Collection
    {
        return $this->model->whereNull('parent_id')
                         ->where('is_active', true)
                         ->get();
    }

    /**
     * Get category with its children
     */
    public function getCategoryWithChildren(int $id): ?Category
    {
        return $this->model->where('id', $id)
                         ->where('is_active', true)
                         ->with(['children' => function($query) {
                             $query->where('is_active', true);
                         }])
                         ->first();
    }
}