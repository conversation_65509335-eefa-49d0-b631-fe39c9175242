<?php

namespace App\Http\Requests\Api\Auth\Client;

use App\Http\Requests\Api\BaseApiRequest;
use Illuminate\Http\Request;

class UpdateProfileRequest extends BaseApiRequest {
  
  public function __construct(Request $request) {
    if (isset($request['phone'])) {
      $request['phone'] = fixPhone($request['phone']);
    }
    if (isset($request['country_code'])) {
      $request['country_code'] = fixPhone($request['country_code']);
    }
  }

  public function rules() {
    return [
      'name'         => 'sometimes|required|min:3|max:30',
       'phone'        => [
                'sometimes',
                'required',
                'phone:SA',
                'unique:users,phone,NULL,id,deleted_at,NULL'
            ],
      'email'        => 'sometimes|required|email|max:50|unique:users,email,' . auth()->id() . ',id,deleted_at,NULL',
      'image'        => 'sometimes|nullable|image',
      'city_id'      => 'sometimes|required|exists:cities,id',
      'region_id'      => 'sometimes|required|exists:regions,id',
      'gender' => 'sometimes|nullable|in:male,female',
    'password'     => 'sometimes|nullable|min:6|max:100|confirmed',
    ];
  }
}