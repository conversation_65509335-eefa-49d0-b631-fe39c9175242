<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\StorePurchaseRequest;
use App\Http\Requests\UpdatePurchaseRequest;
use App\Http\Resources\Api\PurchaseRequestResource;
use App\Services\PurchaseRequestService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PurchaseRequestController extends Controller
{
    protected $service;

    public function __construct(PurchaseRequestService $service)
    {
        $this->service = $service;
    }

    public function getAsBuyer(Request $request): JsonResponse
    {
        $filters = $request->only(['sort_by']);
        $data = $this->service->getAsBuyer(auth()->id(), $filters);
        return Responder::success(PurchaseRequestResource::collection($data));
    }

    public function getAsSeller(): JsonResponse
    {
        $data = $this->service->getAsSeller();
        return Responder::success(PurchaseRequestResource::collection($data));
    }

    public function show($id): JsonResponse
    {
        $data = $this->service->getById($id);

        if (!$data) {
            return Responder::error(__('apis.not_found'), 404);
        }

        return Responder::success(new PurchaseRequestResource($data));
    }

  public function store(StorePurchaseRequest $request): JsonResponse
{
    $authUserId = auth()->id(); 

    $advertisement = \App\Models\Advertisement::find($request->advertisement_id);

    if (!$advertisement) {
        return Responder::error(__('apis.not_found'), 404);
    }

    if ($advertisement->status === 'sold') {
        return Responder::error(__('apis.cannot_purchase_sold_ad'), 400);
    }

    $data = $this->service->create($request->validated());

    $buyerId = $data->buyer_id;
    $sellerId = $data->seller_id;

    $conversation = \App\Models\Conversation::where('advertisement_id', $data->advertisement_id)->first();

    if (!$conversation) {
        $conversation = \App\Models\Conversation::create([
            'advertisement_id' => $data->advertisement_id,
        ]);
    }

    $senderId   = $authUserId == $buyerId ? $buyerId : $sellerId;
    $receiverId = $authUserId == $buyerId ? $sellerId : $buyerId;

    \App\Models\Message::create([
        'conversation_id' => $conversation->id,
        'sender_id'       => $senderId,
        'receiver_id'     => $receiverId,
        'message'         => 'A new purchase request has been submitted.',
        'message_type'    => 'text',
        'is_read'         => false,
    ]);

    return Responder::success(new PurchaseRequestResource($data), [
        'message' => __('apis.created_successfully')
    ]);
}


    public function update(UpdatePurchaseRequest $request, $id): JsonResponse
    {
        $data = $this->service->update($id, $request->validated());

        return Responder::success(new PurchaseRequestResource($data), [
            'message' => __('apis.updated_successfully')
        ]);
    }

    public function destroy($id): JsonResponse
    {
        $this->service->delete($id);

        return Responder::success(null, [
            'message' => __('apis.deleted_successfully')
        ]);
    }

    public function sellerConfirm($id): JsonResponse
    {
        $this->service->confirmBySeller($id);

        $purchase = $this->service->getById($id);

        return Responder::success(new PurchaseRequestResource($purchase), [
            'message' => __('apis.updated_successfully')
        ]);
    }

    public function buyerConfirm($id): JsonResponse
    {
        $this->service->confirmByBuyer($id);

        $purchase = $this->service->getById($id);

        return Responder::success(new PurchaseRequestResource($purchase), [
            'message' => __('apis.updated_successfully')
        ]);
    }

public function reportProblem(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        $response = $this->service->reportProblem($id, $request->reason);

        return $response;
    }


    public function cancelByAdmin($id): JsonResponse
    {
        if (!auth()->user()?->is_admin) {
            return Responder::error(__('apis.unauthorized'), 403);
        }

        $this->service->cancelByAdmin($id);

        return Responder::success(null, [
            'message' => __('apis.updated_successfully')
        ]);
    }
}
