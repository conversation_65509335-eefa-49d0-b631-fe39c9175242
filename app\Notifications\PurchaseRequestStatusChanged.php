<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class PurchaseRequestStatusChanged extends Notification
{
    use Queueable;

    protected string $newStatus;
    protected ?string $oldStatus;
    protected int $purchaseRequestId;
    protected ?int $advertisementId;

    public function __construct(string $newStatus, ?string $oldStatus, int $purchaseRequestId, ?int $advertisementId = null)
    {
        $this->newStatus = $newStatus;
        $this->oldStatus = $oldStatus;
        $this->purchaseRequestId = $purchaseRequestId;
        $this->advertisementId = $advertisementId;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        $newStatusText = __('apis.purchase_status.' . $this->newStatus);
        $oldStatusText = $this->oldStatus ? __('apis.purchase_status.' . $this->oldStatus) : null;

        if (is_null($this->oldStatus)) {
            // إشعار الإنشاء
            $title = 'تم إنشاء طلب شراء';
            $body = "تم إنشاء طلب شراء للإعلان رقم {$this->advertisementId}، الطلب رقم {$this->purchaseRequestId}، الحالة الحالية: \"{$newStatusText}\"";
        } else {
            // إشعار التحديث
            $title = 'تم تغيير حالة الطلب';
            $body = "تم تغيير الطلب رقم {$this->purchaseRequestId} من الحالة \"{$oldStatusText}\" إلى الحالة \"{$newStatusText}\"";
        }

        return [
            'type' => 'purchase_request_status_changed',
            'title' => $title,
            'body' => $body,
            'purchase_request_id' => $this->purchaseRequestId,
            'advertisement_id' => $this->advertisementId,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
        ];
    }
}
