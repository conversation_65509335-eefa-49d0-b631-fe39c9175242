<?php

namespace App\Repositories;

use App\Models\TrainerApplication;
use Illuminate\Database\Eloquent\Collection;

class TrainerApplicationRepository
{
    protected TrainerApplication $model;

    public function __construct(TrainerApplication $model)
    {
        $this->model = $model;
    }

public function getByUserId(int $userId, ?int $cityId = null): Collection
{
    return TrainerApplication::query()
        ->where('user_id', $userId)
        ->when($cityId, fn($q) => $q->where('city_id', $cityId))
        ->latest()
        ->get();
}

    

    public function findById($id): ?TrainerApplication
    {
        return $this->model->with(['user', 'city', 'region', 'media'])->find($id);
    }

    public function create(array $data): TrainerApplication
    {
        return $this->model->create($data);
    }

    public function update(TrainerApplication $application, array $data): TrainerApplication
    {
        $application->update($data);
        return $application;
    }

    public function delete(TrainerApplication $application)
    {
        return $application->delete();
    }
}