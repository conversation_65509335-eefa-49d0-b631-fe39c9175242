<div class="position-relative">
    {{-- table content --}}
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>#</th>
                <th>{{ __('admin.advertisement') }}</th>
                <th>{{ __('admin.buyer') }}</th>
                <th>{{ __('admin.seller') }}</th>
                {{-- <th>{{ __('admin.status') }}</th> --}}
                <th>{{ __('admin.amount_paid') }}</th>
                {{-- <th>{{ __('admin.wallet_credit_used') }}</th> --}}
                {{-- <th>{{ __('admin.payment_method') }}</th> --}}
                {{-- <th>{{ __('admin.payment_status') }}</th> --}}
                <th>{{ __('admin.control') }}</th>
            </tr>
        </thead>
        <tbody>
            @foreach($rows as $row)
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="{{ $row->id }}">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $row->id }}</td>
                    <td>{{ optional($row->advertisement)->name ?? '-' }}</td>
                    <td>{{ optional($row->buyer)->name ?? '-' }}</td>
                    <td>{{ optional($row->seller)->name ?? '-' }}</td>
                    {{-- <td>
                        @if($row->status == 'pending')
                            <span class="badge badge-warning">{{ __('admin.pending') }}</span>
                        @elseif($row->status == 'approved')
                            <span class="badge badge-success">{{ __('admin.approved') }}</span>
                        @elseif($row->status == 'rejected')
                            <span class="badge badge-danger">{{ __('admin.rejected') }}</span>
                        @endif
                    </td> --}}
                    <td>{{ $row->amount_paid }}</td>
                    {{-- <td>{{ $row->wallet_credit_used }}</td>
                    <td>{{ $row->payment_method }}</td>
                    <td>{{ $row->payment_status }}</td> --}}
                    <td class="product-action">
                        <a href="{{ route('admin.purchares.show', ['id' => $row->id]) }}" class="btn btn-warning btn-sm">
                            <i class="feather icon-eye"></i> {{ __('admin.show') }}
                        </a>
                        {{-- <a href="{{ route('admin.purchares.edit', ['id' => $row->id]) }}" class="btn btn-primary btn-sm">
                            <i class="feather icon-edit"></i> {{ __('admin.edit') }}
                        </a>
                        <span class="delete-row btn btn-danger btn-sm" data-url="{{ url('admin/purchares/' . $row->id) }}">
                            <i class="feather icon-trash"></i> {{ __('admin.delete') }}
                        </span> --}}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}

    {{-- no data found --}}
    @if ($rows->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{ asset('admin/app-assets/images/pages/404.png') }}" alt="">
            <span class="mt-2" style="font-family: cairo">{{ __('admin.there_are_no_matches_matching') }}</span>
        </div>
    @endif
</div>

{{-- pagination --}}
@if ($rows->count() > 0 && $rows instanceof \Illuminate\Pagination\AbstractPaginator)
    <div class="d-flex justify-content-center mt-3">
        {{ $rows->links() }}
    </div>
@endif
