<?php

namespace App\Http\Controllers\Api;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\TrainerApplicationRequest;
use App\Http\Requests\TrainerApplicationUpdateRequest;
use App\Http\Resources\Api\Client\TrainerApplicationIndexResource;
use App\Http\Resources\Api\Client\TrainerApplicationResource;
use App\Models\TrainerApplication;
use App\Services\TrainerApplicationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TrainerApplicationController extends Controller
{
    protected TrainerApplicationService $service;

    public function __construct(TrainerApplicationService $service)
    {
        $this->service = $service;
    }

  public function index(Request $request): JsonResponse
{
    $userId = auth()->id();
    $cityId = $request->input('city_id');
    $sort = $request->input('sort', 'latest'); 
    $search = $request->input('name');
    

    $applications = $this->service->getUserApplications($userId, $cityId, $sort, $search);

    return Responder::success(TrainerApplicationIndexResource::collection($applications));
}

public function store(TrainerApplicationRequest $request): JsonResponse
{
    $userId = auth()->id();

    $existingApplication = TrainerApplication::where('user_id', $userId)->first();

    if ($existingApplication) {
        return Responder::error(__('apis.you_have_already_applied'), 400);
    }

    $validated = $request->validated();

    $application = $this->service->createApplication(
        $validated,
        $userId,
        $request->file('personal_image'),
        $request->file('certificate_image'),
        $request->file('works', [])
    );

    return Responder::success(null, ['message' => __('apis.created_successfully')]);
}


    public function show($id): JsonResponse
    {
        $application = $this->service->getApplicationById($id);

        if (!$application || $application->user_id !== auth()->id()) {
            return Responder::error(__('apis.not_found'), 404);
        }

        return Responder::success(new TrainerApplicationResource($application));
    }

    public function update(TrainerApplicationUpdateRequest $request, $id): JsonResponse
    {
        $result = $this->service->updateApplication(
            $id,
            $request->validated(),
            $request->file('personal_image'),
            $request->file('certificate_image'),
            $request->file('works', [])
        );

        if ($result instanceof JsonResponse) {
            return $result;
        }

        return Responder::success(new TrainerApplicationResource($result), ['message' => __('apis.updated_successfully')]);
    }

    public function destroy($id): JsonResponse
    {
        return $this->service->deleteApplication($id);
    }
}
