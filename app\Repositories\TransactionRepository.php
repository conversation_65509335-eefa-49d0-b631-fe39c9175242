<?php

namespace App\Repositories;

use App\Models\Transaction;

class TransactionRepository
{
    public function __construct(protected Transaction $transaction) {}

    public function getTransactions($type, $limit = null, $date = null)
    {
        $query = $this->transaction->where(['user_id' => auth()->id()])->latest();

        if ($date) {
            $query->whereDate('created_at', $date);
        }

        return $limit ? $query->limit($limit)->get() : $query->paginate(20);
    }

    public function list($type, $request)
    {
        $date = isset($request['date']) ? $request['date'] : null;

        return $this->getTransactions($type, null, $date);
    }

    public function walletReport($type)
    {
        return $this->getTransactions($type, 20);
    }

    public function create($data)
    {
        return $this->transaction->create($data);
    }

    public function getBalance()
    {
        return auth()->user()->wallet?->balance ?? 0;
    }

    public function totalTransactions(?int $month = null, ?string $day = null): float
    {
        $query = $query = $this->transaction->where('amount', '>=', 0)
            ->when($month, fn ($q) => $q->whereMonth('created_at', $month))
            ->when($day, fn ($q) => $q->whereDay('created_at', $day));

        return $query->sum('amount');
    }
}
