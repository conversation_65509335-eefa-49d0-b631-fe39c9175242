<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Services\AdService;
use Illuminate\Http\Request;
use App\Http\Requests\AdRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\Controller;
use App\Http\Requests\AdUpdateRequest;
use App\Http\Resources\Client\AdSliderResource;

class AdController extends Controller
{
    protected AdService $service;

    public function __construct(AdService $service)
    {
        $this->service = $service;
    }

public function myAds(Request $request): JsonResponse
{
    $userId = auth()->id();

    $filters = $request->only([
        'main_category_id',
        'sub_category_id',
        'min_price',
        'max_price',
        'gender_target',
        'city_id',
        'sort_by',
        'status',
        'name', 
    ]);

    $ads = $this->service->getUserAds($userId, $filters);

    return Responder::success(AdSliderResource::collection($ads));
}


public function otherAds(Request $request): JsonResponse
{
    $userId = auth()->id();

    $filters = $request->only([
        'main_category_id',
        'sub_category_id',
        'min_price',
        'max_price',
        'gender_target',
        'city_id',
        'sort_by',
        'status',
        'name', 
    ]);

    $ads = $this->service->getOtherUsersAds($userId, $filters);

    return Responder::success(AdSliderResource::collection($ads));
}




    public function store(AdRequest $request): JsonResponse
    {
        $userId = auth()->id();
        $validated = app(AdRequest::class)->validated();
        $ad = $this->service->createAd($validated, $userId);

        if ($request->hasFile('main_image')) {
            $ad->addMedia($request->file('main_image'))
                ->toMediaCollection('ads_main');
        }

     if (!empty($validated['images']) && is_array($validated['images'])) {
    foreach ($validated['images'] as $image) {
        if ($image instanceof UploadedFile) {
            $ad->addMedia($image)->toMediaCollection('ads');
        }
    }
}


        return Responder::success(null, ['message' => __('apis.created_successfully')]);
    }

public function show($id): JsonResponse
{
    $userId = auth()->id();
    $ad = $this->service->getAdById($id);

    if (!$ad || $ad->user_id !== $userId) {
        return Responder::error(__('apis.not_found'), 404);
    }

    return Responder::success(new AdSliderResource($ad));
}


public function update(AdUpdateRequest $request, $id): JsonResponse
{
    $userId = auth()->id();
    $validated = $request->validated();

    $ad = $this->service->updateAd($id, $validated);

    if (!$ad) {
        return Responder::error(__('apis.not_found'), 404);
    }

    if ($request->hasFile('main_image')) {
        $ad->clearMediaCollection('ads_main'); 
        $ad->addMedia($request->file('main_image'))
            ->toMediaCollection('ads_main');
    }

    if (!empty($validated['images']) && is_array($validated['images'])) {
        foreach ($validated['images'] as $image) {
            if ($image instanceof UploadedFile) {
                $ad->addMedia($image)->toMediaCollection('ads');
            }
        }
    }

    return Responder::success(new AdSliderResource($ad), ['message' => __('apis.updated_successfully')]);
}

// public function update(AdUpdateRequest $request, $id): JsonResponse
// {
//     $result = $this->service->updateAd($id, $request->validated(), $request->file('image'));

//     if ($result instanceof JsonResponse) {
//         return $result;
//     }

//     return Responder::success(new AdSliderResource($result), ['message' => __('apis.updated_successfully')]);
// }

 public function destroy($id): JsonResponse
    {
        return $this->service->deleteAd($id);
    }


}