<?php

namespace Database\Factories;

use App\Models\Advertisement;
use App\Models\User;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class AdvertisementFactory extends Factory
{
    protected $model = Advertisement::class;

    public function definition(): array
    {
        $mainCategory = Category::whereNull('parent_id')->inRandomOrder()->first();
        $subCategory  = Category::where('parent_id', $mainCategory?->id)->inRandomOrder()->first();

        return [
            'user_id' => User::inRandomOrder()->value('id') ?? 1,
            'name' => [
                'en' => $this->faker->words(3, true),
                'ar' => 'إعلان ' . $this->faker->randomNumber(),
            ],
            'description' => [
                'en' => $this->faker->sentence,
                'ar' => 'وصف الإعلان ' . $this->faker->randomNumber(),
            ],
            'price' => $this->faker->randomFloat(2, 100, 1000),
            'main_category_id' => $mainCategory?->id,
            'sub_category_id' => $subCategory?->id ?? $mainCategory?->id,
            'gender_target' => $this->faker->randomElement(['male', 'female', 'both']),
            'region' => $this->faker->state,
            'city' => $this->faker->city,
            'status' => $this->faker->randomElement(['under_review', 'active', 'rejected']),
            'whatsapp_contact' => '01' . $this->faker->randomNumber(9, true),
            'is_main' => $this->faker->boolean,
        ];
    }
}
