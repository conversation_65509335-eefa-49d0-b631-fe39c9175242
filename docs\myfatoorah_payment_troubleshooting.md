# MyFatoorah Payment Troubleshooting Guide

## Issue: "DECLINED : Do not honour" Error

This error indicates that the payment is being declined by the payment gateway. Here's a comprehensive troubleshooting guide.

## Quick Fixes Applied

### 1. **Enhanced Data Validation**
- ✅ **Phone Number Validation**: Improved phone number cleaning and validation
- ✅ **Amount Validation**: Ensures amount is properly formatted as float
- ✅ **Required Fields**: Added validation for all required fields
- ✅ **Fallback Values**: Added fallback values for missing data

### 2. **Improved Error Handling**
- ✅ **Detailed Logging**: Added comprehensive logging for debugging
- ✅ **Data Validation**: Pre-validates all data before sending to MyFatoorah
- ✅ **Error Messages**: Better error messages for troubleshooting

### 3. **Debug Tools Added**
- ✅ **Debug Service**: Created comprehensive debugging service
- ✅ **Debug Endpoint**: Added `/api/client/courses/payment/debug` endpoint
- ✅ **Recommendations**: Automatic recommendations based on debug results

## Common Causes & Solutions

### **1. Invalid Phone Number**
**Issue**: Phone number format not accepted by MyFatoorah
**Solution**: 
```php
// Phone must be 9 digits starting with 5 (Saudi format)
// Examples: *********, 501234567
```

**Test**: Use debug endpoint to check phone validation:
```bash
GET /api/client/courses/payment/debug?enrollment_id=16
```

### **2. Test Mode Configuration**
**Issue**: Using production cards in test mode or vice versa
**Solution**:
```env
# For testing
MYFATOORAH_TEST_MODE=true

# Use test card numbers in test mode:
# Visa: ****************
# Mastercard: ****************
# Mada: ****************
```

### **3. API Key Issues**
**Issue**: Invalid or expired API key
**Solution**:
```env
# Ensure you have the correct API key
MYFATOORAH_API_KEY=your_actual_api_key_here

# Test mode and live mode have different API keys
```

### **4. Currency/Amount Issues**
**Issue**: Invalid amount or currency format
**Solution**:
```php
// Amount must be positive number
// Currency must match your MyFatoorah account settings
MYFATOORAH_CURRENCY=SAR
```

### **5. Missing Required Fields**
**Issue**: Required fields missing or empty
**Solution**: All these fields are now validated:
- CustomerName
- CustomerMobile (valid Saudi phone)
- InvoiceValue (> 0)
- DisplayCurrencyIso
- CallBackUrl
- ErrorUrl

## Debug Process

### **Step 1: Use Debug Endpoint**
```bash
GET /api/client/courses/payment/debug?enrollment_id=16

# This will check:
# - Configuration validity
# - User data completeness
# - Phone number format
# - Amount validation
# - MyFatoorah connection
# - Available payment gateways
```

### **Step 2: Check Debug Response**
```json
{
  "status": true,
  "data": {
    "debug_info": {
      "config": {
        "api_key_set": true,
        "test_mode": true,
        "currency": "SAR"
      },
      "user_data": {
        "phone_valid": false,  // ❌ Issue found
        "phone_original": "**********",
        "phone_cleaned": "*********"
      },
      "myfatoorah_connection": {
        "connection_success": true
      }
    },
    "recommendations": [
      "User needs a valid Saudi phone number (9 digits starting with 5)"
    ]
  }
}
```

### **Step 3: Fix Issues Based on Recommendations**

## Test Card Numbers (Test Mode Only)

### **Successful Test Cards**
```
Visa: ****************
Mastercard: ****************
Mada: ****************
American Express: ***************
```

### **CVV**: Any 3-4 digits
### **Expiry**: Any future date
### **Name**: Any name

## Environment Configuration Check

### **Required .env Variables**
```env
# MyFatoorah Configuration
MYFATOORAH_API_KEY=your_test_api_key_here
MYFATOORAH_TEST_MODE=true
MYFATOORAH_CURRENCY=SAR
MYFATOORAH_COUNTRY_CODE=+966
MYFATOORAH_LANGUAGE=ar
MYFATOORAH_NOTIFICATION_OPTION=Lnk

# Webhook (optional for testing)
MYFATOORAH_WEBHOOK_SECRET=your_webhook_secret

# Logging
MYFATOORAH_LOG_ENABLED=true
```

## Testing Steps

### **1. Test User Data**
```sql
-- Check user has valid phone number
SELECT id, name, email, phone 
FROM users 
WHERE id = 1;

-- Update user phone if needed
UPDATE users 
SET phone = '*********' 
WHERE id = 1;
```

### **2. Test Enrollment**
```bash
# Create new enrollment
POST /api/client/courses/1/enroll
{
    "payment_method": "mada",
    "gateway": "myfatoorah"
}
```

### **3. Debug Payment**
```bash
# Debug the enrollment
GET /api/client/courses/payment/debug?enrollment_id=16
```

### **4. Test Payment with Valid Data**
Use the debug recommendations to fix any issues, then retry the payment.

## Common Error Messages & Solutions

### **"DECLINED : Do not honour"**
- ❌ **Invalid phone number format**
- ❌ **Using real card in test mode**
- ❌ **Invalid amount (0 or negative)**
- ❌ **Missing required fields**

### **"Invalid API Key"**
- ❌ **Wrong API key in .env**
- ❌ **Test/Live mode mismatch**

### **"Invalid Currency"**
- ❌ **Currency not supported by your account**
- ❌ **Wrong currency code**

## Monitoring & Logs

### **Check Laravel Logs**
```bash
tail -f storage/logs/laravel.log | grep MyFatoorah
```

### **Check MyFatoorah Logs**
```bash
tail -f storage/logs/myfatoorah.log
```

## Next Steps

1. **Run Debug Endpoint**: Check enrollment ID 16 with debug endpoint
2. **Fix Phone Number**: Ensure user has valid Saudi phone number
3. **Verify Test Mode**: Confirm using test cards in test mode
4. **Check API Key**: Verify correct test API key is set
5. **Test Payment**: Retry payment after fixes

## Support

If issues persist after following this guide:

1. **Check MyFatoorah Dashboard**: Look for transaction logs
2. **Contact MyFatoorah Support**: Provide transaction reference
3. **Review API Documentation**: https://myfatoorah.readme.io/docs

The enhanced debugging tools should help identify the exact cause of the payment failures! 🔍
