<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rating extends Model
{
    use HasFactory;

        protected $table = 'rates';
    protected $fillable = [
        'rater_id',
        'client_id',
        'trainer_id',
        'purchase_request_id',
        'stars',
        'comment',
        'status'
    ];

    // Relationships
    public function rater()
    {
        return $this->belongsTo(User::class, 'rater_id');
    }

    
    public function user()
{
    return $this->belongsTo(\App\Models\User::class, 'rater_id');
}

    public function ratee()
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    public function purchaseRequest()
    {
        return $this->belongsTo(PurchaseRequest::class);
    }
}