<?php

namespace App\Http\Controllers\Api;
use App\Http\Resources\Api\VideoResource;
use App\Models\Fqs;
use App\Models\City;
use App\Models\Image;
use App\Models\Intro;
use App\Models\Region;
use App\Models\Social;
use App\Models\Country;
use App\Models\Category;
use App\Facades\Responder;
use App\Models\SiteSetting;
use App\Models\PaymentMethod;
use App\Models\DeliveryPeriod;
use App\Models\OrderStatusEnum;
use App\Models\ProductCategory;
use App\Services\CouponService;
use App\Services\SettingService;
use App\Models\PaymentMethodEnum;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Settings\FqsResource;
use App\Http\Resources\Api\Settings\CityResource;
use App\Http\Resources\Api\Settings\ImageResource;
use App\Http\Resources\Api\Settings\IntroResource;
use App\Http\Resources\Api\Settings\RegionResource;
use App\Http\Resources\Api\Settings\SocialResource;
use App\Http\Requests\Api\Coupon\checkCouponRequest;
use App\Http\Resources\Api\Settings\CountryResource;
use App\Http\Resources\Api\Settings\CategoryResource;
use App\Http\Resources\Api\Settings\OrderStatusResource;
use App\Http\Resources\Api\Settings\PaymentMethodResource;
use App\Http\Resources\Api\Settings\DeliveryPeriodResource;
use App\Http\Resources\Api\Settings\RegionWithCitiesResource;
use App\Http\Resources\Api\Settings\CountryWithCitiesResource;
use App\Http\Resources\Api\Settings\CountryWithRegionsResource;
use App\Models\ShortVideo;

class SettingController extends Controller {

  public function settings() {
    $data = SettingService::appInformations(SiteSetting::pluck('value', 'key'));
    return Responder::success($data);
  }

  public function about() {
    $about = SiteSetting::where(['key' => 'about_' . lang()])->first()->value;
    return Responder::success($about);
  }

  public function terms() {
    $terms = SiteSetting::where(['key' => 'terms_' . lang()])->first()->value;
    return Responder::success($terms);
  }

  public function privacy() {
    $privacy = SiteSetting::where(['key' => 'privacy_' . lang()])->first()->value;
    return Responder::success($privacy);
  }

  public function intros() {
    $intros = IntroResource::collection(Intro::latest()->get());
    return Responder::success($intros);
  }

  public function fqss() {
    $fqss = FqsResource::collection(Fqs::latest()->get());
    return Responder::success($fqss);
  }

  public function socials() {
    $contacts = SiteSetting::whereIn('key', ['email', 'phone', 'whatsapp'])->get(['id' , 'key', 'value']);
    $socials = SocialResource::collection(Social::latest()->get());
    return Responder::success(['contacts' => $contacts, 'socials' => $socials]);
  }

public function images()
{
    $images = Image::latest()->paginate(15);
    return Responder::paginated(ImageResource::collection($images));
}

  public function categories($id = null) {
    $categories = CategoryResource::collection(Category::where('is_active',1)->latest()->get());
    return Responder::success($categories);
  }

  public function ProductCategories($id = null) {
    $categories = CategoryResource::collection(ProductCategory::where('is_active',1)->latest()->get());
    return Responder::success($categories);
  }


  public function countries() {
    $countries = CountryResource::collection(Country::latest()->get());
    return Responder::success($countries);
  }

  public function cities() {
    $cities = CityResource::collection(City::latest()->get());
    return Responder::success($cities);
  }

  public function regions() {
    $regions = RegionResource::collection(Region::latest()->get());
    return Responder::success($regions);
  }

  public function regionCities($region_id) {
    $cities = CityResource::collection(City::where('region_id', $region_id)->latest()->get());
    return Responder::success($cities);
  }

  public function regionsWithCities() {
    $regions = RegionWithCitiesResource::collection(Region::with('cities')->latest()->get());
    return Responder::success($regions);
  }

  public function CountryCities($country_id) {
    $cities = CityResource::collection(City::where('country_id', $country_id)->latest()->get());
    return Responder::success($cities);
  }

  public function CountryRegions($country_id) {
    $regions = RegionResource::collection(Region::where('country_id', $country_id)->latest()->get());
    return Responder::success($regions);
  }

  public function countriesWithCities() {
    $countries = CountryWithCitiesResource::collection(Country::with('cities')->latest()->get());
    return Responder::success($countries);
  }

  public function countriesWithRegions() {
    $countries = CountryWithRegionsResource::collection(Country::with('regions')->latest()->get());
    return Responder::success($countries);
  }

  public function checkCoupon(checkCouponRequest $request)
  {
    $checkCouponRes = CouponService::checkCoupon( $request->coupon_num , $request->total_price) ;
    if ($checkCouponRes['key'] === 'success') {
      return Responder::success($checkCouponRes['data'] ?? null, ['message' => $checkCouponRes['msg']]);
    } else {
      return Responder::error($checkCouponRes['msg'], [], 400);
    }
  }
  public function isProduction()
  {
    $isProduction = SiteSetting::where(['key' => 'is_production'])->first()->value;
    return Responder::success($isProduction);
  }

  public function isRegister()
  {
    $isProduction = SiteSetting::where(['key' => 'registeration_availability'])->first()->value;
    return Responder::success($isProduction);
  }

  public function VatAmount()
  {
    $isProduction = SiteSetting::where(['key' => 'vat_amount'])->first()->value;
    return Responder::success($isProduction);
  }



  /**
   * Get all payment methods with their integer values and text values
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function paymentMethods()
  {
    $paymentMethods = PaymentMethodResource::collection(PaymentMethod::all());
    return Responder::success($paymentMethods);
  }

  /**
   * Get all order statuses with their integer values and text values
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function orderStatuses()
  {
    $orderStatuses = OrderStatusResource::collection(OrderStatusEnum::all());
    return Responder::success($orderStatuses);
  }

  /**
   * Get all delivery periods
   *
   * @return \Illuminate\Http\JsonResponse
   */
  public function deliveryPeriods()
  {
    $deliveryPeriods = DeliveryPeriodResource::collection(DeliveryPeriod::all());
    return Responder::success($deliveryPeriods);
  }


  public function videos()
  {
    $videos = VideoResource::collection(ShortVideo::where('is_active' , true)->get());
    return Responder::success($videos);
  }



}
