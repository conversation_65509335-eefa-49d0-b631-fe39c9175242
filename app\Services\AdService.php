<?php

namespace App\Services;

use App\Models\Advertisement;
use App\Repositories\AdRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class AdService
{
    protected AdRepository $repository;

    public function __construct(AdRepository $repository)
    {
        $this->repository = $repository;
    }

 public function getUserAds($userId, $filters = [])
{
    return $this->repository->getUserAds($userId, $filters);
}





public function getOtherUsersAds($userId, $filters = [])
{
    return Advertisement::query()
        ->where('user_id', '!=', $userId)

        ->when(isset($filters['status']), function ($q) use ($filters) {
            if (is_array($filters['status'])) {
                $q->whereIn('status', $filters['status']);
            } else {
                $q->where('status', $filters['status']);
            }
        })

        ->when(isset($filters['name']), function ($q) use ($filters) {
            $q->where('name', 'like', '%' . $filters['name'] . '%');
            // لو الاسم translatable:
            // $q->where('name->' . app()->getLocale(), 'like', '%' . $filters['name'] . '%');
        })

        ->when(isset($filters['main_category_id']), fn($q) =>
            $q->where('main_category_id', $filters['main_category_id']))

        ->when(isset($filters['sub_category_id']), fn($q) =>
            $q->where('sub_category_id', $filters['sub_category_id']))

        ->when(isset($filters['min_price']), fn($q) =>
            $q->where('price', '>=', $filters['min_price']))

        ->when(isset($filters['max_price']), fn($q) =>
            $q->where('price', '<=', $filters['max_price']))

        ->when(isset($filters['gender_target']), fn($q) =>
            $q->where('gender_target', $filters['gender_target']))

        ->when(isset($filters['city_id']), fn($q) =>
            $q->where('city_id', $filters['city_id']))

        ->when(isset($filters['sort_by']), function ($q) use ($filters) {
            switch ($filters['sort_by']) {
                case 'oldest':
                    $q->orderBy('created_at', 'asc');
                    break;
                case 'lowest_price':
                    $q->orderBy('price', 'asc');
                    break;
                case 'highest_price':
                    $q->orderBy('price', 'desc');
                    break;
                default:
                    $q->orderBy('created_at', 'desc');
            }
        }, fn($q) => $q->orderBy('created_at', 'desc'))

        ->withCount('ratings')
        ->withAvg('ratings', 'stars')
        ->get();
}



    

    public function getAdById($id)
    {
        return $this->repository->findById($id);
    }

    public function createAd(array $data, $userId, $image = null)
    {
        $data['user_id'] = $userId;

        $ad = $this->repository->create($data);
        
        if ($image) {
            $ad->addMedia($image)->toMediaCollection('ads');
        }

        return $ad;
    }

public function updateAd($id, array $data)
{
    $ad = $this->repository->findById($id);

    if (!$ad || $ad->user_id !== auth()->id()) {
     return Responder::error(null, ['message' => __('apis.not_authorized_to_update_ad')], 403);
    }

    $this->repository->update($ad, $data);

  

    return $ad;
}



  public function deleteAd($id)
    {
        $ad = $this->repository->findById($id);

        if (!$ad || $ad->user_id !== Auth::id()) {
            return response()->json([
                'status' => false,
                'message' => __('apis.not_authorized_to_delete_ad')
            ], 403);
        }

        $this->repository->delete($ad);

        return response()->json([
            'status' => true,
            'message' => __('apis.deleted_successfully')
        ]);
    }

    
}