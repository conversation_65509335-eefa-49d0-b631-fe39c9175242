<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AccountDeletionRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Account deletion requests from users
        
        DB::table('account_deletion_requests')->insert([
            [
                'user_id' => 5,
                'reason' => 'I no longer use beauty services and want to delete my account for privacy reasons.',
                'status' => 'pending',
                'admin_notes' => null,
                'processed_by' => null,
                'processed_at' => null,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => 6,
                'reason' => 'Moving to another country and will not need this service anymore.',
                'status' => 'approved',
                'admin_notes' => 'Account deletion approved. User data will be anonymized.',
                'processed_by' => 1,
                'processed_at' => now()->subDays(1),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => 7,
                'reason' => 'Not satisfied with the service quality.',
                'status' => 'rejected',
                'admin_notes' => 'Deletion request rejected. Customer service will contact user to resolve issues.',
                'processed_by' => 1,
                'processed_at' => now()->subHours(12),
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subHours(12),
            ],
            [
                'user_id' => 8,
                'reason' => 'Privacy concerns about data handling.',
                'status' => 'pending',
                'admin_notes' => null,
                'processed_by' => null,
                'processed_at' => null,
                'created_at' => now()->subHours(6),
                'updated_at' => now()->subHours(6),
            ],
            [
                'user_id' => 9,
                'reason' => 'Created duplicate account by mistake.',
                'status' => 'approved',
                'admin_notes' => 'Duplicate account confirmed. Deletion approved.',
                'processed_by' => 1,
                'processed_at' => now()->subHours(2),
                'created_at' => now()->subHours(8),
                'updated_at' => now()->subHours(2),
            ],
        ]);

        $this->command->info('Account deletion requests seeded successfully!');
    }
}
