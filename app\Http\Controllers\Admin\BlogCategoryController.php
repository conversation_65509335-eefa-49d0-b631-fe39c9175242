<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\blogcategories\Store;
use App\Http\Requests\Admin\blogcategories\Update;
use App\Models\BlogCategory ;
use App\Traits\Report;


class BlogCategoryController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {
            $blogcategories = BlogCategory::search(request()->searchArray)->paginate(30);
            $html = view('admin.blogcategories.table' ,compact('blogcategories'))->render() ;
            return response()->json(['html' => $html]);
        }
        return view('admin.blogcategories.index');
    }

    public function create()
    {
        return view('admin.blogcategories.create');
    }


    public function store(Store $request)
    {
        $data = $request->validated();
        $data['is_active'] = (int) $request->input('is_active', 0);

        BlogCategory::create($data);
        Report::addToLog('اضافه تصنيف-المدونه');
        return response()->json(['url' => route('admin.blogcategories.index')]);
    }
    public function edit($id)
    {
        
        $blogcategory = BlogCategory::findOrFail($id);
        return view('admin.blogcategories.edit' , ['blogcategory' => $blogcategory]);
    }

    public function update(Update $request, $id)
    {
        $data = $request->validated();

        $blogcategory = BlogCategory::findOrFail($id);
        $blogcategory->update($data);
        Report::addToLog('تعديل تصنيف-المدونه');
        return response()->json(['url' => route('admin.blogcategories.index')]);
    }

    public function show($id)
    {
        $blogcategory = BlogCategory::with('blogs')->findOrFail($id);
        return view('admin.blogcategories.show' , ['blogcategory' => $blogcategory]);
    }
    public function destroy($id)
    {
        $blogcategory = BlogCategory::findOrFail($id)->delete();
        Report::addToLog('  حذف تصنيف-المدونه') ;
        return response()->json(['id' =>$id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (BlogCategory::whereIntegerInRaw('id',$ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من تصنيفات-المدونه') ;
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }
}
