<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration {
  public function up() {
    Schema::create('users', function (Blueprint $table) {
      $table->id();
      $table->string('name');
      $table->string('email')->unique()->nullable();
      $table->string('country_code', 5)->default('966');
      $table->string('phone')->unique();
      $table->string('password', 100);
      $table->enum('gender', ['male', 'female'])->nullable();
    $table->enum('status', ['active', 'suspended', 'banned', 'deleted'])->default('active');
      $table->boolean('is_active')->default(false);
      $table->boolean('is_notify')->default(true);
      $table->string('code', 10)->nullable();
      $table->timestamp('code_expire')->nullable();
      $table->foreignId('region_id')->nullable()->constrained()->cascadeOnDelete();
      $table->foreignId('city_id')->nullable()->constrained()->cascadeOnDelete();
      $table->timestamp('created_at')->useCurrent();
      $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();
      $table->softDeletes();
    });
  }

  public function down() {
    Schema::dropIfExists('users');
  }
}
